import pandas as pd
import os
import json
import time
from datetime import datetime
import numpy as np
from tqdm import tqdm
import sys
import matplotlib.pyplot as plt
import seaborn as sns

# --- Matplotlib and Seaborn Setup ---
try:
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")
    import matplotlib.font_manager as fm
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    # Check for a common Chinese font
    if any('SimHei' in f for f in available_fonts):
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        print("🎨 使用中文字体: SimHei")
    else:
        print("🎨 未找到中文字体，将使用默认字体")
except Exception as e:
    print(f"🎨 字体设置失败: {e}")

def load_all_metrics(target_dir):
    all_metrics = []
    for filename in os.listdir(target_dir):
        if filename.endswith('_metrics.json'):
            filepath = os.path.join(target_dir, filename)
            with open(filepath, 'r') as f:
                metrics = json.load(f)
                all_metrics.append({**metrics, 'filepath': filepath})

    if not all_metrics:
        return pd.DataFrame()

    df = pd.DataFrame(all_metrics)
    
    # Extract model name from filename
    df['model'] = df['filepath'].apply(lambda x: x.split('_metrics.json')[0].split('/')[-1])
    
    return df

def create_and_save_boxplot(df, target_dir):
    """Generates and saves a boxplot of key metrics."""
    if df.empty or 'model' not in df.columns or 'AUC-PR' not in df.columns:
        return

    plot_file = os.path.join(target_dir, 'live_showdown_boxplot.png')
    
    metrics_to_plot = ['AUC-PR', 'AUC-ROC', 'F1', 'PA-F1']
    
    # Filter for metrics that are actually in the dataframe
    metrics_to_plot = [m for m in metrics_to_plot if m in df.columns]
    
    if not metrics_to_plot:
        print("   (No plottable metric columns found in data)")
        return

    num_metrics = len(metrics_to_plot)
    fig, axes = plt.subplots(1, num_metrics, figsize=(6 * num_metrics, 7), sharey=False)
    if num_metrics == 1:
        axes = [axes] # Make it iterable

    fig.suptitle(f'LERN vs. LTS_AD Performance Distribution ({len(df)//2} datasets processed)', fontsize=20, y=1.02)

    for i, metric in enumerate(metrics_to_plot):
        ax = axes[i]
        sns.boxplot(x='model', y=metric, data=df, ax=ax, palette="coolwarm", showfliers=False)
        sns.stripplot(x='model', y=metric, data=df, ax=ax, color=".25", size=4, jitter=True)
        ax.set_title(f'{metric} Distribution', fontsize=16)
        ax.set_xlabel('Model', fontsize=12)
        ax.set_ylabel('Score', fontsize=12)
        ax.tick_params(axis='x', rotation=10)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    
    try:
        plt.savefig(plot_file, dpi=150, bbox_inches='tight')
        plt.close(fig)
        # Use carriage return to keep the output on one line
        print(f"   📊 箱线图已更新: {plot_file}", end='\r')
    except Exception as e:
        print(f"   ❌ 保存箱线图失败: {e}")


def monitor_and_calculate_averages(target_dir, sleep_interval=10):
    """
    Continuously monitors the target directory for new results and prints summary statistics.
    """
    while True:
        # Load all metrics
        metrics_df = load_all_metrics(target_dir)

        if not metrics_df.empty:
            # --- Create and save the boxplot ---
            create_and_save_boxplot(metrics_df, target_dir)

            # --- Footer ---
            print("\n" + "="*80)

        # Wait for the next cycle
        for _ in range(sleep_interval):
            time.sleep(1)

if __name__ == '__main__':
    TARGET_DIR = 'LERN_vs_LTS_AD_Showdown_Results'
    monitor_and_calculate_averages(TARGET_DIR) 