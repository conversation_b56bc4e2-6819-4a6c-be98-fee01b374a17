# -*- coding: utf-8 -*-
# LERN-R: Lightweight and Effective Reconstruction Network with Recurrent Decoder
# This version uses a GRU-based decoder to explore serial vs. parallel reconstruction.

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import os, sys
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import warnings
from torch.nn.utils import weight_norm

warnings.filterwarnings('ignore')

# --- TSB-AD Imports & TCN Components ---
# (Assuming these are correctly set up as in lern.py)
try:
    from TSB_AD.models.base import BaseDetector
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector

class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        self.net = nn.Sequential(self.conv1, self.relu1, self.dropout1, self.conv2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        for i, out_channels in enumerate(num_channels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            layers.append(TemporalBlock(in_channels, out_channels, kernel_size, stride=1, dilation=dilation_size, padding=0, dropout=dropout))
        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

# ----------------------------------------------------
# 1. LERN-R Model Definition
# ----------------------------------------------------
class LERN_R_Model(nn.Module):
    def __init__(self, input_dim, window_size, latent_dim, tcn_channels, gru_hidden_dim, gru_layers, cnn_channels=16, downsample_stride=2):
        super(LERN_R_Model, self).__init__()
        self.window_size = window_size
        self.input_dim = input_dim

        # --- Encoder (Same as LERN) ---
        self.encoder_cnn = nn.Sequential(
            nn.Conv1d(input_dim, cnn_channels, kernel_size=7, padding=3, stride=downsample_stride),
            nn.GELU()
        )
        with torch.no_grad():
            dummy_input = torch.zeros(1, input_dim, window_size)
            self.downsampled_len = self.encoder_cnn(dummy_input).shape[2]
        self.encoder_tcn = TemporalConvNet(cnn_channels, tcn_channels)
        self.fc_encode = nn.Linear(tcn_channels[-1] * self.downsampled_len, latent_dim)

        # --- Decoder (Recurrent) ---
        self.decoder_gru = nn.GRU(input_dim, gru_hidden_dim, gru_layers, batch_first=True)
        self.decoder_fc_out = nn.Linear(gru_hidden_dim, input_dim)
        self.latent_to_hidden = nn.Linear(latent_dim, gru_layers * gru_hidden_dim)
        self.gru_layers = gru_layers
        self.gru_hidden_dim = gru_hidden_dim
        self.output_activation = nn.Sigmoid()

    def forward(self, x):
        batch_size = x.shape[0]
        device = x.device
        x_permuted = x.permute(0, 2, 1)

        # === Encoder ===
        encoded_cnn = self.encoder_cnn(x_permuted)
        encoded_tcn = self.encoder_tcn(encoded_cnn)
        encoded_flat = encoded_tcn.flatten(start_dim=1)
        latent_vec = self.fc_encode(encoded_flat)
        
        # === Decoder ===
        # Use latent vector to initialize GRU hidden state
        hidden = self.latent_to_hidden(latent_vec)
        hidden = hidden.view(batch_size, self.gru_layers, self.gru_hidden_dim).permute(1, 0, 2).contiguous()

        # Prepare for decoding loop
        decoder_input = torch.zeros(batch_size, 1, self.input_dim, device=device)
        outputs = []

        for _ in range(self.window_size):
            gru_out, hidden = self.decoder_gru(decoder_input, hidden)
            output_step = self.decoder_fc_out(gru_out)
            outputs.append(output_step)
            decoder_input = output_step # Use own output as next input
        
        reconstructed = torch.cat(outputs, dim=1)
        return self.output_activation(reconstructed)


# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class LERN_R_AD(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        # Hyperparameters
        self.window_size = HP.get('window_size', 128)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)
        
        print(f"🔄 Initializing LERN-R Detector... (Device: {self.device})")
        
        self.model = None
        self.ts_scaler = MinMaxScaler()
        self.score_scaler = MinMaxScaler()
        self.criterion = nn.MSELoss()

    def _create_windows(self, X):
        if len(X) < self.window_size: return np.empty((0, self.window_size, X.shape[1]))
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        
        if self.model is None:
            self.model = LERN_R_Model(
                input_dim=input_dim,
                window_size=self.window_size,
                latent_dim=self.latent_dim,
                tcn_channels=self.HP.get('tcn_channels', [16, 16]),
                gru_hidden_dim=self.HP.get('gru_hidden_dim', 64),
                gru_layers=self.HP.get('gru_layers', 2),
                cnn_channels=self.HP.get('cnn_channels', 16),
                downsample_stride=self.HP.get('downsample_stride', 4)
            ).to(self.device)

        X_original_for_scoring = X
        if self.normalize: X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        if len(windows) == 0:
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        optimizer = optim.AdamW(self.model.parameters(), lr=self.lr)
        
        self.model.train()
        for epoch in range(self.epochs):
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                reconstructed = self.model(batch_windows)
                loss = self.criterion(reconstructed, batch_windows)
                loss.backward()
                optimizer.step()
        
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        n_samples = X.shape[0]
        if self.normalize: X_norm = self.ts_scaler.transform(X)
        else: X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed = self.model(batch_windows)
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(np.array(window_scores)):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        if n_samples > self.window_size -1:
             scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel() 