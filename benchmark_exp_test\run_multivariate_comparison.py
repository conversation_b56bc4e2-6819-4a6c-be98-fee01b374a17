# -*- coding: utf-8 -*-
# run_multivariate_comparison.py
#
# A script to run a focused comparison on multivariate datasets between:
# 1. User's custom models: Attn-TCN-VAE, GRU-TCN-AE, GRU-TCN-VAE
# 2. Key baseline models: CNN, OmniAnomaly, PCA

import os
import sys
import pandas as pd
import random
import time
from tqdm import tqdm
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- TSB-AD Imports ---
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.models.CNN import CNN
from TSB_AD.models.OmniAnomaly import OmniAnomaly
from TSB_AD.models.PCA import PCA

# --- Visualization Import ---
from visualize_results import plot_detection_results

# --- User Model Imports ---
from benchmark_exp.attn_tcn_vae import AttnTcnVAE_AD
from benchmark_exp.gru_tcn_vae import GruTcnVae_AD
from benchmark_exp.gru_tcn_ae import GruTcnAe_AD
from benchmark_exp.lad import LAD_AD
from benchmark_exp.lern import LERN_AD
from benchmark_exp.lern_r import LERN_R_AD

# --- Custom Multivariate Data Loader ---
class MultivariateDataset:
    """A simple data loader for multivariate CSV files from TSB-AD-M."""
    def __init__(self, dataset_path):
        df = pd.read_csv(dataset_path).dropna()
        
        try:
            train_size_str = os.path.basename(dataset_path).split('_')[-3]
            train_size = int(train_size_str)
        except (ValueError, IndexError):
            train_size = int(len(df) * 0.7)

        self.data = df.iloc[:, :-1].values
        self.label = df.iloc[:, -1].values
        self.train = self.data[:train_size]
        self.test = self.data[train_size:]
        self.name = os.path.basename(dataset_path)

# --- Configuration ---
DATASET_ROOT = 'Datasets/TSB-AD-M/'
NUM_DATASETS_TO_TEST = 10
OUTPUT_DIR = 'benchmark_results_comparison'
OUTPUT_FILE = os.path.join(OUTPUT_DIR, 'final_showdown_comparison.csv')
OUTPUT_PLOT_PERFORMANCE = os.path.join(OUTPUT_DIR, 'final_showdown_performance.png')
OUTPUT_PLOT_RUNTIME = os.path.join(OUTPUT_DIR, 'final_showdown_runtime.png')

# --- Model Definitions ---
# Final showdown: LERN vs. LERN-R vs. CNN
MODELS = {
    'LERN': {
        'class': LERN_AD,
        'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64, 'latent_dim': 32, 'tcn_channels': [32,32,32], 'downsample_stride': 4, 'gpu': 0},
        'type': 'user'
    },
    'LERN-R': {
        'class': LERN_R_AD,
        'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64, 'latent_dim': 32, 'tcn_channels': [16,16], 'gru_hidden_dim': 64, 'downsample_stride': 4, 'gpu': 0},
        'type': 'user'
    },
    'CNN': {
        'class': CNN,
        'params': {'window_size': 100, 'num_channel': [32, 32, 40], 'lr': 0.0008},
        'type': 'baseline'
    },
    'OmniAnomaly': {
        'class': OmniAnomaly,
        'params': {'win_size': 100, 'lr': 0.002},
        'type': 'baseline'
    }
}

def run_single_experiment(model_name, model_info, dataset_path):
    """Runs a single model on a single dataset and returns the results."""
    try:
        dataset = MultivariateDataset(dataset_path)
        train_data = dataset.train
        full_data = dataset.data
        label = dataset.label
        
        start_time = time.time()
        
        model_type = model_info['type']
        
        # Adjust initialization based on model type
        if model_type == 'user':
            model = model_info['class'](HP=model_info['params'])
            model.fit(train_data)
        elif model_type == 'baseline':
            # Add feature dimension for baselines that require it
            params = model_info['params']
            if model_name in ['CNN', 'OmniAnomaly']:
                 params['feats'] = train_data.shape[1]
            model = model_info['class'](**params)
            model.fit(train_data)
        elif model_type == 'baseline_unsupervised':
            model = model_info['class'](**model_info['params'])
            model.fit(full_data) # Unsupervised models train on all data

        scores = model.decision_function(full_data)
        runtime = time.time() - start_time
        
        min_len = min(len(scores), len(label))
        scores, label = scores[:min_len], label[:min_len]
        
        slidingWindow = find_length_rank(full_data[:min_len], rank=1)
        results = get_metrics(scores, label, slidingWindow=slidingWindow)
        results['runtime(s)'] = runtime
        results['model'] = model_name
        results['dataset'] = os.path.basename(dataset_path)

        # Generate plot
        plot_save_dir = os.path.join(OUTPUT_DIR, model_name)
        plot_detection_results(
            data=full_data, label=label, score=scores,
            model_name=model_name, filename=dataset.name,
            train_size=len(train_data), save_dir=plot_save_dir
        )
        return results
    except Exception as e:
        print(f"❌ Error running {model_name} on {os.path.basename(dataset_path)}: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function to run the multivariate comparison benchmark."""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    all_datasets = [f for f in os.listdir(DATASET_ROOT) if f.endswith('.csv')]
    if not all_datasets:
        print(f"❌ No datasets found in {DATASET_ROOT}. Exiting.")
        return
        
    selected_datasets = random.sample(all_datasets, min(len(all_datasets), NUM_DATASETS_TO_TEST))
        
    print(f"🔬 Running focused multivariate comparison on {len(selected_datasets)} datasets...")
    print("   Models to be tested:", list(MODELS.keys()))
    print("   Datasets:", selected_datasets)

    all_results = []
    
    for dataset_name in tqdm(selected_datasets, desc="Total Progress"):
        dataset_path = os.path.join(DATASET_ROOT, dataset_name)
        print(f"\n--- Processing Dataset: {dataset_name} ---")
        
        for model_name, model_info in MODELS.items():
            print(f"  -> Running Model: {model_name}")
            results = run_single_experiment(model_name, model_info, dataset_path)
            if results:
                all_results.append(results)
                print(f"     ✅ Finished. PA-F1: {results.get('PA-F1', 'N/A'):.4f}, Runtime: {results.get('runtime(s)', 'N/A'):.2f}s")
    
    if not all_results:
        print("\n❌ No results were generated. Exiting.")
        return

    summary_df = pd.DataFrame(all_results)
    
    avg_per_model = summary_df.drop(columns=['dataset']).groupby('model').mean()
    print("\n--- Average Performance Per Model ---")
    print(avg_per_model.to_string())
    
    avg_df_to_save = avg_per_model.reset_index()
    avg_df_to_save['dataset'] = 'Average'
    full_summary_df = pd.concat([summary_df, avg_df_to_save], ignore_index=True)
    
    cols = ['model', 'dataset', 'runtime(s)', 'PA-F1', 'Standard-F1', 'AUC-PR', 'AUC-ROC']
    full_summary_df = full_summary_df[[c for c in cols if c in full_summary_df.columns]]
    
    full_summary_df.to_csv(OUTPUT_FILE, index=False)
    
    print(f"\n\n🎉 Benchmark finished!")
    print(f"📄 Summary report saved to: {OUTPUT_FILE}")

    create_visualizations(avg_per_model.reset_index())
    print(f"📊 Performance plot saved to: {OUTPUT_PLOT_PERFORMANCE}")
    print(f"📊 Runtime plot saved to: {OUTPUT_PLOT_RUNTIME}")


def create_visualizations(avg_df):
    """Generates and saves plots for model comparison."""
    if avg_df.empty:
        print("⚠️ No data to visualize.")
        return
        
    plt.style.use('seaborn-v0_8-whitegrid')
    
    metrics_to_plot = [c for c in ['PA-F1', 'Standard-F1', 'AUC-PR'] if c in avg_df.columns]
    plot_data = avg_df.melt(id_vars='model', value_vars=metrics_to_plot, var_name='Metric', value_name='Score')

    plt.figure(figsize=(14, 8))
    ax = sns.barplot(data=plot_data, x='Metric', y='Score', hue='model', palette='viridis')
    plt.title('Average Model Performance Comparison (Multivariate)', fontsize=16, weight='bold')
    plt.ylim(0, max(1.0, plot_data['Score'].max() * 1.1))
    
    for p in ax.patches:
        ax.annotate(f'{p.get_height():.3f}', (p.get_x() + p.get_width() / 2., p.get_height()), 
                    ha='center', va='center', xytext=(0, 9), textcoords='offset points', fontsize=9)

    plt.tight_layout()
    plt.savefig(OUTPUT_PLOT_PERFORMANCE, dpi=300)
    plt.close()

    plt.figure(figsize=(12, 7))
    runtime_data = avg_df.sort_values('runtime(s)', ascending=False)
    ax = sns.barplot(data=runtime_data, x='model', y='runtime(s)', palette='plasma')
    plt.title('Average Model Runtime Comparison (Log Scale)', fontsize=16, weight='bold')
    plt.ylabel('Average Runtime (seconds)')
    ax.set_yscale("log")
    plt.xticks(rotation=30, ha="right")

    for p in ax.patches:
        ax.annotate(f'{p.get_height():.2f}s', (p.get_x() + p.get_width() / 2., p.get_height()),
                    ha='center', va='center', xytext=(0, 9), textcoords='offset points', fontsize=9)

    plt.tight_layout()
    plt.savefig(OUTPUT_PLOT_RUNTIME, dpi=300)
    plt.close()


if __name__ == '__main__':
    main() 