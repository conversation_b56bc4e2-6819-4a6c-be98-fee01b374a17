import os
import sys
import torch
import numpy as np
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Model and Data Imports ---
from benchmark_exp.hta_ad import HTA_AD

# --- Plotting Style ---
np.random.seed(42) # for reproducible random perturbation locations

def load_dataset_from_path(dataset_path):
    """Loads a dataset and returns the training part."""
    if not os.path.exists(dataset_path):
        raise FileNotFoundError(f"Dataset not found at {dataset_path}")
        
    df = pd.read_csv(dataset_path)
    
    try:
        train_size_str = os.path.basename(dataset_path).split('_')[-3]
        split_point = int(train_size_str)
    except (ValueError, IndexError):
        split_point = int(len(df) * 0.5)

    train_data = df.iloc[:split_point].values[:, 0].astype(np.float32)
    return train_data

def find_perfect_window(model, data, window_size, device, error_threshold=1e-3):
    """
    Finds a window in the data that is reconstructed with very low error.
    """
    model.model.eval()
    
    windows = np.array([data[i:i + window_size] for i in range(len(data) - window_size + 1)])
    windows_tensor = torch.from_numpy(windows).unsqueeze(-1).to(device)
    
    with torch.no_grad():
        recons = model.model(windows_tensor)
        errors = torch.mean((windows_tensor - recons)**2, dim=(1, 2)).cpu().numpy()
        
    perfect_idx = np.where(errors < error_threshold)[0]
    if len(perfect_idx) == 0:
        print("Warning: No 'perfect' window found below threshold. Using best available.")
        best_idx = np.argmin(errors)
        return windows[best_idx]
    
    return windows[perfect_idx[0]]


def main():
    """Main function to run the multi-point perturbation analysis."""
    
    # --- Configuration ---
    DATASET_NAME = '836_Exathlon_id_27_Facility_tr_10766_1st_12590'
    WINDOW_SIZE = 128
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    # Only run for 20 perturbation points as requested
    PERTURBATION_COUNT = 20 
    PERTURBATION_MAGNITUDE_STD = 3.0 

    # --- Data and Model ---
    print(f"Loading dataset: {DATASET_NAME}")
    dataset_path = os.path.join('Datasets/TSB-AD-U/', DATASET_NAME + '.csv')
    data = load_dataset_from_path(dataset_path)

    print("Initializing and training HTA_AD model...")
    model = HTA_AD(HP={'window_size': WINDOW_SIZE, 'epochs': 15})
    model.fit(data.reshape(-1, 1))
    
    # --- Analysis ---
    print("Finding a well-reconstructed window...")
    original_window = find_perfect_window(model, data, WINDOW_SIZE, device)
    signal_std = original_window.std()

    # --- Perturbation ---
    print(f"Applying {PERTURBATION_COUNT} perturbations...")
    perturbed_window = original_window.copy()
    perturbation_indices = np.random.choice(range(WINDOW_SIZE), size=PERTURBATION_COUNT, replace=False)
    for idx in perturbation_indices:
        perturbed_window[idx] += signal_std * PERTURBATION_MAGNITUDE_STD
    
    model.model.eval()
    with torch.no_grad():
        perturbed_tensor = torch.from_numpy(perturbed_window).unsqueeze(0).unsqueeze(-1).to(device)
        perturbed_recons = model.model(perturbed_tensor).squeeze().cpu().numpy()
    error_series = np.abs(perturbed_window - perturbed_recons)

    # --- Visualization with Plotly ---
    print("Generating final perturbation plot with Plotly...")
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        subplot_titles=('<b>Reconstruction vs. Signal</b>', '<b>Point-wise Absolute Error</b>'),
        vertical_spacing=0.15
    )
    
    time_steps = np.arange(WINDOW_SIZE)

    # --- Reconstruction Plot (Top) ---
    fig.add_trace(go.Scatter(
        x=time_steps, y=perturbed_window, name='Perturbed Signal',
        line=dict(color='#1f77b4', width=1.5), opacity=0.8
    ), row=1, col=1)
    
    fig.add_trace(go.Scatter(
        x=time_steps, y=perturbed_recons, name='Reconstruction',
        line=dict(color='#ff7f0e', width=2.5)
    ), row=1, col=1)

    # --- Error Plot (Bottom) ---
    fig.add_trace(go.Bar(
        x=time_steps, y=error_series, name='Absolute Error',
        marker_color='#d62728'
    ), row=2, col=1)
    
    # --- Add Perturbation Lines ---
    for i, idx in enumerate(perturbation_indices):
        is_first = i == 0
        fig.add_vline(
            x=idx, line_width=1.5, line_dash="dash", line_color="#2ca02c",
            row='all', col=1
        )
    
    # Dummy trace for the legend of perturbation lines
    fig.add_trace(go.Scatter(
        x=[None], y=[None], mode='lines',
        line=dict(color='#2ca02c', dash='dash', width=2),
        name='Perturbation Location'
    ), row=1, col=1)

    # --- Update Layout ---
    fig.update_layout(
        title_text=f'<b>HTA_AD Response to {PERTURBATION_COUNT} Anomaly Spikes</b>',
        font=dict(family="Times New Roman", size=16),
        plot_bgcolor='white',
        legend=dict(font_size=14, yanchor="top", y=0.98, xanchor="right", x=0.98),
        height=800,
        width=1600,
        bargap=0,
    )
    
    fig.update_yaxes(title_text="Signal Amplitude", row=1, col=1, gridcolor='#E5E5E5', linecolor='black', linewidth=1, showline=True)
    fig.update_yaxes(title_text="Absolute Error", row=2, col=1, gridcolor='#E5E5E5', linecolor='black', linewidth=1, showline=True)
    fig.update_xaxes(title_text="Time Step within Window", row=2, col=1, gridcolor='#E5E5E5', linecolor='black', linewidth=1, showline=True)
    fig.update_xaxes(row=1, col=1, gridcolor='#E5E5E5', linecolor='black', linewidth=1, showline=True)

    # --- Save Figure ---
    output_dir = 'visualizations/perturbation_analysis/'
    os.makedirs(output_dir, exist_ok=True)
        
    output_filename = f'perturbation_20_spikes_{DATASET_NAME}_plotly.png'
    output_path = os.path.join(output_dir, output_filename)
    pio.write_image(fig, output_path, scale=2)
    print(f"\nAnalysis complete. Plot saved to: {output_path}")

if __name__ == '__main__':
    main() 