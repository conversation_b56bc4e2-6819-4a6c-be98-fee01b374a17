import os
import sys
import torch
import numpy as np
import pandas as pd
import json
import plotly.graph_objects as go
import plotly.io as pio
from sklearn.metrics import precision_recall_curve, auc

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Model and Data Imports ---
from benchmark_exp.hta_ad import HTA_AD

np.random.seed(42)
torch.manual_seed(42)

def load_clean_dataset(dataset_path):
    """Loads a dataset and splits it into clean train and test sets."""
    if not os.path.exists(dataset_path):
        raise FileNotFoundError(f"Dataset not found at {dataset_path}")
        
    df = pd.read_csv(dataset_path)
    
    try:
        # Try to parse from directory name first
        train_size_str = os.path.basename(os.path.dirname(dataset_path)).split('_tr_')[-1].split('_')[0]
        split_point = int(train_size_str)
    except (ValueError, IndexError):
        # Fallback for different naming conventions
        file_base = os.path.basename(dataset_path)
        if '_tr_' in file_base:
             train_size_str = file_base.split('_tr_')[-1].split('_')[0]
             split_point = int(train_size_str)
        else:
            # A reasonable default if parsing fails
            split_point = int(len(df) * 0.7)


    train_data = df.iloc[:split_point, [0]].values.astype(np.float32)
    test_data = df.iloc[split_point:, [0]].values.astype(np.float32)
    test_labels = df.iloc[split_point:, 1].values.astype(int)
    
    return train_data, test_data, test_labels

def inject_noise(data, noise_ratio, magnitude_std=1.5):
    """Injects spike-like noise into a dataset by adding/subtracting large values."""
    if noise_ratio == 0:
        return data
    
    noisy_data = data.copy()
    num_points = len(data)
    num_noise_points = int(num_points * noise_ratio)
    
    if num_noise_points > num_points:
        num_noise_points = num_points
        
    noise_indices = np.random.choice(num_points, num_noise_points, replace=False)
    
    signal_std = data.std()
    if signal_std == 0:
        signal_std = 1.0
        
    noise_magnitude_std = signal_std * magnitude_std
    
    for idx in noise_indices:
        noise = np.random.normal(0, noise_magnitude_std)
        noisy_data[idx, 0] += noise
        
    return noisy_data

def calculate_vus_pr(labels, scores):
    """Calculates the Volume Under the Precision-Recall Surface (VUS-PR)."""
    precision, recall, _ = precision_recall_curve(labels, scores)
    pr_auc = auc(recall, precision)
    return pr_auc

def run_analysis_on_dataset(dataset_name):
    """Runs the full noisy training analysis for a single dataset."""
    print(f"\n=========================================================")
    print(f"========== Running Analysis for: {dataset_name} ==========")
    print(f"=========================================================\n")

    # --- Config ---
    WINDOW_SIZE = 128
    NOISE_LEVELS = np.linspace(0, 1.0, 11)
    NOISE_MAGNITUDE = 10.0

    # --- Load Data ---
    print(f"Loading clean dataset: {dataset_name}")
    dataset_path = os.path.join(project_root, 'Datasets/TSB-AD-U/', dataset_name + '.csv')
    
    if not os.path.exists(dataset_path):
        dataset_path_alt = os.path.join(project_root, 'full_univariate_benchmark_results', dataset_name, 'test_data.csv')
        if os.path.exists(dataset_path_alt):
            dataset_path = dataset_path_alt
        else:
            print(f"SKIPPING: Could not find dataset {dataset_name} in primary or fallback location.")
            return

    clean_train_data, clean_test_data, clean_test_labels = load_clean_dataset(dataset_path)

    results = []

    # --- Run Experiment for Each Noise Level ---
    for noise_level in NOISE_LEVELS:
        print(f"\n--- Testing with {noise_level*100:.1f}% noise in training data (Magnitude: {NOISE_MAGNITUDE}) ---")
        
        # 1. Inject noise into training data
        print("Injecting noise...")
        noisy_train_data = inject_noise(clean_train_data, noise_level, magnitude_std=NOISE_MAGNITUDE)
        
        # 2. Train a new model on the noisy data
        print("Training HTA_AD model on noisy data...")
        model = HTA_AD(HP={'window_size': WINDOW_SIZE, 'epochs': 15})
        model.fit(noisy_train_data)
        
        # 3. Evaluate on the CLEAN test data
        print("Evaluating model on clean test data...")
        anomaly_scores = model.decision_function(clean_test_data)
        
        # 4. Calculate performance metric (VUS-PR)
        vus_pr_score = calculate_vus_pr(clean_test_labels, anomaly_scores)
        print(f"Performance (VUS-PR Score): {vus_pr_score:.4f}")
        results.append(vus_pr_score)

    # --- Visualization (Line Plot) with Plotly ---
    print("\nGenerating final performance degradation plot with Plotly...")
    
    # Convert noise levels to percentage for the x-axis
    noise_percentages = [l * 100 for l in NOISE_LEVELS]
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=noise_percentages, 
        y=results,
        mode='lines+markers+text',
        marker=dict(size=10, color='#1f77b4'),
        line=dict(color='#1f77b4', width=2),
        text=[f'{r:.3f}' for r in results],
        textposition='top center',
        textfont=dict(size=14, weight='bold'),
        name='HTA_AD Performance'
    ))

    fig.update_layout(
        title=f'<b>HTA_AD Performance on {dataset_name}<br>with Noisy Training Data</b>',
        xaxis_title='<b>Percentage of Noise Contamination in Training Data (%)</b>',
        yaxis_title='<b>Performance on Clean Test Data (VUS-PR Score)</b>',
        font=dict(family="Times New Roman", size=16),
        plot_bgcolor='white',
        xaxis=dict(
            tickmode='array',
            tickvals=noise_percentages,
            ticktext=[f'{p:.0f}%' for p in noise_percentages],
            gridcolor='#E5E5E5',
            showline=True,
            linecolor='black',
            linewidth=1
        ),
        yaxis=dict(
            gridcolor='#E5E5E5',
            showline=True,
            linecolor='black',
            linewidth=1
        ),
        height=700,
        width=1200,
        showlegend=False
    )
    
    # Set y-axis to be slightly larger than the max result for clarity
    if results:
        fig.update_yaxes(range=[min(results) * 0.95, max(results) * 1.05])

    # --- Save Figure ---
    output_dir = os.path.join(project_root, 'visualizations', 'robustness_analysis')
    os.makedirs(output_dir, exist_ok=True)
        
    output_filename = f'noisy_training_degradation_{dataset_name}_plotly.png'
    output_path = os.path.join(output_dir, output_filename)
    pio.write_image(fig, output_path, scale=2)
    print(f"\nAnalysis complete for {dataset_name}. Plot saved to: {output_path}")

    return {"scores": results, "noise_levels": noise_percentages}

def main():
    """Main function to run the noisy training analysis across multiple datasets."""
    
    # --- Select datasets for the experiment ---
    DATASET_NAMES = [
        '836_Exathlon_id_27_Facility_tr_10766_1st_12590', # The original one
        '001_NAB_id_1_Facility_tr_1007_1st_2014',         # A NAB dataset
        '029_WSD_id_1_WebService_tr_4559_1st_10201'       # A WSD dataset
    ]

    all_results = {}
    for name in DATASET_NAMES:
        result = run_analysis_on_dataset(name)
        if result:
            all_results[name] = result

    # --- Save all results to a JSON file ---
    results_dir = os.path.join(project_root, 'visualizations', 'robustness_analysis')
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    
    results_filepath = os.path.join(results_dir, 'robustness_results.json')
    with open(results_filepath, 'w') as f:
        json.dump(all_results, f, indent=4)
    
    print(f"\nAll experiment results saved to: {results_filepath}")

if __name__ == '__main__':
    main() 