#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成用于模型架构图（如Visio）的可视化组件
V3: 单变量和多变量均包含异常展示，并进行视觉美化
"""

import numpy as np
import plotly.graph_objects as go
import plotly.io as pio
import os

# --- 全局绘图样式设置 ---
pio.templates["diagram_asset_v3"] = go.layout.Template(
    layout=go.Layout(
        font=dict(family="Arial", size=16, color="#000"),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        xaxis=dict(showgrid=False, zeroline=False, showticklabels=False, showline=False, title_text=""),
        yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, showline=False, title_text=""),
        margin=dict(l=5, r=5, t=5, b=5),
        showlegend=False,
    )
)
pio.templates.default = "diagram_asset_v3"

# --- 1. 单变量输入 (含异常) ---
def generate_univariate_input_with_anomaly_plot(output_dir):
    """生成包含异常的单变量时间序列输入"""
    t = np.linspace(0, 10, 200)
    y = np.sin(t * 1.5) + np.cos(t * 0.5)
    y[100:110] += 2.0  # 添加异常
    fig = go.Figure(go.Scatter(x=t, y=y, mode='lines', line=dict(color='#3498db', width=6)))
    filepath = os.path.join(output_dir, '1_input_univariate_anomaly.png')
    fig.write_image(filepath, width=300, height=100, scale=3)
    print(f"✅ 1. Saved univariate input with anomaly to: {filepath}")

# --- 2. 单变量输出 (凸显异常) ---
def generate_univariate_output_with_anomaly_plot(output_dir):
    """生成单变量重构输出，并凸显对异常的重构失败"""
    t = np.linspace(0, 10, 200)
    y_true = np.sin(t * 1.5) + np.cos(t * 0.5)
    y_true[100:110] += 2.0  # 完全相同的异常
    y_recon = np.sin(t * 1.5) + np.cos(t * 0.5)  # 重构时 "忽略" 了异常

    fig = go.Figure()
    # 原始序列 (虚线)
    fig.add_trace(go.Scatter(x=t, y=y_true, mode='lines', line=dict(color='#3498db', width=5, dash='dot'), opacity=0.7))
    # 重构序列 (实线)
    fig.add_trace(go.Scatter(x=t, y=y_recon, mode='lines', line=dict(color='#2ecc71', width=5)))
    # 高亮误差区域
    fig.add_trace(go.Scatter(
        x=np.concatenate([t, t[::-1]]),
        y=np.concatenate([y_true, y_recon[::-1]]),
        fill='toself',
        fillcolor='rgba(231, 76, 60, 0.4)',
        line=dict(color='rgba(255,255,255,0)'),
        hoverinfo="skip"
    ))
    filepath = os.path.join(output_dir, '2_output_univariate_anomaly.png')
    fig.write_image(filepath, width=300, height=100, scale=3)
    print(f"✅ 2. Saved univariate output with anomaly to: {filepath}")

# --- 3. 多变量输入 (含异常) ---
def generate_multivariate_input_with_anomaly_plot(output_dir):
    """生成包含异常的多变量时间序列输入"""
    t = np.linspace(0, 10, 200)
    y1 = np.sin(t * 1.5)
    y2 = np.cos(t * 0.8) * 0.8 - 0.5
    y3 = np.sin(t * 1.2) * 0.6 + 1.0
    y3[100:110] += 1.5 # 异常尖峰
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=t, y=y1, mode='lines', line=dict(color='#3498db', width=4)))
    fig.add_trace(go.Scatter(x=t, y=y2, mode='lines', line=dict(color='#9b59b6', width=4)))
    fig.add_trace(go.Scatter(x=t, y=y3, mode='lines', line=dict(color='#e74c3c', width=5))) # 突出异常序列
    filepath = os.path.join(output_dir, '3_input_multivariate_anomaly.png')
    fig.write_image(filepath, width=300, height=150, scale=3)
    print(f"✅ 3. Saved multivariate input with anomaly to: {filepath}")

# --- 4. 多变量输出 (凸显异常) ---
def generate_multivariate_output_with_anomaly_plot(output_dir):
    """生成多变量重构输出，并凸显对异常的重构失败"""
    t = np.linspace(0, 10, 200)
    y1_true, y2_true, y3_true = np.sin(t*1.5), np.cos(t*0.8)*0.8-0.5, np.sin(t*1.2)*0.6+1.0
    y3_true[100:110] += 1.5
    y1_recon, y2_recon, y3_recon = y1_true*0.98, y2_true*0.95, np.sin(t*1.2)*0.6+1.0

    fig = go.Figure()
    # 正常序列的重构
    for y_true, y_recon, color in [(y1_true, y1_recon, '#3498db'), (y2_true, y2_recon, '#9b59b6')]:
        fig.add_trace(go.Scatter(x=t, y=y_true, mode='lines', line=dict(color=color, width=4, dash='dot'), opacity=0.5))
        fig.add_trace(go.Scatter(x=t, y=y_recon, mode='lines', line=dict(color=color, width=4)))
    # 异常序列的重构
    fig.add_trace(go.Scatter(x=t, y=y3_true, mode='lines', line=dict(color='#e74c3c', width=4, dash='dot'), opacity=0.7))
    fig.add_trace(go.Scatter(x=t, y=y3_recon, mode='lines', line=dict(color='#2ecc71', width=4)))
    # 高亮异常序列的误差区域
    fig.add_trace(go.Scatter(
        x=np.concatenate([t, t[::-1]]), y=np.concatenate([y3_true, y3_recon[::-1]]),
        fill='toself', fillcolor='rgba(231, 76, 60, 0.4)',
        line=dict(color='rgba(255,255,255,0)'), hoverinfo="skip"
    ))
    filepath = os.path.join(output_dir, '4_output_multivariate_anomaly.png')
    fig.write_image(filepath, width=300, height=150, scale=3)
    print(f"✅ 4. Saved multivariate output with anomaly to: {filepath}")

def main():
    """主执行函数"""
    output_dir = 'visualizations/diagram_assets'
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 Assets will be saved to: {output_dir}")
        
    print("\n--- Generating Visualization Assets ---")
    generate_univariate_input_with_anomaly_plot(output_dir)
    generate_univariate_output_with_anomaly_plot(output_dir)
    generate_multivariate_input_with_anomaly_plot(output_dir)
    generate_multivariate_output_with_anomaly_plot(output_dir)
    print("\n🎉 All 4 assets generated successfully!")

if __name__ == '__main__':
    main() 