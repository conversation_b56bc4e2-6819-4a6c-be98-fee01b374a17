# -*- coding: utf-8 -*-
# run_focused_comparison.py
#
# A script to run a focused comparison between three models:
# 1. Attn-TCN-VAE (Self-Attention)
# 2. TemporalAttn_TCN_VAE (Temporal-Aware Attention)
# 3. LTS_AD (High-performance baseline)
#
# The script will randomly select 5 datasets and generate a summary CSV.

import os
import sys
import pandas as pd
import random
import time
from tqdm import tqdm
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- TSB-AD Imports ---
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.evaluation.metrics import get_metrics

# --- Visualization Import ---
from visualize_results import plot_detection_results

# --- Model Imports ---
from benchmark_exp.attn_tcn_vae import AttnTcnVAE_AD
from benchmark_exp.temporal_attn_tcn_vae import TemporalAttn_TCN_VAE
from benchmark_exp.Lightweight_TS_Model import LTS_AD
from benchmark_exp.gru_tcn_vae import GruTcnVae_AD
from benchmark_exp.gru_tcn_ae import GruTcnAe_AD

# --- Custom Data Loader ---
class SimpleDataset:
    """A simple data loader to replace the missing TSB_UCR_Dataset."""
    def __init__(self, dataset_path):
        df = pd.read_csv(dataset_path)
        
        # Heuristic to find split point if not obvious
        # This assumes the 'tr' part of the filename indicates train size
        try:
            train_size_str = os.path.basename(dataset_path).split('_')[-3]
            train_size = int(train_size_str)
        except (ValueError, IndexError):
            # Fallback to a 70/30 split if filename parsing fails
            train_size = int(len(df) * 0.7)

        data = df.iloc[:, 0].values.reshape(-1, 1)
        self.label = df.iloc[:, 1].values
        self.train = data[:train_size]
        self.test = data[train_size:]
        self.name = os.path.basename(dataset_path)

# --- Configuration ---
DATASET_ROOT = 'Datasets/TSB-AD-U/'
NUM_DATASETS_TO_TEST = 5
OUTPUT_DIR = 'benchmark_results_comparison'
OUTPUT_FILE = os.path.join(OUTPUT_DIR, 'focused_comparison_summary.csv')
OUTPUT_PLOT_PERFORMANCE = os.path.join(OUTPUT_DIR, 'focused_comparison_performance.png')
OUTPUT_PLOT_RUNTIME = os.path.join(OUTPUT_DIR, 'focused_comparison_runtime.png')

# --- Model Definitions ---
MODELS = {
    'Attn-TCN-VAE': {
        'class': AttnTcnVAE_AD,
        'params': {
            'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64,
            'latent_dim': 32, 'beta': 1.0, 'tcn_layers': 4, 'tcn_filters': 64,
            'tcn_kernel_size': 3, 'tcn_dropout': 0.2, 'gpu': 0
        }
    },
    'TemporalAttn-TCN-VAE': {
        'class': TemporalAttn_TCN_VAE,
        'params': {
            'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64,
            'latent_dim': 32, 'beta': 1.0, 'tcn_layers': 4, 'tcn_filters': 64,
            'tcn_kernel_size': 3, 'tcn_dropout': 0.2, 'gpu': 0
        }
    },
    'GRU-TCN-VAE': {
        'class': GruTcnVae_AD,
        'params': {
            'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64,
            'latent_dim': 32, 'beta': 1.0, 'tcn_layers': 4, 'tcn_filters': 64,
            'tcn_kernel_size': 3, 'tcn_dropout': 0.2, 'gru_hidden_dim': 64,
            'gru_layers': 1, 'gpu': 0
        }
    },
    'GRU-TCN-AE': {
        'class': GruTcnAe_AD,
        'params': {
            'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64,
            'latent_dim': 32, 'tcn_layers': 4, 'tcn_filters': 64,
            'tcn_kernel_size': 3, 'tcn_dropout': 0.2, 'gru_hidden_dim': 64,
            'gru_layers': 1, 'gpu': 0
        }
    },
    'LTS_AD': {
        'class': LTS_AD,
        'params': {
            'window_size': 100, 'epochs': 30, 'lr': 1e-3, 'batch_size': 128,
            'latent_dim': 16, 'gpu': 0
        }
    }
}

def run_single_experiment(model_name, model_info, dataset_path):
    """Runs a single model on a single dataset and returns the results."""
    try:
        # Load data using the simple loader
        dataset = SimpleDataset(dataset_path)
        train_data = dataset.train
        test_data = dataset.test
        label = dataset.label
        
        # Instantiate and train model
        start_time = time.time()
        model = model_info['class'](HP=model_info['params'])
        model.fit(train_data)
        
        # Get scores
        combined_data = np.concatenate([train_data, test_data], axis=0)
        scores = model.decision_function(combined_data)
        runtime = time.time() - start_time
        
        # Align scores and labels
        min_len = min(len(scores), len(label))
        scores, label = scores[:min_len], label[:min_len]
        
        # Evaluate
        slidingWindow = find_length_rank(combined_data[:min_len], rank=1)
        results = get_metrics(scores, label, slidingWindow=slidingWindow)
        results['runtime(s)'] = runtime
        results['model'] = model_name
        results['dataset'] = os.path.basename(dataset_path)

        # --- Generate Detailed Plot ---
        plot_save_dir = os.path.join(OUTPUT_DIR, model_name)
        plot_detection_results(
            data=combined_data,
            label=label,
            score=scores,
            model_name=model_name,
            filename=dataset.name,
            train_size=len(train_data),
            save_dir=plot_save_dir
        )
        
        return results
    except Exception as e:
        print(f"❌ Error running {model_name} on {os.path.basename(dataset_path)}: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function to run the comparison benchmark."""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Get list of all datasets and randomly select a subset
    all_datasets = [f for f in os.listdir(DATASET_ROOT) if f.endswith('.csv')]
    if len(all_datasets) < NUM_DATASETS_TO_TEST:
        print(f"⚠️ Warning: Found only {len(all_datasets)} datasets, testing on all of them.")
        selected_datasets = all_datasets
    else:
        selected_datasets = random.sample(all_datasets, NUM_DATASETS_TO_TEST)
        
    print(f"🔬 Running focused comparison on {len(selected_datasets)} datasets...")
    print("   Models to be tested:", list(MODELS.keys()))
    print("   Datasets:", selected_datasets)

    all_results = []
    
    for dataset_name in tqdm(selected_datasets, desc="Total Progress"):
        dataset_path = os.path.join(DATASET_ROOT, dataset_name)
        print(f"\n--- Processing Dataset: {dataset_name} ---")
        
        for model_name, model_info in MODELS.items():
            print(f"  -> Running Model: {model_name}")
            results = run_single_experiment(model_name, model_info, dataset_path)
            if results:
                all_results.append(results)
                print(f"     ✅ Finished. F1-Score: {results.get('Standard-F1', 'N/A'):.4f}, Runtime: {results.get('runtime(s)', 'N/A'):.2f}s")
    
    if not all_results:
        print("\n❌ No results were generated. Exiting.")
        return

    # Create and save summary dataframe
    summary_df = pd.DataFrame(all_results)
    
    # --- Correctly Calculate Average Performance by Model ---
    avg_per_model = summary_df.drop(columns=['dataset']).groupby('model').mean()
    print("\n--- Average Performance Per Model ---")
    print(avg_per_model.to_string())
    
    # Append average results to the main dataframe for the report
    avg_df_to_save = avg_per_model.reset_index()
    avg_df_to_save['dataset'] = 'Average'
    full_summary_df = pd.concat([summary_df, avg_df_to_save], ignore_index=True)
    
    # Reorder columns for readability
    cols = ['model', 'dataset', 'runtime(s)'] + [c for c in full_summary_df.columns if c not in ['model', 'dataset', 'runtime(s)']]
    full_summary_df = full_summary_df[cols]
    
    full_summary_df.to_csv(OUTPUT_FILE, index=False)
    
    print(f"\n\n🎉 Benchmark finished!")
    print(f"📄 Summary report saved to: {OUTPUT_FILE}")

    # --- Generate and Save Visualizations ---
    create_visualizations(avg_per_model)
    print(f"📊 Performance plot saved to: {OUTPUT_PLOT_PERFORMANCE}")
    print(f"📊 Runtime plot saved to: {OUTPUT_PLOT_RUNTIME}")


def create_visualizations(avg_df):
    """Generates and saves plots for model comparison."""
    
    # Plot 1: Key Performance Metrics (F1, AUC-PR, AUC-ROC)
    plt.style.use('seaborn-v0_8-whitegrid')
    metrics_to_plot = ['Standard-F1', 'AUC-PR', 'AUC-ROC']
    plot_data = avg_df[metrics_to_plot].reset_index()
    plot_data_melted = plot_data.melt(id_vars='model', var_name='Metric', value_name='Score')

    plt.figure(figsize=(12, 7))
    ax = sns.barplot(data=plot_data_melted, x='Metric', y='Score', hue='model', palette='viridis')
    plt.title('Average Model Performance Comparison', fontsize=16, weight='bold')
    plt.xlabel('Metric', fontsize=12)
    plt.ylabel('Average Score', fontsize=12)
    plt.ylim(0, max(1.0, plot_data_melted['Score'].max() * 1.1))
    plt.xticks(fontsize=10)
    plt.yticks(fontsize=10)
    plt.legend(title='Model', fontsize=10)
    
    # Add value labels on top of bars
    for p in ax.patches:
        ax.annotate(f'{p.get_height():.3f}', 
                    (p.get_x() + p.get_width() / 2., p.get_height()), 
                    ha='center', va='center', 
                    xytext=(0, 9), 
                    textcoords='offset points',
                    fontsize=9,
                    color='black')

    plt.tight_layout()
    plt.savefig(OUTPUT_PLOT_PERFORMANCE, dpi=300)
    plt.close()

    # Plot 2: Runtime Comparison
    runtime_data = avg_df[['runtime(s)']].reset_index()
    plt.figure(figsize=(10, 6))
    ax = sns.barplot(data=runtime_data, x='model', y='runtime(s)', palette='plasma')
    plt.title('Average Model Runtime Comparison', fontsize=16, weight='bold')
    plt.xlabel('Model', fontsize=12)
    plt.ylabel('Average Runtime (seconds)', fontsize=12)
    plt.xticks(rotation=15, ha="right", fontsize=10)
    plt.yticks(fontsize=10)

    # Add value labels on top of bars
    for p in ax.patches:
        ax.annotate(f'{p.get_height():.2f}s', 
                    (p.get_x() + p.get_width() / 2., p.get_height()), 
                    ha='center', va='center', 
                    xytext=(0, 9), 
                    textcoords='offset points',
                    fontsize=10,
                    color='black')

    plt.tight_layout()
    plt.savefig(OUTPUT_PLOT_RUNTIME, dpi=300)
    plt.close()


if __name__ == '__main__':
    main() 