import os
import sys
import torch
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.io as pio
from plotly.subplots import make_subplots

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Model and Data Imports ---
from TSB_AD.models.AnomalyTransformer import AnomalyTransformer
from benchmark_exp.hta_ad import HTA_AD
# from TSB_AD.data_manager.data_loader import load_data
# from TSB_AD.data_manager.dataset_manager import get_dataset_meta

# The following lines are remnants of matplotlib/seaborn and are no longer needed.
# sns.set_style("whitegrid")
# plt.rcParams['font.family'] = 'sans-serif'
# plt.rcParams['font.sans-serif'] = ['Arial']


def load_dataset_from_path(dataset_path):
    """Loads a dataset and splits it into train and test sets."""
    df = pd.read_csv(dataset_path)
    
    # Heuristic to find split point. Many datasets in this repo
    # indicate train size in the filename, e.g., _tr_1234_
    try:
        train_size_str = os.path.basename(dataset_path).split('_')[-3]
        train_size = int(train_size_str)
    except (ValueError, IndexError):
        # Fallback: look for first anomaly label
        labels_present = df.columns[-1].lower() in ['label', 'attack']
        if labels_present and df.iloc[:, -1].isin([1]).any():
             train_size = np.where(df.iloc[:, -1] == 1)[0][0]
        else:
            # Default to 70/30 split if no other info
            train_size = int(len(df) * 0.7)
            
    X_train = df.iloc[:train_size, :-1].values.astype(float)
    y_train = df.iloc[:train_size, -1].values.astype(int)
    X_test = df.iloc[train_size:, :-1].values.astype(float)
    y_test = df.iloc[train_size:, -1].values.astype(int)
    
    return X_train, y_train, X_test, y_test


def get_reconstruction(model, X, window_size, device):
    """
    A helper function to get reconstructions from a trained model.
    This is especially needed for models that don't expose reconstruction directly.
    """
    reconstructions = np.zeros_like(X)
    
    # Apply the same scaler that was used during training
    if hasattr(model, 'ts_scaler') and model.ts_scaler is not None:
        X_norm = model.ts_scaler.transform(X)
    else:
        # Fallback for models without a scaler or for AnomalyTransformer
        # which handles scaling externally.
        X_norm = X

    if isinstance(model, AnomalyTransformer):
        internal_model = model.model
        if internal_model is None:
             raise ValueError("AnomalyTransformer's internal model is not initialized.")
        internal_model.eval()
        
        windows = np.array([X_norm[i:i + window_size] for i in range(len(X_norm) - window_size + 1)])
        if len(windows) == 0:
            return reconstructions
            
        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=model.batch_size, shuffle=False)

        full_reconstructed_points = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(device)
                output, _, _, _ = internal_model(batch_windows)
                full_reconstructed_points.append(output[:, -1, :].cpu().numpy())
        
        if not full_reconstructed_points:
            return reconstructions

        all_reconstructed_points = np.concatenate(full_reconstructed_points)

        recons_norm = np.zeros_like(X_norm)
        for i, point in enumerate(all_reconstructed_points):
             recons_norm[i + window_size - 1, :] = point
        recons_norm[:window_size-1] = recons_norm[window_size-1]

    elif hasattr(model, 'model'): # For HTA_AD and other TSB-AD compliant models
        internal_model = model.model
        internal_model.eval()
        windows = model._create_windows(X_norm)
        if len(windows) == 0: return reconstructions
        
        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=512, shuffle=False)
        
        full_reconstructed_windows = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(device)
                reconstructed_batch = internal_model(batch_windows)
                full_reconstructed_windows.append(reconstructed_batch.cpu().numpy())
        
        all_reconstructed_windows = np.concatenate(full_reconstructed_windows)
        
        recons_norm = np.zeros_like(X_norm)
        counts = np.zeros_like(X_norm)
        for i, recon_window in enumerate(all_reconstructed_windows):
            recons_norm[i:i + window_size] += recon_window
            counts[i:i + window_size] += 1
        recons_norm[counts > 0] /= counts[counts > 0]
        recons_norm[:window_size-1] = recons_norm[window_size-1]

    else:
        raise NotImplementedError(f"Reconstruction logic not implemented for model type: {type(model)}")

    # Inverse transform the normalized reconstruction to get the final result
    if hasattr(model, 'ts_scaler') and model.ts_scaler is not None:
        reconstructions = model.ts_scaler.inverse_transform(recons_norm)
    else:
        reconstructions = recons_norm # Assume reconstruction is already in original scale

    return reconstructions


def main():
    """
    Main function to run the reconstruction analysis using Plotly.
    """
    DATASET_NAME = '001_NAB_id_1_Facility_tr_1007_1st_2014'
    dataset_path_m = os.path.join('Datasets/TSB-AD-M/', DATASET_NAME + '.csv')
    dataset_path_u = os.path.join('Datasets/TSB-AD-U/', DATASET_NAME + '.csv')
    
    if os.path.exists(dataset_path_m):
        DATASET_PATH = dataset_path_m
    elif os.path.exists(dataset_path_u):
        DATASET_PATH = dataset_path_u
    else:
        print(f"❌ Dataset '{DATASET_NAME}.csv' not found.")
        return

    WINDOW_SIZE = 128
    DEVICE = "cuda:0" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {DEVICE}")

    print(f"\nLoading dataset: {DATASET_NAME}...")
    X_train, y_train, X_test, y_test = load_dataset_from_path(DATASET_PATH)
    full_series = np.concatenate([X_train, X_test], axis=0)
    full_labels = np.concatenate([y_train, y_test], axis=0)
    train_test_split_idx = len(X_train)
    
    models_to_analyze = {
        'HTA_AD': HTA_AD(HP={'window_size': WINDOW_SIZE, 'epochs': 10, 'gpu': 0}),
        'AnomalyTransformer': AnomalyTransformer(win_size=WINDOW_SIZE, num_epochs=10, device=DEVICE)
    }

    fig = make_subplots(
        rows=len(models_to_analyze), cols=1,
        subplot_titles=list(models_to_analyze.keys()),
        shared_xaxes=True
    )
        
    print("\nStarting model training and reconstruction analysis...")
    for i, (model_name, model_instance) in enumerate(models_to_analyze.items()):
        row_num = i + 1
        print(f"\n--- Processing: {model_name} ---")
        
        print("Training model...")
        model_instance.fit(X_train)
        
        print("Generating reconstruction...")
        reconstruction = get_reconstruction(model_instance, full_series, WINDOW_SIZE, DEVICE)

        # Main Traces
        fig.add_trace(go.Scatter(y=full_series[:, 0], name='Original Signal', line=dict(color='#1f77b4')), row=row_num, col=1)
        fig.add_trace(go.Scatter(y=reconstruction[:, 0], name='Reconstructed Signal', line=dict(color='#ff7f0e', dash='dash')), row=row_num, col=1)

        # Highlight anomalies with vrect and a dummy trace for the legend
        anomaly_indices = np.where(full_labels == 1)[0]
        anomaly_spans = get_anomaly_spans(anomaly_indices)
        if anomaly_spans:
            fig.add_trace(go.Scatter(
                x=[None], y=[None], mode='markers',
                marker=dict(color='rgba(255, 215, 0, 0.6)', size=15, symbol='square'),
                name='Ground Truth Anomaly'
            ), row=row_num, col=1)
            for start, end in anomaly_spans:
                fig.add_vrect(x0=start, x1=end, fillcolor="gold", opacity=0.4, layer="below", line_width=0)
        
        # Mark train/test split with vline and a dummy trace for the legend
        fig.add_vline(x=train_test_split_idx, line_width=2, line_dash="dot", line_color="green")
        fig.add_trace(go.Scatter(
            x=[None], y=[None], mode='lines',
            line=dict(color='green', dash='dot', width=2),
            name='Train/Test Split'
        ), row=row_num, col=1)
        
        fig.update_yaxes(title_text="Value", row=row_num, col=1)
    
    fig.update_xaxes(title_text="Time Step", row=len(models_to_analyze), col=1)
    fig.update_layout(
        title_text=f'<b>Reconstruction Analysis on {DATASET_NAME}</b>',
        title_x=0.5,
        font=dict(family="Times New Roman", size=14),
        template='plotly_white',
        height=500 * len(models_to_analyze),
        width=1600,
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
    )

    output_dir = 'visualizations/reconstruction_comparison'
    os.makedirs(output_dir, exist_ok=True)
    save_path = os.path.join(output_dir, f'reconstruction_{DATASET_NAME}_plotly.png')
    pio.write_image(fig, save_path, scale=2)
    print(f"\n✅ Analysis complete. Plot saved to: {save_path}")


def get_anomaly_spans(anomaly_indices):
    """Helper function to find contiguous blocks of anomalies."""
    spans = []
    if len(anomaly_indices) == 0:
        return spans
    
    start_idx = anomaly_indices[0]
    for i in range(1, len(anomaly_indices)):
        if anomaly_indices[i] != anomaly_indices[i-1] + 1:
            spans.append((start_idx, anomaly_indices[i-1]))
            start_idx = anomaly_indices[i]
    spans.append((start_idx, anomaly_indices[-1]))
    return spans


if __name__ == '__main__':
    main() 