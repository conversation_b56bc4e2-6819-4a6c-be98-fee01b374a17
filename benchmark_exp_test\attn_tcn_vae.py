# -*- coding: utf-8 -*-
# Attention-based Temporal Convolutional Network Variational Autoencoder (Attn-TCN-VAE)
# A model combining TCN for temporal feature extraction and Attention for highlighting
# important features, all within a VAE framework.
#
# This file is structured to be compatible with the TSB-AD benchmark framework,
# similar to Lightweight_TS_Model.py.

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import sys
from torch.utils.data import DataLoader, TensorDataset
from torch.nn.utils import weight_norm

# --- TCN Library ---
# Removed external dependency. TCN is implemented locally below.

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector

from sklearn.preprocessing import MinMaxScaler


# ----------------------------------------------------
# 1. Model Definition
# ----------------------------------------------------
class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding=padding, dilation=dilation))
        self.chomp1 = lambda x: x[:, :, :-padding].contiguous()
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)

        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding=padding, dilation=dilation))
        self.chomp2 = lambda x: x[:, :, :-padding].contiguous()
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)

        self.net = nn.Sequential(self.conv1, self.relu1, self.dropout1,
                                 self.conv2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        self.init_weights()

    def init_weights(self):
        self.conv1.weight.data.normal_(0, 0.01)
        self.conv2.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)

    def forward(self, x):
        # Apply layers sequentially
        out = self.conv1(x)
        out = self.chomp1(out)
        out = self.relu1(out)
        out = self.dropout1(out)
        
        out = self.conv2(out)
        out = self.chomp2(out)
        out = self.relu2(out)
        out = self.dropout2(out)
        
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers += [TemporalBlock(in_channels, out_channels, kernel_size, stride=1, dilation=dilation_size,
                                     padding=(kernel_size-1) * dilation_size, dropout=dropout)]

        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

class Attention(nn.Module):
    """A simple self-attention mechanism."""
    def __init__(self, input_dim):
        super(Attention, self).__init__()
        self.W = nn.Parameter(torch.Tensor(input_dim, input_dim))
        self.b = nn.Parameter(torch.Tensor(input_dim))
        self.u = nn.Parameter(torch.Tensor(input_dim))
        nn.init.xavier_uniform_(self.W)
        nn.init.zeros_(self.b)
        # Use a 1D-compatible initialization for the context vector 'u'
        fan_in, _ = nn.init._calculate_fan_in_and_fan_out(self.W)
        bound = 1 / fan_in**0.5
        nn.init.uniform_(self.u, -bound, bound)

    def forward(self, x):
        # x shape: (batch_size, seq_len, input_dim)
        uit = torch.tanh(torch.matmul(x, self.W) + self.b)
        ait = torch.matmul(uit, self.u)
        a = F.softmax(ait, dim=1)
        output = x * a.unsqueeze(-1)
        output = torch.sum(output, dim=1)  # -> (batch_size, input_dim)
        return output

class AttnTcnVAE_Model(nn.Module):
    """The core Attn-TCN-VAE neural network architecture."""
    def __init__(self, input_dim, window_size, latent_dim, tcn_channels, tcn_kernel_size, tcn_dropout):
        super(AttnTcnVAE_Model, self).__init__()
        self.latent_dim = latent_dim
        self.window_size = window_size
        self.tcn_channels = tcn_channels
        self.input_dim = input_dim

        # === Encoder ===
        self.encoder_tcn = TemporalConvNet(
            num_inputs=input_dim,
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size,
            dropout=tcn_dropout
        )
        self.encoder_attention = Attention(input_dim=tcn_channels[-1])
        self.fc_mu = nn.Linear(tcn_channels[-1], latent_dim)
        self.fc_log_var = nn.Linear(tcn_channels[-1], latent_dim)

        # === Decoder ===
        self.decoder_fc = nn.Linear(latent_dim, window_size * tcn_channels[-1])
        self.decoder_tcn = TemporalConvNet(
            num_inputs=tcn_channels[-1],
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size,
            dropout=tcn_dropout
        )
        self.decoder_conv_out = nn.Conv1d(tcn_channels[-1], input_dim, kernel_size=1)

    def reparameterize(self, mu, log_var):
        std = torch.exp(0.5 * log_var)
        eps = torch.randn_like(std)
        return mu + eps * std

    def forward(self, x):
        # x shape: (batch_size, window_size, input_dim)
        x = x.permute(0, 2, 1)  # -> (batch, input_dim, window_size) for TCN

        # Encoder
        tcn_out = self.encoder_tcn(x)
        tcn_out = tcn_out.permute(0, 2, 1) # -> (batch, window_size, tcn_channels[-1])
        attention_out = self.encoder_attention(tcn_out) # -> (batch, tcn_channels[-1])

        mu = self.fc_mu(attention_out)
        log_var = self.fc_log_var(attention_out)
        z = self.reparameterize(mu, log_var)

        # Decoder
        dec_in = self.decoder_fc(z)
        dec_in = dec_in.view(-1, self.tcn_channels[-1], self.window_size)
        dec_tcn_out = self.decoder_tcn(dec_in)
        reconstructed = self.decoder_conv_out(dec_tcn_out)
        reconstructed = reconstructed.permute(0, 2, 1)
        reconstructed = torch.sigmoid(reconstructed) # Sigmoid for output scaled to [0,1]

        return reconstructed, mu, log_var


# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class AttnTcnVAE_AD(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        # Model hyperparameters
        self.window_size = HP.get('window_size', 128)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)
        self.beta = HP.get('beta', 1.0)
        
        # TCN parameters
        tcn_layers = HP.get('tcn_layers', 4)
        tcn_filters = HP.get('tcn_filters', 64)
        self.tcn_channels = [tcn_filters] * tcn_layers
        self.tcn_kernel_size = HP.get('tcn_kernel_size', 3)
        self.tcn_dropout = HP.get('tcn_dropout', 0.2)
        
        print(f"🔄 Initializing Attn-TCN-VAE Detector... (Device: {self.device})")
        
        self.ts_scaler = MinMaxScaler()
        self.score_scaler = MinMaxScaler()
        self.model = None

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def _vae_loss(self, recon, x, mu, log_var):
        recon_loss = F.mse_loss(recon, x, reduction='sum')
        kld_loss = -0.5 * torch.sum(1 + log_var - mu.pow(2) - log_var.exp())
        return recon_loss + self.beta * kld_loss

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        if self.model is None:
            self.model = AttnTcnVAE_Model(
                input_dim=input_dim, window_size=self.window_size, latent_dim=self.latent_dim,
                tcn_channels=self.tcn_channels, tcn_kernel_size=self.tcn_kernel_size,
                tcn_dropout=self.tcn_dropout
            ).to(self.device)
        
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        if len(windows) == 0:
            print("⚠️ Warning: Not enough data to create windows. Skipping training.")
            self.decision_scores_ = np.zeros(X.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.lr)

        self.model.train()
        for epoch in range(self.epochs):
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                recon, mu, log_var = self.model(batch_windows)
                loss = self._vae_loss(recon, batch_windows, mu, log_var)
                loss.backward()
                optimizer.step()

        self.decision_scores_ = self._compute_scores(X, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0:
            return np.zeros(X.shape[0])

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                recon, _, _ = self.model(batch_windows)
                errors = torch.mean((recon - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        window_scores = np.array(window_scores)
        scores_mapped = np.zeros(X.shape[0])
        counts = np.zeros(X.shape[0])
        for i, score in enumerate(window_scores):
            scores_mapped[i:i + self.window_size] += score
            counts[i:i + self.window_size] += 1
        
        scores_mapped[counts > 0] /= counts[counts > 0]
        scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

    def predict(self, X):
        return self.decision_function(X)


# ----------------------------------------------------
# 3. Standalone Test
# ----------------------------------------------------
def standalone_test():
    """
    Provides a standalone test for the Attn-TCN-VAE detector.
    """
    # Configuration
    DATA_PATH = '../../Datasets/public_dataset/001_NAB_id_1_Facility_tr_1007_1st_2014.csv'
    DATA_DIREC = os.path.dirname(DATA_PATH)

    # Hyperparameters for the test
    hp = {
        'window_size': 128,
        'epochs': 20,
        'lr': 0.001,
        'batch_size': 64,
        'latent_dim': 16,
        'beta': 1.0,
        'tcn_layers': 3,
        'tcn_filters': 32,
        'tcn_kernel_size': 3,
        'tcn_dropout': 0.1,
        'gpu': 0
    }

    # Load data
    from TSB_AD.utils.dataset import TSB_UCR_Dataset
    from T_AD.utils.slidingWindows import find_length, sliding_windows
    from TSB_AD.eval.metrics import score_recall_precision_w_threshold
    from TSB_AD.eval.plot import plot_detection_results

    print(f"📖 Loading data from {DATA_PATH}...")
    data = TSB_UCR_Dataset(DATA_PATH)
    train_data = data.train
    test_data = data.test
    label = data.label[len(train_data):]
    train_size = len(train_data)
    
    # Initialize and train the detector
    print("\n🛠️  Initializing and training the detector...")
    detector = AttnTcnVAE_AD(HP=hp, normalize=True)
    detector.fit(train_data)

    # Get anomaly scores
    print("\n🔍 Getting anomaly scores...")
    scores = detector.predict(test_data)
    
    # Evaluate
    print("\n📈 Evaluating results...")
    result, _ = score_recall_precision_w_threshold(scores, label, 0.1)
    print(result)

    # Plot
    print("\n🎨 Plotting results...")
    plot_detection_results(data, label, scores, 'Attn-TCN-VAE', DATA_DIREC, train_size)

    print(f"\n✅ Standalone test finished.")


if __name__ == '__main__':
    standalone_test() 