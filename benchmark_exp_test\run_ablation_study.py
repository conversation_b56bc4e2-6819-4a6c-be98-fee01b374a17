# -*- coding: utf-8 -*-
# run_ablation_study.py
#
# A script to run an ablation study on the HTA-AD model to demonstrate
# the effectiveness of its components (CNN, TCN, Downsampling).

import os
import sys
import pandas as pd
import random
import time
from tqdm import tqdm
import numpy as np
import json
import warnings

warnings.filterwarnings('ignore')

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- TSB-AD Imports ---
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.evaluation.metrics import get_metrics
from benchmark_exp.hta_ad import HTA_AD

# --- Dataset Loaders ---
class UnivariateDataset:
    def __init__(self, dataset_path):
        df = pd.read_csv(dataset_path).dropna()
        try:
            train_size_str = os.path.basename(dataset_path).split('_')[-3]
            self.train_size = int(train_size_str)
        except (ValueError, IndexError):
            self.train_size = int(len(df) * 0.7)

        self.data = df.iloc[:, 0:-1].values.astype(float)
        self.label = df.iloc[:, -1].astype(int).to_numpy()
        self.train = self.data[:self.train_size]
        self.name = os.path.basename(dataset_path).replace('.csv', '')

class MultivariateDataset:
    def __init__(self, dataset_path):
        df = pd.read_csv(dataset_path)
        
        self.data = df.iloc[:, :-1].values.astype(float)
        self.label = df.iloc[:, -1].astype(int).to_numpy()
        
        try:
            train_size_str = os.path.basename(dataset_path).split('_')[-3]
            self.train_size = int(train_size_str)
            self.train = self.data[:self.train_size]
        except (ValueError, IndexError):
            # Fallback for multivariate if train size cannot be inferred
            # We assume labels are present to distinguish train/test
            test_start_index = np.where(self.label == 1)[0]
            if len(test_start_index) > 0:
                self.train_size = test_start_index[0]
            else:
                self.train_size = len(df) # Assume all is training data if no anomalies
            self.train = self.data[:self.train_size]

        self.name = os.path.basename(dataset_path).replace('.csv', '')

# --- Configuration ---
UNIVARIATE_ROOT = 'Datasets/TSB-AD-U/'
MULTIVARIATE_ROOT = 'Datasets/TSB-AD-M/'
NUM_DATASETS_TO_TEST = 20
OUTPUT_DIR = 'ablation_results/'

# --- Model Ablation Definitions ---
BASE_PARAMS = {'window_size': 128, 'epochs': 20, 'lr': 1e-3, 'batch_size': 64, 'latent_dim': 16, 'tcn_channels': [16, 16], 'cnn_channels': 16, 'gpu': 0}

ABLATION_CONFIG = {
    'Base': {
        'params': {**BASE_PARAMS, 'downsample_stride': 4}
    },
    'ExperimentA': { # No CNN
        'params': {**BASE_PARAMS, 'downsample_stride': 4, 'ablation_mode': 'no_cnn'}
    },
    'ExperimentB': { # No TCN
        'params': {**BASE_PARAMS, 'downsample_stride': 4, 'ablation_mode': 'no_tcn'}
    },
    'ExperimentC': { # No Downsampling
        'params': {**BASE_PARAMS, 'downsample_stride': 1}
    }
}

def run_single_experiment(model_name, model_info, dataset, output_dir):
    try:
        train_data, full_data, label = dataset.train, dataset.data, dataset.label
        dataset_name = dataset.name

        print(f"    -> Running Config: {model_name}")
        start_time = time.time()
        
        model = HTA_AD(HP=model_info['params'])
        model.fit(train_data)
        
        scores = model.decision_function(full_data)
        runtime = time.time() - start_time
        
        min_len = min(len(scores), len(label))
        scores, label = scores[:min_len], label[:min_len]
        
        slidingWindow = find_length_rank(full_data[:min_len], rank=1)
        results = get_metrics(scores, label, slidingWindow=slidingWindow)
        results['runtime(s)'] = runtime
        
        metrics_df = pd.DataFrame([results])
        metrics_path = os.path.join(output_dir, "metrics.csv")
        metrics_df.to_csv(metrics_path, index=False)
        
        print(f"       ✅ Finished. Saved results to {output_dir}")
        return results

    except Exception as e:
        print(f"    ❌ Error running {model_name} on {dataset_name}: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_suite(dataset_type, dataset_root, dataset_loader, num_to_run):
    print(f"\n{'='*20} Running Ablation Suite for {dataset_type.upper()} Datasets {'='*20}")
    
    all_datasets = sorted([f for f in os.listdir(dataset_root) if f.endswith('.csv')])
        
    if not all_datasets:
        print(f"❌ No datasets found in {dataset_root}. Exiting.")
        return None

    selected_datasets = all_datasets[:num_to_run]
    print(f"🔬 Testing on first {len(selected_datasets)} datasets.")

    all_results = []
    
    for dataset_name in tqdm(selected_datasets, desc=f"{dataset_type.upper()} Progress"):
        dataset_path = os.path.join(dataset_root, dataset_name)
        
        print(f"\n--- Processing Dataset: {dataset_name} ---")
        
        for model_name, model_info in ABLATION_CONFIG.items():
            # Create a dedicated output directory for this experiment run
            run_output_dir = os.path.join(OUTPUT_DIR, model_name, dataset_type, dataset_name)
            os.makedirs(run_output_dir, exist_ok=True)
            
            metrics_path = os.path.join(run_output_dir, "metrics.csv")
            if os.path.exists(metrics_path):
                print(f"       ⏩ Skipping {model_name}, results already exist.")
                try:
                    res_df = pd.read_csv(metrics_path)
                    results = res_df.to_dict('records')[0]
                except Exception as e:
                    print(f"       ⚠️ Could not reload existing results for {model_name}: {e}")
                    results = None
            else:
                dataset = dataset_loader(dataset_path)
                results = run_single_experiment(model_name, model_info, dataset, run_output_dir)

            if results:
                results['model'] = model_name
                results['dataset'] = dataset_name
                all_results.append(results)
                
    if not all_results:
        print(f"\n❌ No results generated for {dataset_type}. Check for errors.")
        return None
        
    summary_df = pd.DataFrame(all_results)
    return summary_df

def main():
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # --- Run Univariate Suite ---
    uni_summary = run_suite('univariate', UNIVARIATE_ROOT, UnivariateDataset, NUM_DATASETS_TO_TEST)
    
    # --- Run Multivariate Suite ---
    multi_summary = run_suite('multivariate', MULTIVARIATE_ROOT, MultivariateDataset, NUM_DATASETS_TO_TEST)

    # --- Final Reports ---
    print("\n\n\n" + "="*60)
    print(" " * 20 + "ABLATION STUDY SUMMARY")
    print("="*60)

    if uni_summary is not None and not uni_summary.empty:
        avg_uni = uni_summary.drop(columns=['dataset']).groupby('model').mean()
        avg_uni = avg_uni.reindex(ABLATION_CONFIG.keys()) # Keep consistent order
        print("\n--- Average Performance on UNIVARIATE Datasets ---")
        print(avg_uni.to_string())
        summary_path = os.path.join(OUTPUT_DIR, 'univariate_summary.csv')
        avg_uni.to_csv(summary_path)
        print(f"\n✅ Univariate summary saved to {summary_path}")

    if multi_summary is not None and not multi_summary.empty:
        avg_multi = multi_summary.drop(columns=['dataset']).groupby('model').mean()
        avg_multi = avg_multi.reindex(ABLATION_CONFIG.keys()) # Keep consistent order
        print("\n--- Average Performance on MULTIVARIATE Datasets ---")
        print(avg_multi.to_string())
        summary_path = os.path.join(OUTPUT_DIR, 'multivariate_summary.csv')
        avg_multi.to_csv(summary_path)
        print(f"\n✅ Multivariate summary saved to {summary_path}")

    print(f"\n\n🎉 Ablation study finished!")
    print(f"📄 All detailed results saved in: {OUTPUT_DIR}")

if __name__ == '__main__':
    main() 