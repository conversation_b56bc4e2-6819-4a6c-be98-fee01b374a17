#!/usr/bin/env python
"""
批量测试脚本：在GPU1上运行所有TSB-AD-U数据集的LERN v2.0测试
"""

import os
import sys
import subprocess
import time
import pandas as pd
import glob
from pathlib import Path

# 设置GPU设备
os.environ["CUDA_VISIBLE_DEVICES"] = "1"

def get_all_datasets():
    """获取所有数据集文件"""
    dataset_dir = "../Datasets/TSB-AD-U/"
    csv_files = glob.glob(os.path.join(dataset_dir, "*.csv"))
    csv_files.sort()
    return csv_files

def run_single_dataset(csv_file, results_log):
    """运行单个数据集的测试"""
    filename = os.path.basename(csv_file)
    print(f"\n{'='*80}")
    print(f"🚀 开始测试数据集: {filename}")
    print(f"{'='*80}")
    
    start_time = time.time()
    
    # 构建命令
    cmd = [
        "python", "Run_LERN_Detector.py",
        "--filename", filename,
        "--data_direc", "../Datasets/TSB-AD-U/",
        "--AD_Name", "LERN_v2_GPU1"
    ]
    
    try:
        # 运行命令
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=1800  # 30分钟超时
        )
        
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {filename} 测试成功! 耗时: {elapsed_time:.1f}s")
            
            # 解析输出中的评估结果
            output_lines = result.stdout.split('\n')
            eval_result = None
            for line in output_lines:
                if "📊 评估结果:" in line:
                    try:
                        eval_str = line.split("📊 评估结果: ")[1]
                        eval_result = eval(eval_str)
                        break
                    except:
                        pass
            
            # 记录结果
            log_entry = {
                'dataset': filename,
                'status': 'SUCCESS',
                'time': f"{elapsed_time:.1f}s",
                'AUC-ROC': eval_result.get('AUC-ROC', 'N/A') if eval_result else 'N/A',
                'AUC-PR': eval_result.get('AUC-PR', 'N/A') if eval_result else 'N/A',
                'VUS-ROC': eval_result.get('VUS-ROC', 'N/A') if eval_result else 'N/A',
                'VUS-PR': eval_result.get('VUS-PR', 'N/A') if eval_result else 'N/A',
                'F1': eval_result.get('Standard-F1', 'N/A') if eval_result else 'N/A'
            }
            
        else:
            print(f"❌ {filename} 测试失败!")
            print(f"错误输出: {result.stderr[:500]}")
            
            log_entry = {
                'dataset': filename,
                'status': 'FAILED',
                'time': f"{elapsed_time:.1f}s",
                'error': result.stderr[:200] if result.stderr else 'Unknown error'
            }
    
    except subprocess.TimeoutExpired:
        elapsed_time = time.time() - start_time
        print(f"⏰ {filename} 测试超时! (30分钟)")
        
        log_entry = {
            'dataset': filename,
            'status': 'TIMEOUT',
            'time': f"{elapsed_time:.1f}s",
            'error': 'Timeout after 30 minutes'
        }
    
    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"💥 {filename} 测试异常: {str(e)}")
        
        log_entry = {
            'dataset': filename,
            'status': 'ERROR',
            'time': f"{elapsed_time:.1f}s",
            'error': str(e)
        }
    
    # 追加到结果日志
    results_log.append(log_entry)
    
    return log_entry

def main():
    print("🎯 LERN v2.0 批量测试 - GPU1")
    print("=" * 80)
    
    # 获取所有数据集
    datasets = get_all_datasets()
    print(f"📂 发现 {len(datasets)} 个数据集")
    
    # 创建结果日志
    results_log = []
    
    # 记录开始时间
    total_start_time = time.time()
    
    # 逐个测试数据集
    for i, dataset in enumerate(datasets, 1):
        print(f"\n进度: {i}/{len(datasets)}")
        
        result = run_single_dataset(dataset, results_log)
        
        # 每10个数据集保存一次中间结果
        if i % 10 == 0:
            df = pd.DataFrame(results_log)
            df.to_csv("lern_v2_gpu1_batch_results_partial.csv", index=False)
            print(f"📊 已保存前 {i} 个数据集的中间结果")
    
    # 计算总时间
    total_elapsed = time.time() - total_start_time
    
    # 保存最终结果
    df = pd.DataFrame(results_log)
    df.to_csv("lern_v2_gpu1_batch_results_final.csv", index=False)
    
    # 统计结果
    success_count = len([r for r in results_log if r['status'] == 'SUCCESS'])
    failed_count = len([r for r in results_log if r['status'] == 'FAILED'])
    timeout_count = len([r for r in results_log if r['status'] == 'TIMEOUT'])
    error_count = len([r for r in results_log if r['status'] == 'ERROR'])
    
    print(f"\n🏁 批量测试完成!")
    print(f"=" * 80)
    print(f"📊 总体统计:")
    print(f"   总数据集: {len(datasets)}")
    print(f"   成功: {success_count}")
    print(f"   失败: {failed_count}")
    print(f"   超时: {timeout_count}")
    print(f"   异常: {error_count}")
    print(f"   总耗时: {total_elapsed/3600:.1f} 小时")
    print(f"📁 结果已保存到: lern_v2_gpu1_batch_results_final.csv")
    
    # 显示成功数据集的性能统计
    successful_results = [r for r in results_log if r['status'] == 'SUCCESS' and 'AUC-ROC' in r]
    if successful_results:
        auc_rocs = [float(r['AUC-ROC']) for r in successful_results if r['AUC-ROC'] != 'N/A']
        if auc_rocs:
            print(f"\n📈 性能统计 (成功的数据集):")
            print(f"   平均 AUC-ROC: {sum(auc_rocs)/len(auc_rocs):.4f}")
            print(f"   最佳 AUC-ROC: {max(auc_rocs):.4f}")
            print(f"   最差 AUC-ROC: {min(auc_rocs):.4f}")

if __name__ == "__main__":
    main() 