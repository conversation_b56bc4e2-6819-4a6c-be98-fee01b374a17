import pandas as pd
import os
import time
from datetime import datetime
import numpy as np

def load_hta_ad_metrics(target_dir):
    """加载所有HTA_AD_metrics.csv文件"""
    all_metrics = []
    
    for dataset_dir in os.listdir(target_dir):
        dataset_path = os.path.join(target_dir, dataset_dir)
        if not os.path.isdir(dataset_path):
            continue
            
        metrics_file = os.path.join(dataset_path, 'HTA_AD_metrics.csv')
        if os.path.exists(metrics_file):
            try:
                df = pd.read_csv(metrics_file)
                if not df.empty:
                    metrics = df.iloc[0].to_dict()
                    metrics['dataset'] = dataset_dir
                    all_metrics.append(metrics)
            except Exception as e:
                continue
    
    if not all_metrics:
        return pd.DataFrame()

    return pd.DataFrame(all_metrics)

def print_summary(df):
    """打印实时更新的所有指标表格"""
    if df.empty:
        print("⏳ 等待实验结果...")
        return

    print(f"📊 HTA_AD 多变量异常检测实时结果 (共 {len(df)} 个数据集)")
    
    # 定义所有可能的指标列
    all_metric_cols = [
        'AUC-PR', 'AUC-ROC', 'PA-F1', 'Standard-F1', 'VUS-PR', 'VUS-ROC', 
        'Event-based-F1', 'R-based-F1', 'Affiliation-F', 'runtime(s)'
    ]
    
    # 筛选出DataFrame中实际存在的指标列
    numeric_cols = [col for col in all_metric_cols if col in df.columns and pd.api.types.is_numeric_dtype(df[col])]
    display_cols = ['dataset'] + numeric_cols
    
    display_df = df[display_cols].copy()

    # 对数据集名称进行排序
    display_df = display_df.sort_values(by='dataset').reset_index(drop=True)

    # 计算平均值行
    if len(display_df) > 0:
        mean_series = display_df[numeric_cols].mean()
        mean_series['dataset'] = '--- AVERAGE ---'
        mean_df = pd.DataFrame(mean_series).T
        
        # 创建分隔行
        separator_data = {col: '---' for col in display_cols}
        separator_df = pd.DataFrame([separator_data])
        
        # 合并
        display_df = pd.concat([display_df, separator_df, mean_df], ignore_index=True)
    
    # 格式化输出
    pd.set_option('display.max_rows', 500)
    pd.set_option('display.max_columns', 20)
    pd.set_option('display.width', 1000)
    pd.set_option('display.colheader_justify', 'center')
    pd.set_option('display.float_format', '{:.4f}'.format)
    
    # 动态调整分隔线长度
    table_string = display_df.to_string(index=False, na_rep='-')
    if table_string:
        line_length = len(table_string.split('\n')[0])
        print("=" * line_length)
        print(table_string)
        print("=" * line_length)

def monitor_results(target_dir, sleep_interval=10):
    """监控结果目录"""
    print(f"🔍 监控 HTA_AD 多变量实验结果: {target_dir}")
    print("按 Ctrl+C 停止监控\n")
    
    while True:
        try:
            # 清屏
            os.system('clear')
            
            # 加载指标
            df = load_hta_ad_metrics(target_dir)
            
            # 打印摘要
            print_summary(df)
            
            # 时间戳
            print(f"\n🕐 最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except KeyboardInterrupt:
            print("\n\n🛑 监控已停止")
            break
        except Exception as e:
            print(f"\n❌ 错误: {e}")
        
        # 等待
        time.sleep(sleep_interval)

if __name__ == '__main__':
    TARGET_DIR = 'hta_ad_multivariate_results'
    monitor_results(TARGET_DIR) 