# -*- coding: utf-8 -*-
# Temporal Attention TCN VAE (TemporalAttn-TCN-VAE)
# This model enhances the Attn-TCN-VAE by incorporating a time-aware 
# attention mechanism (Exponential Decay Attention).

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import sys
from torch.utils.data import DataLoader, TensorDataset
from torch.nn.utils import weight_norm
from sklearn.preprocessing import MinMaxScaler

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector

# ----------------------------------------------------
# 1. Model Definition
# ----------------------------------------------------

# --- TCN Components (Copied from attn_tcn_vae.py for self-containment) ---
class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding=padding, dilation=dilation))
        self.chomp1 = lambda x: x[:, :, :-padding].contiguous()
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)

        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding=padding, dilation=dilation))
        self.chomp2 = lambda x: x[:, :, :-padding].contiguous()
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)

        self.net = nn.Sequential(self.conv1, self.relu1, self.dropout1,
                                 self.conv2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        self.init_weights()

    def init_weights(self):
        self.conv1.weight.data.normal_(0, 0.01)
        self.conv2.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)

    def forward(self, x):
        out = self.conv1(x)
        out = self.chomp1(out)
        out = self.relu1(out)
        out = self.dropout1(out)
        out = self.conv2(out)
        out = self.chomp2(out)
        out = self.relu2(out)
        out = self.dropout2(out)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers += [TemporalBlock(in_channels, out_channels, kernel_size, stride=1, dilation=dilation_size,
                                     padding=(kernel_size-1) * dilation_size, dropout=dropout)]
        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

# --- Temporal Attention Component ---
class ExponentialDecayAttention(nn.Module):
    def __init__(self, input_dim, temperature=None):
        super().__init__()
        if temperature is None:
            temperature = np.sqrt(input_dim)
        self.temperature = temperature
        self.decay_rate = nn.Parameter(torch.tensor([0.1]), requires_grad=True)
        self.query = nn.Linear(input_dim, input_dim)
        self.key = nn.Linear(input_dim, input_dim)
        self.value = nn.Linear(input_dim, input_dim)

    def forward(self, x):
        q = self.query(x)
        k = self.key(x)
        v = self.value(x)
        
        attn = torch.matmul(q, k.transpose(-2, -1)) / self.temperature

        seq_len = attn.size(-1)
        device = attn.device
        indices = torch.arange(seq_len, device=device).float()
        dist_matrix = torch.abs(indices.unsqueeze(0) - indices.unsqueeze(1))
        
        # Use relu to ensure learned decay rate is non-negative
        decay_mask = torch.exp(-torch.relu(self.decay_rate) * dist_matrix)
        
        attn = attn * decay_mask
        attn = F.softmax(attn, dim=-1)
        
        output = torch.matmul(attn, v)
        return output

class TemporalAttn_Model(nn.Module):
    """The core TemporalAttn-TCN-VAE neural network architecture."""
    def __init__(self, input_dim, window_size, latent_dim, tcn_channels, tcn_kernel_size, tcn_dropout):
        super(TemporalAttn_Model, self).__init__()
        self.latent_dim = latent_dim
        self.window_size = window_size
        self.tcn_channels = tcn_channels
        self.input_dim = input_dim

        # Encoder
        self.encoder_tcn = TemporalConvNet(input_dim, tcn_channels, tcn_kernel_size, dropout=tcn_dropout)
        self.encoder_attention = ExponentialDecayAttention(input_dim=tcn_channels[-1])
        self.fc_mu = nn.Linear(tcn_channels[-1], latent_dim)
        self.fc_log_var = nn.Linear(tcn_channels[-1], latent_dim)

        # Decoder
        self.decoder_fc = nn.Linear(latent_dim, window_size * tcn_channels[-1])
        self.decoder_tcn = TemporalConvNet(tcn_channels[-1], tcn_channels, tcn_kernel_size, dropout=tcn_dropout)
        self.decoder_conv_out = nn.Conv1d(tcn_channels[-1], input_dim, kernel_size=1)

    def reparameterize(self, mu, log_var):
        std = torch.exp(0.5 * log_var)
        eps = torch.randn_like(std)
        return mu + eps * std

    def forward(self, x):
        x_perm = x.permute(0, 2, 1)

        # Encoder
        tcn_out = self.encoder_tcn(x_perm).permute(0, 2, 1)
        attention_out = self.encoder_attention(tcn_out)
        
        # We need to aggregate the sequence from attention before passing to VAE
        # Let's use the last time step's output for simplicity
        attention_last_step = attention_out[:, -1, :]
        
        mu = self.fc_mu(attention_last_step)
        log_var = self.fc_log_var(attention_last_step)
        z = self.reparameterize(mu, log_var)

        # Decoder
        dec_in = self.decoder_fc(z).view(-1, self.tcn_channels[-1], self.window_size)
        dec_tcn_out = self.decoder_tcn(dec_in)
        reconstructed = self.decoder_conv_out(dec_tcn_out).permute(0, 2, 1)
        reconstructed = torch.sigmoid(reconstructed)

        return reconstructed, mu, log_var

# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class TemporalAttn_TCN_VAE(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        # Hyperparameters
        self.window_size = HP.get('window_size', 128)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)
        self.beta = HP.get('beta', 1.0)
        tcn_layers = HP.get('tcn_layers', 4)
        tcn_filters = HP.get('tcn_filters', 64)
        self.tcn_channels = [tcn_filters] * tcn_layers
        self.tcn_kernel_size = HP.get('tcn_kernel_size', 3)
        self.tcn_dropout = HP.get('tcn_dropout', 0.2)
        
        self.ts_scaler = MinMaxScaler()
        self.score_scaler = MinMaxScaler()
        self.model = None

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def _vae_loss(self, recon, x, mu, log_var):
        recon_loss = F.mse_loss(recon, x, reduction='sum')
        kld_loss = -0.5 * torch.sum(1 + log_var - mu.pow(2) - log_var.exp())
        return recon_loss + self.beta * kld_loss

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        if self.model is None:
            self.model = TemporalAttn_Model(
                input_dim=input_dim, window_size=self.window_size, latent_dim=self.latent_dim,
                tcn_channels=self.tcn_channels, tcn_kernel_size=self.tcn_kernel_size,
                tcn_dropout=self.tcn_dropout
            ).to(self.device)
        
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        if len(windows) == 0: return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.lr)

        self.model.train()
        for epoch in range(self.epochs):
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                recon, mu, log_var = self.model(batch_windows)
                loss = self._vae_loss(recon, batch_windows, mu, log_var)
                loss.backward()
                optimizer.step()
        return self

    def _compute_scores(self, X, fit_scaler=False):
        if self.normalize:
            X = self.ts_scaler.transform(X)
        windows = self._create_windows(X)
        if len(windows) == 0: return np.zeros(X.shape[0])

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                recon, _, _ = self.model(batch_windows)
                error = torch.mean((recon - batch_windows) ** 2, dim=(1, 2))
                scores.extend(error.cpu().numpy())
        
        point_scores = np.zeros(X.shape[0])
        for i, score in enumerate(scores):
            point_scores[i:i + self.window_size] = np.maximum(point_scores[i:i + self.window_size], score)

        if fit_scaler:
            self.score_scaler.fit(point_scores.reshape(-1, 1))
        return self.score_scaler.transform(point_scores.reshape(-1, 1)).ravel()

    def decision_function(self, X):
        train_scores = self._compute_scores(X, fit_scaler=True)
        self.decision_scores_ = train_scores
        return self.decision_scores_

    def predict(self, X):
        return self.decision_function(X) 