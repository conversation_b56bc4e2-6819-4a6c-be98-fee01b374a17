#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ECG心电图数据的简洁可视化 - 回到简化版风格
使用SVDB心电图数据展示HTA-AD的三个核心实验
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
import warnings
import pandas as pd

# Ensure TSB_AD module can be imported
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD

warnings.filterwarnings('ignore')

# 简洁的绘图参数 - 与simplified版本保持一致
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 13
plt.rcParams['figure.dpi'] = 300

def load_ecg_data():
    """加载ECG心电图数据"""
    print("🫀 Loading ECG (Electrocardiogram) Data...")
    
    # 使用SVDB心电图数据 - 有清晰的心跳模式
    ecg_data_path = "../Datasets/TSB-AD-U/TSB-AD-U/251_SVDB_id_15_Medical_tr_5421_1st_5521.csv"
    
    try:
        df = pd.read_csv(ecg_data_path)
        data = df['Data'].values.astype(np.float32)
        labels = df['Label'].values.astype(int)
        train_size = 5421  # 从文件名解析
        
        print(f"  ✅ ECG数据加载成功: {len(data)} points")
        print(f"  💓 心跳异常: {labels.sum()} ({labels.sum()/len(labels):.1%})")
        print(f"  📊 信号范围: {data.min():.3f} - {data.max():.3f} mV")
        print(f"  🎯 训练长度: {train_size}, 异常开始: {train_size+100}")
        
        return data, labels, train_size
        
    except Exception as e:
        print(f"❌ 加载ECG数据失败: {e}")
        return None, None, None

def experiment_1_cnn_ecg():
    """实验一：CNN特征提取 - ECG心电图版本"""
    print("\n" + "="*60)
    print("🔍 Experiment 1: CNN Feature Extraction on ECG Data")
    print("="*60)
    
    data, labels, train_size = load_ecg_data()
    if data is None:
        return
    
    # 选择一个有清晰ECG模式的片段 (包含P波、QRS波群、T波)
    segment_start = 1000  # 跳过开始的噪声
    segment_length = 100  # 100个采样点，约包含1-2个心跳周期
    
    ecg_segment = data[segment_start:segment_start + segment_length]
    
    print(f"🎨 Analyzing ECG heartbeat patterns...")
    print(f"   Selected segment: {segment_start} to {segment_start + segment_length}")
    print(f"   ECG amplitude range: {ecg_segment.min():.3f} to {ecg_segment.max():.3f} mV")
    
    # 模拟CNN下采样（2:1压缩）
    # 使用平均池化来模拟CNN的特征提取
    compressed_length = segment_length // 2
    ecg_compressed = []
    
    for i in range(compressed_length):
        # 平均池化 - 每2个点压缩为1个点
        start_idx = i * 2
        end_idx = min(start_idx + 2, segment_length)
        avg_value = np.mean(ecg_segment[start_idx:end_idx])
        ecg_compressed.append(avg_value)
    
    ecg_compressed = np.array(ecg_compressed)
    
    print(f"✅ ECG heartbeat compression: {segment_length}→{compressed_length} (2:1)")
    print(f"✅ Preserved ECG waveform characteristics")
    
    # 创建可视化
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))
    
    # 左图：原始ECG信号
    time_orig = np.arange(segment_length)
    ax1.plot(time_orig, ecg_segment, color='#1f77b4', linewidth=2, label='Original ECG Signal', marker='o', markersize=4)
    ax1.set_title('Original ECG Heartbeat Pattern', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Time Steps', fontsize=12)
    ax1.set_ylabel('ECG Amplitude (mV)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 右图：CNN压缩后的信号
    time_comp = np.arange(compressed_length) * 2  # 调整时间轴对应
    ax2.plot(time_comp, ecg_compressed, color='#ff7f0e', linewidth=2.5, label='CNN Compressed', marker='s', markersize=5)
    ax2.set_title('CNN Compressed ECG Features', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Time Steps', fontsize=12)
    ax2.set_ylabel('Compressed Amplitude', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 添加说明文本
    fig.suptitle('📊 CNN Processing ECG Data: 100→50 points', fontsize=16, fontweight='bold', y=0.95)
    
    plt.tight_layout()
    plt.savefig('cnn_ecg.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📁 Saved: cnn_ecg.png")

def experiment_2_tcn_ecg():
    """实验二：TCN感受野分析 - 理论分析，与之前保持一致"""
    print("\n" + "="*60)
    print("📡 Experiment 2: TCN Receptive Field Analysis")
    print("="*60)
    
    # 这个实验主要是理论分析，不依赖具体数据
    print("🔬 Analyzing TCN vs Standard Convolution receptive fields...")
    
    # TCN参数
    num_layers = 3
    kernel_size = 3
    tcn_receptive_field = 1
    
    # 计算TCN感受野
    for layer in range(num_layers):
        dilation = 2 ** layer
        tcn_receptive_field += dilation * (kernel_size - 1)
        print(f"   Layer {layer+1}: dilation={dilation}, cumulative receptive field={tcn_receptive_field}")
    
    # 标准卷积感受野
    std_receptive_field = 1 + num_layers * (kernel_size - 1)
    
    print(f"\n📊 Final Results:")
    print(f"   🔸 TCN Receptive Field: {tcn_receptive_field}")
    print(f"   🔸 Standard CNN Receptive Field: {std_receptive_field}")
    print(f"   🔸 TCN Advantage: {tcn_receptive_field/std_receptive_field:.1f}x larger")
    
    # 可视化感受野
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # TCN感受野可视化
    tcn_positions = [0, 2, 6, 14]  # 1, 1+2, 1+2+4, 1+2+4+8
    tcn_colors = ['red', 'orange', 'blue', 'green']
    
    for i, (pos, color) in enumerate(zip(tcn_positions, tcn_colors)):
        dilation = 2 ** i if i > 0 else 1
        width = (kernel_size - 1) * dilation + 1 if i > 0 else 1
        ax1.barh(i, width, left=pos, color=color, alpha=0.7, 
                label=f'Layer {i+1} (d={dilation})')
    
    ax1.set_title('🚀 TCN Dilated Convolution Receptive Field', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Time Steps', fontsize=12)
    ax1.set_ylabel('Layer', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0, tcn_receptive_field + 2)
    
    # 标准卷积感受野可视化
    std_positions = [0, 2, 4]
    std_colors = ['lightblue', 'lightgreen', 'lightcoral']
    
    for i, (pos, color) in enumerate(zip(std_positions, std_colors)):
        ax2.barh(i, kernel_size, left=pos, color=color, alpha=0.7,
                label=f'Layer {i+1}')
    
    ax2.set_title('📺 Standard Convolution Receptive Field', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Time Steps', fontsize=12)
    ax2.set_ylabel('Layer', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, tcn_receptive_field + 2)
    
    plt.tight_layout()
    plt.savefig('tcn_ecg.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Final Receptive Field: TCN={tcn_receptive_field}, Standard={std_receptive_field}")
    print(f"📁 Saved: tcn_ecg.png")

def experiment_3_reconstruction_ecg():
    """实验三：重构误差分析 - ECG异常检测"""
    print("\n" + "="*60)
    print("🎯 Experiment 3: Reconstruction Error Analysis on ECG")
    print("="*60)
    
    data, labels, train_size = load_ecg_data()
    if data is None:
        return
    
    print("🔄 Initializing HTA-AD for ECG Analysis...")
    
    # 使用简单可靠的参数
    HP = {
        'gpu': 0,
        'window_size': 100,
        'epochs': 10,  # 减少训练时间
        'lr': 1e-3,
        'batch_size': 64,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2,
        'ablation_mode': None
    }
    
    model = HTA_AD(HP=HP, normalize=True)
    
    # 选择合适的训练数据量（避免过长的训练时间）
    train_subset_size = min(2000, train_size)  # 最多用2000个点训练
    train_data = data[:train_subset_size].reshape(-1, 1)
    
    print(f"  📏 Training on {train_subset_size} ECG samples...")
    model.fit(train_data)
    print("  ✅ Training completed")
    
    # 选择一个包含异常的测试窗口
    test_start = train_size + 100  # 异常开始后一点
    test_length = 200  # 测试200个点
    test_end = test_start + test_length
    
    test_segment = data[test_start:test_end].reshape(-1, 1)
    test_labels = labels[test_start:test_end]
    
    print(f"  🧪 Testing on segment {test_start}-{test_end}")
    print(f"  🚨 Test segment anomalies: {test_labels.sum()}/{len(test_labels)} ({test_labels.sum()/len(test_labels):.1%})")
    
    # 获取异常分数
    all_scores = model.decision_function(data.reshape(-1, 1))
    test_scores = all_scores[test_start:test_end]
    
    # 计算正常和异常的平均分数
    normal_mask = test_labels == 0
    anomaly_mask = test_labels == 1
    
    if normal_mask.sum() > 0 and anomaly_mask.sum() > 0:
        normal_score = test_scores[normal_mask].mean()
        anomaly_score = test_scores[anomaly_mask].mean()
        error_amplification = anomaly_score / normal_score if normal_score != 0 else float('inf')
        
        print(f"  📊 Normal ECG Score: {normal_score:.4f}")
        print(f"  🚨 Anomaly ECG Score: {anomaly_score:.4f}")
        print(f"  📈 Error Amplification: {error_amplification:.1f}x")
    else:
        normal_score = test_scores.mean()
        anomaly_score = test_scores.max()
        error_amplification = 1.0
        print(f"  ⚠️ Limited anomalies in test segment")
        print(f"  📊 Average Score: {normal_score:.4f}")
    
    # 创建可视化
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 10), sharex=True)
    
    time_steps = np.arange(test_length)
    
    # 子图1：原始ECG信号
    ax1.plot(time_steps, test_segment.flatten(), color='#1f77b4', linewidth=1.5, label='ECG Signal')
    
    # 标记异常点
    anomaly_indices = np.where(test_labels == 1)[0]
    if len(anomaly_indices) > 0:
        ax1.scatter(anomaly_indices, test_segment.flatten()[anomaly_indices], 
                   color='red', marker='o', s=30, zorder=5, 
                   label=f'ECG Anomalies ({len(anomaly_indices)})', alpha=0.8)
    
    ax1.set_title('🫀 ECG Signal with Detected Anomalies', fontsize=14, fontweight='bold')
    ax1.set_ylabel('ECG Amplitude (mV)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2：HTA-AD异常分数
    ax2.plot(time_steps, test_scores, color='#ff7f0e', linewidth=2, label='HTA-AD Anomaly Score')
    
    # 如果有明显的阈值
    if len(anomaly_indices) > 0:
        threshold = np.percentile(test_scores, 85)  # 使用85%分位数作为阈值
        ax2.axhline(y=threshold, color='red', linestyle='--', linewidth=2, 
                   label=f'Detection Threshold', alpha=0.7)
        
        # 填充超过阈值的区域
        ax2.fill_between(time_steps, test_scores, threshold, 
                        where=test_scores >= threshold, 
                        color='red', alpha=0.2, interpolate=True)
    
    ax2.set_title('🔍 HTA-AD Anomaly Detection Scores', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Anomaly Score', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 子图3：真实标签
    ax3.fill_between(time_steps, 0, test_labels, step='mid', color='red', alpha=0.6,
                     label='Ground Truth Anomalies')
    ax3.set_title('🎯 Ground Truth ECG Anomaly Labels', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Time Steps', fontsize=12)
    ax3.set_ylabel('Anomaly', fontsize=12)
    ax3.set_ylim(-0.1, 1.1)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('reconstruction_ecg.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Normal Score: {normal_score:.4f}")
    print(f"✅ Anomaly Score: {anomaly_score:.4f}")
    print(f"✅ Error Amplification: {error_amplification:.1f}x")
    print(f"📁 Saved: reconstruction_ecg.png")

if __name__ == "__main__":
    print("="*80)
    print("🚀 ECG Heartbeat Anomaly Detection - Simple Visualization")
    print("="*80)
    
    # 运行三个实验
    experiment_1_cnn_ecg()
    experiment_2_tcn_ecg() 
    experiment_3_reconstruction_ecg()
    
    print("\n" + "="*80)
    print("✅ All ECG visualizations completed!")
    print("📁 Generated files:")
    print("   📊 cnn_ecg.png")
    print("   📊 tcn_ecg.png") 
    print("   📊 reconstruction_ecg.png")
    print("="*80) 
# -*- coding: utf-8 -*-
"""
ECG心电图数据的简洁可视化 - 回到简化版风格
使用SVDB心电图数据展示HTA-AD的三个核心实验
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
import warnings
import pandas as pd

# Ensure TSB_AD module can be imported
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD

warnings.filterwarnings('ignore')

# 简洁的绘图参数 - 与simplified版本保持一致
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 13
plt.rcParams['figure.dpi'] = 300

def load_ecg_data():
    """加载ECG心电图数据"""
    print("🫀 Loading ECG (Electrocardiogram) Data...")
    
    # 使用SVDB心电图数据 - 有清晰的心跳模式
    ecg_data_path = "../Datasets/TSB-AD-U/TSB-AD-U/251_SVDB_id_15_Medical_tr_5421_1st_5521.csv"
    
    try:
        df = pd.read_csv(ecg_data_path)
        data = df['Data'].values.astype(np.float32)
        labels = df['Label'].values.astype(int)
        train_size = 5421  # 从文件名解析
        
        print(f"  ✅ ECG数据加载成功: {len(data)} points")
        print(f"  💓 心跳异常: {labels.sum()} ({labels.sum()/len(labels):.1%})")
        print(f"  📊 信号范围: {data.min():.3f} - {data.max():.3f} mV")
        print(f"  🎯 训练长度: {train_size}, 异常开始: {train_size+100}")
        
        return data, labels, train_size
        
    except Exception as e:
        print(f"❌ 加载ECG数据失败: {e}")
        return None, None, None

def experiment_1_cnn_ecg():
    """实验一：CNN特征提取 - ECG心电图版本"""
    print("\n" + "="*60)
    print("🔍 Experiment 1: CNN Feature Extraction on ECG Data")
    print("="*60)
    
    data, labels, train_size = load_ecg_data()
    if data is None:
        return
    
    # 选择一个有清晰ECG模式的片段 (包含P波、QRS波群、T波)
    segment_start = 1000  # 跳过开始的噪声
    segment_length = 100  # 100个采样点，约包含1-2个心跳周期
    
    ecg_segment = data[segment_start:segment_start + segment_length]
    
    print(f"🎨 Analyzing ECG heartbeat patterns...")
    print(f"   Selected segment: {segment_start} to {segment_start + segment_length}")
    print(f"   ECG amplitude range: {ecg_segment.min():.3f} to {ecg_segment.max():.3f} mV")
    
    # 模拟CNN下采样（2:1压缩）
    # 使用平均池化来模拟CNN的特征提取
    compressed_length = segment_length // 2
    ecg_compressed = []
    
    for i in range(compressed_length):
        # 平均池化 - 每2个点压缩为1个点
        start_idx = i * 2
        end_idx = min(start_idx + 2, segment_length)
        avg_value = np.mean(ecg_segment[start_idx:end_idx])
        ecg_compressed.append(avg_value)
    
    ecg_compressed = np.array(ecg_compressed)
    
    print(f"✅ ECG heartbeat compression: {segment_length}→{compressed_length} (2:1)")
    print(f"✅ Preserved ECG waveform characteristics")
    
    # 创建可视化
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))
    
    # 左图：原始ECG信号
    time_orig = np.arange(segment_length)
    ax1.plot(time_orig, ecg_segment, color='#1f77b4', linewidth=2, label='Original ECG Signal', marker='o', markersize=4)
    ax1.set_title('Original ECG Heartbeat Pattern', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Time Steps', fontsize=12)
    ax1.set_ylabel('ECG Amplitude (mV)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 右图：CNN压缩后的信号
    time_comp = np.arange(compressed_length) * 2  # 调整时间轴对应
    ax2.plot(time_comp, ecg_compressed, color='#ff7f0e', linewidth=2.5, label='CNN Compressed', marker='s', markersize=5)
    ax2.set_title('CNN Compressed ECG Features', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Time Steps', fontsize=12)
    ax2.set_ylabel('Compressed Amplitude', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 添加说明文本
    fig.suptitle('📊 CNN Processing ECG Data: 100→50 points', fontsize=16, fontweight='bold', y=0.95)
    
    plt.tight_layout()
    plt.savefig('cnn_ecg.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📁 Saved: cnn_ecg.png")

def experiment_2_tcn_ecg():
    """实验二：TCN感受野分析 - 理论分析，与之前保持一致"""
    print("\n" + "="*60)
    print("📡 Experiment 2: TCN Receptive Field Analysis")
    print("="*60)
    
    # 这个实验主要是理论分析，不依赖具体数据
    print("🔬 Analyzing TCN vs Standard Convolution receptive fields...")
    
    # TCN参数
    num_layers = 3
    kernel_size = 3
    tcn_receptive_field = 1
    
    # 计算TCN感受野
    for layer in range(num_layers):
        dilation = 2 ** layer
        tcn_receptive_field += dilation * (kernel_size - 1)
        print(f"   Layer {layer+1}: dilation={dilation}, cumulative receptive field={tcn_receptive_field}")
    
    # 标准卷积感受野
    std_receptive_field = 1 + num_layers * (kernel_size - 1)
    
    print(f"\n📊 Final Results:")
    print(f"   🔸 TCN Receptive Field: {tcn_receptive_field}")
    print(f"   🔸 Standard CNN Receptive Field: {std_receptive_field}")
    print(f"   🔸 TCN Advantage: {tcn_receptive_field/std_receptive_field:.1f}x larger")
    
    # 可视化感受野
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # TCN感受野可视化
    tcn_positions = [0, 2, 6, 14]  # 1, 1+2, 1+2+4, 1+2+4+8
    tcn_colors = ['red', 'orange', 'blue', 'green']
    
    for i, (pos, color) in enumerate(zip(tcn_positions, tcn_colors)):
        dilation = 2 ** i if i > 0 else 1
        width = (kernel_size - 1) * dilation + 1 if i > 0 else 1
        ax1.barh(i, width, left=pos, color=color, alpha=0.7, 
                label=f'Layer {i+1} (d={dilation})')
    
    ax1.set_title('🚀 TCN Dilated Convolution Receptive Field', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Time Steps', fontsize=12)
    ax1.set_ylabel('Layer', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0, tcn_receptive_field + 2)
    
    # 标准卷积感受野可视化
    std_positions = [0, 2, 4]
    std_colors = ['lightblue', 'lightgreen', 'lightcoral']
    
    for i, (pos, color) in enumerate(zip(std_positions, std_colors)):
        ax2.barh(i, kernel_size, left=pos, color=color, alpha=0.7,
                label=f'Layer {i+1}')
    
    ax2.set_title('📺 Standard Convolution Receptive Field', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Time Steps', fontsize=12)
    ax2.set_ylabel('Layer', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, tcn_receptive_field + 2)
    
    plt.tight_layout()
    plt.savefig('tcn_ecg.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Final Receptive Field: TCN={tcn_receptive_field}, Standard={std_receptive_field}")
    print(f"📁 Saved: tcn_ecg.png")

def experiment_3_reconstruction_ecg():
    """实验三：重构误差分析 - ECG异常检测"""
    print("\n" + "="*60)
    print("🎯 Experiment 3: Reconstruction Error Analysis on ECG")
    print("="*60)
    
    data, labels, train_size = load_ecg_data()
    if data is None:
        return
    
    print("🔄 Initializing HTA-AD for ECG Analysis...")
    
    # 使用简单可靠的参数
    HP = {
        'gpu': 0,
        'window_size': 100,
        'epochs': 10,  # 减少训练时间
        'lr': 1e-3,
        'batch_size': 64,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2,
        'ablation_mode': None
    }
    
    model = HTA_AD(HP=HP, normalize=True)
    
    # 选择合适的训练数据量（避免过长的训练时间）
    train_subset_size = min(2000, train_size)  # 最多用2000个点训练
    train_data = data[:train_subset_size].reshape(-1, 1)
    
    print(f"  📏 Training on {train_subset_size} ECG samples...")
    model.fit(train_data)
    print("  ✅ Training completed")
    
    # 选择一个包含异常的测试窗口
    test_start = train_size + 100  # 异常开始后一点
    test_length = 200  # 测试200个点
    test_end = test_start + test_length
    
    test_segment = data[test_start:test_end].reshape(-1, 1)
    test_labels = labels[test_start:test_end]
    
    print(f"  🧪 Testing on segment {test_start}-{test_end}")
    print(f"  🚨 Test segment anomalies: {test_labels.sum()}/{len(test_labels)} ({test_labels.sum()/len(test_labels):.1%})")
    
    # 获取异常分数
    all_scores = model.decision_function(data.reshape(-1, 1))
    test_scores = all_scores[test_start:test_end]
    
    # 计算正常和异常的平均分数
    normal_mask = test_labels == 0
    anomaly_mask = test_labels == 1
    
    if normal_mask.sum() > 0 and anomaly_mask.sum() > 0:
        normal_score = test_scores[normal_mask].mean()
        anomaly_score = test_scores[anomaly_mask].mean()
        error_amplification = anomaly_score / normal_score if normal_score != 0 else float('inf')
        
        print(f"  📊 Normal ECG Score: {normal_score:.4f}")
        print(f"  🚨 Anomaly ECG Score: {anomaly_score:.4f}")
        print(f"  📈 Error Amplification: {error_amplification:.1f}x")
    else:
        normal_score = test_scores.mean()
        anomaly_score = test_scores.max()
        error_amplification = 1.0
        print(f"  ⚠️ Limited anomalies in test segment")
        print(f"  📊 Average Score: {normal_score:.4f}")
    
    # 创建可视化
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 10), sharex=True)
    
    time_steps = np.arange(test_length)
    
    # 子图1：原始ECG信号
    ax1.plot(time_steps, test_segment.flatten(), color='#1f77b4', linewidth=1.5, label='ECG Signal')
    
    # 标记异常点
    anomaly_indices = np.where(test_labels == 1)[0]
    if len(anomaly_indices) > 0:
        ax1.scatter(anomaly_indices, test_segment.flatten()[anomaly_indices], 
                   color='red', marker='o', s=30, zorder=5, 
                   label=f'ECG Anomalies ({len(anomaly_indices)})', alpha=0.8)
    
    ax1.set_title('🫀 ECG Signal with Detected Anomalies', fontsize=14, fontweight='bold')
    ax1.set_ylabel('ECG Amplitude (mV)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2：HTA-AD异常分数
    ax2.plot(time_steps, test_scores, color='#ff7f0e', linewidth=2, label='HTA-AD Anomaly Score')
    
    # 如果有明显的阈值
    if len(anomaly_indices) > 0:
        threshold = np.percentile(test_scores, 85)  # 使用85%分位数作为阈值
        ax2.axhline(y=threshold, color='red', linestyle='--', linewidth=2, 
                   label=f'Detection Threshold', alpha=0.7)
        
        # 填充超过阈值的区域
        ax2.fill_between(time_steps, test_scores, threshold, 
                        where=test_scores >= threshold, 
                        color='red', alpha=0.2, interpolate=True)
    
    ax2.set_title('🔍 HTA-AD Anomaly Detection Scores', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Anomaly Score', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 子图3：真实标签
    ax3.fill_between(time_steps, 0, test_labels, step='mid', color='red', alpha=0.6,
                     label='Ground Truth Anomalies')
    ax3.set_title('🎯 Ground Truth ECG Anomaly Labels', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Time Steps', fontsize=12)
    ax3.set_ylabel('Anomaly', fontsize=12)
    ax3.set_ylim(-0.1, 1.1)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('reconstruction_ecg.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Normal Score: {normal_score:.4f}")
    print(f"✅ Anomaly Score: {anomaly_score:.4f}")
    print(f"✅ Error Amplification: {error_amplification:.1f}x")
    print(f"📁 Saved: reconstruction_ecg.png")

if __name__ == "__main__":
    print("="*80)
    print("🚀 ECG Heartbeat Anomaly Detection - Simple Visualization")
    print("="*80)
    
    # 运行三个实验
    experiment_1_cnn_ecg()
    experiment_2_tcn_ecg() 
    experiment_3_reconstruction_ecg()
    
    print("\n" + "="*80)
    print("✅ All ECG visualizations completed!")
    print("📁 Generated files:")
    print("   📊 cnn_ecg.png")
    print("   📊 tcn_ecg.png") 
    print("   📊 reconstruction_ecg.png")
    print("="*80) 