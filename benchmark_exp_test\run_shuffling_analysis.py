# -*- coding: utf-8 -*-
import os
import sys
import pandas as pd
import numpy as np
import torch
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# --- Project Root Addition ---
# Add the project root to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- TSB-AD Imports ---
from TSB_AD.models.AnomalyTransformer import AnomalyTransformer
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.utils.slidingWindows import find_length_rank
from benchmark_exp.hta_ad import HTA_AD

# Set Plotly default template
pio.templates.default = "seaborn"

def shuffle_training_data(X_train, shuffle_ratio):
    """
    Shuffles a specified ratio of the training data.
    A shuffle_ratio of 0.0 means no shuffling.
    A shuffle_ratio of 1.0 means complete shuffling.
    """
    if shuffle_ratio == 0:
        return X_train
    
    n_samples = X_train.shape[0]
    n_to_shuffle = int(n_samples * shuffle_ratio)
    
    if n_to_shuffle == 0 and shuffle_ratio > 0:
        return X_train # Not enough samples to shuffle

    # Get indices to shuffle
    shuffled_indices = np.random.choice(n_samples, n_to_shuffle, replace=False)
    
    # Create a copy to shuffle
    X_train_shuffled = X_train.copy()
    
    # Get the values to be shuffled and shuffle them
    values_to_shuffle = X_train_shuffled[shuffled_indices]
    np.random.shuffle(values_to_shuffle) # In-place shuffle of the selected values
    
    # Place the shuffled values back
    X_train_shuffled[shuffled_indices] = values_to_shuffle
    
    return X_train_shuffled


def run_shuffling_experiment_on_dataset(dataset_name, models_to_test):
    """
    Runs the shuffling experiment for a given dataset and a list of models.
    """
    print(f"--- Running Shuffling Experiment on {dataset_name} ---")
    
    # --- Data Loading (mimicking main.py) ---
    data_dir = 'Datasets/TSB-AD-U/'
    filepath = os.path.join(data_dir, dataset_name + '.csv')
    df = pd.read_csv(filepath).dropna()
    
    X = df.iloc[:, 0:-1].values.astype(float)
    y = df['Label'].astype(int).to_numpy()

    # Find split point and sliding window from filename/data properties
    try:
        train_index_str = dataset_name.split('_')[-3]
        train_index = int(train_index_str)
    except (IndexError, ValueError):
        # Fallback for filenames that don't match the pattern
        print("Warning: Could not determine train split index from filename. Using 50% split.")
        train_index = int(len(X) * 0.5)

    slidingWindow = find_length_rank(X, rank=1)
    
    X_train = X[:train_index, :]
    X_test = X[train_index:, :]
    y_test = y[train_index:]
    # --- End Data Loading ---

    results = []
    shuffle_ratios = np.linspace(0, 1, 11) # 0%, 10%, ..., 100%

    for model_name, model_class, hp in models_to_test:
        print(f"Testing model: {model_name}")
        for ratio in shuffle_ratios:
            print(f"  Shuffle Ratio: {ratio:.2f}")
            
            # Shuffle the training data
            X_train_shuffled = shuffle_training_data(X_train.copy(), ratio)
            
            # Initialize and fit the model
            if model_name == 'AnomalyTransformer':
                model = model_class(**hp)
                model.fit(X_train_shuffled)
            else:
                model = model_class(**hp)
                model.fit(X_train_shuffled)
            
            # Predict on the original, clean test set
            scores = model.decision_function(X_test)
            
            # Evaluate
            eval_metrics = get_metrics(scores, y_test, slidingWindow=slidingWindow)
            vus_pr_score = eval_metrics['VUS-PR']
            
            results.append({
                'model': model_name,
                'shuffle_ratio': ratio,
                'vus_pr': vus_pr_score,
                'dataset': dataset_name
            })
            
    return pd.DataFrame(results)


def visualize_shuffling_results(df_results, dataset_name):
    """
    Visualizes the experiment results using Plotly.
    """
    fig = go.Figure()

    model_names = df_results['model'].unique()

    for model_name in model_names:
        model_df = df_results[df_results['model'] == model_name]
        fig.add_trace(go.Scatter(
            x=model_df['shuffle_ratio'] * 100,
            y=model_df['vus_pr'],
            mode='lines+markers',
            name=model_name,
            marker=dict(size=10),
            line=dict(width=3)
        ))

    fig.update_layout(
        title='Model Performance Degradation with Data Shuffling',
        xaxis_title='Training Data Shuffling Percentage (%)',
        yaxis_title='VUS-PR Score',
        yaxis=dict(range=[0, 1.05]),
        xaxis=dict(range=[-5, 105]),
        legend_title='Model',
        font=dict(family="Arial, sans-serif", size=14, color="black"),
        margin=dict(l=50, r=50, t=80, b=50),
        title_x=0.5
    )
    
    # Add gridlines
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='LightGray')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='LightGray')

    # Create output directory
    output_dir = 'visualizations/robustness_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    # Save files
    base_filename = f"{output_dir}/shuffling_degradation_{dataset_name}"
    pio.write_html(fig, f"{base_filename}_plotly.html")
    try:
        pio.write_image(fig, f"{base_filename}_plotly.png", width=1000, height=600, scale=2)
    except Exception as e:
        print(f"Could not save PNG. Make sure you have 'kaleido' installed (`pip install kaleido`). Error: {e}")

    print(f"Results saved to {base_filename}_plotly.html/.png")
    return fig


if __name__ == '__main__':
    # --- Configuration ---
    # Select a representative dataset. '001_NAB' is small and good for a quick test.
    # '029_WSD' or '836_Exathlon' are more complex if needed.
    DATASET_TO_RUN = '001_NAB_id_1_Facility_tr_1007_1st_2014'

    MODELS_TO_TEST = [
        (
            'HTA-AD', HTA_AD, 
            {'HP': {'epochs': 30, 'lr': 1e-3, 'batch_size': 64, 'window_size': 128, 'latent_dim': 32, 'tcn_channels': [32]*3, 'gpu': 0}}
        ),
        (
            'AnomalyTransformer', AnomalyTransformer,
            {'win_size': 128, 'num_epochs': 30, 'lr': 1e-3, 'batch_size': 64, 'k': 3}
        )
    ]
    
    # --- Run Experiment ---
    all_results_df = run_shuffling_experiment_on_dataset(DATASET_TO_RUN, MODELS_TO_TEST)
    
    # --- Visualize & Save ---
    if not all_results_df.empty:
        visualize_shuffling_results(all_results_df, DATASET_TO_RUN)
        
        # Save raw results data
        output_dir = 'benchmark_exp/shuffling_results'
        os.makedirs(output_dir, exist_ok=True)
        all_results_df.to_csv(f'{output_dir}/shuffling_results_{DATASET_TO_RUN}.csv', index=False)
        print(f"Raw results saved to {output_dir}/shuffling_results_{DATASET_TO_RUN}.csv")
    else:
        print("No results were generated. Exiting.")