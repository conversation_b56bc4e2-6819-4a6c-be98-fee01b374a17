#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD Model Insights Visualization - Optimized Version
Each experiment generates one focused, high-quality figure suitable for academic papers.
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import sys
import os
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
import warnings

# Ensure TSB_AD module can be imported
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD, HTA_Model
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict

warnings.filterwarnings('ignore')

# Set academic-style plotting parameters
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
sns.set_style("whitegrid")

def generate_synthetic_data_with_anomaly():
    """生成大规模高度周期性的合成数据，包含明显的异常"""
    np.random.seed(42)  # 确保结果可重现
    
    # 大规模参数设置
    total_length = 5000  # 5000个时间点
    window_size = 100
    
    # 创建时间轴
    t = np.linspace(0, 20*np.pi, total_length)
    
    # 1. 长期趋势组件（缓慢变化）
    long_trend = 0.2 * np.sin(0.02 * t) + 0.1 * t / total_length
    
    # 2. 季节性组件（多个周期）
    daily_pattern = 0.4 * np.sin(t)  # 主要日周期
    weekly_pattern = 0.2 * np.sin(t/7)  # 周周期
    monthly_pattern = 0.1 * np.sin(t/30)  # 月周期
    
    # 3. 不规则周期性（模拟现实中的不完美周期）
    irregular_period = 0.3 * np.sin(2.1 * t + 0.3 * np.sin(0.01 * t))
    
    # 4. 高频噪声
    high_freq_noise = 0.1 * np.random.normal(0, 1, total_length)
    
    # 5. 低频漂移
    low_freq_drift = 0.15 * np.sin(0.005 * t) * np.random.randn(total_length) * 0.1
    
    # 6. 随机小扰动（模拟测量误差等）
    small_disturbances = np.zeros(total_length)
    n_small_disturbances = 20  # 增加小扰动数量
    for _ in range(n_small_disturbances):
        pos = np.random.randint(100, total_length - 100)
        duration = np.random.randint(3, 15)
        intensity = np.random.uniform(0.05, 0.2)
        small_disturbances[pos:pos+duration] += intensity * np.random.randn(duration)
    
    # 组合所有组件
    base_signal = (long_trend + daily_pattern + weekly_pattern + monthly_pattern + 
                    irregular_period + high_freq_noise + low_freq_drift + small_disturbances)
    
    # 归一化到合理范围
    base_signal = (base_signal - np.mean(base_signal)) / np.std(base_signal)
    base_signal = 0.5 + 0.35 * base_signal  # 缩放到[0.15, 0.85]左右
    
    # 创建标签
    labels = np.zeros(total_length)
    
    # 添加多种类型的异常（更多更复杂）
    anomaly_configs = [
        # 早期异常
        {'start': 800, 'end': 850, 'type': 'gradual_shift', 'intensity': 0.4},
        {'start': 1200, 'end': 1230, 'type': 'spike', 'intensity': 0.6},
        {'start': 1800, 'end': 1850, 'type': 'pattern_break', 'intensity': 0.3},
        
        # 中期异常
        {'start': 2200, 'end': 2280, 'type': 'oscillation', 'intensity': 0.5},
        {'start': 2600, 'end': 2640, 'type': 'drop', 'intensity': 0.4},
        {'start': 3000, 'end': 3100, 'type': 'gradual_shift', 'intensity': 0.6},
        
        # 后期异常
        {'start': 3500, 'end': 3520, 'type': 'spike', 'intensity': 0.8},
        {'start': 3800, 'end': 3900, 'type': 'pattern_break', 'intensity': 0.4},
        {'start': 4200, 'end': 4280, 'type': 'oscillation', 'intensity': 0.6},
        {'start': 4500, 'end': 4550, 'type': 'gradual_shift', 'intensity': 0.5},
    ]
    
    for anomaly in anomaly_configs:
        start, end = anomaly['start'], anomaly['end']
        anom_type = anomaly['type']
        intensity = anomaly['intensity']
        
        if anom_type == 'gradual_shift':
            # 渐变偏移
            shift_pattern = np.linspace(0, intensity, end - start)
            base_signal[start:end] += shift_pattern
            
        elif anom_type == 'spike':
            # 尖峰异常
            spike_length = end - start
            peak_pos = spike_length // 2
            spike_pattern = np.concatenate([
                np.linspace(0, intensity, peak_pos),
                np.linspace(intensity, 0, spike_length - peak_pos)
            ])
            base_signal[start:end] += spike_pattern
            
        elif anom_type == 'pattern_break':
            # 模式破坏 - 用随机噪声替换正常模式
            base_signal[start:end] = np.mean(base_signal) + intensity * np.random.randn(end - start)
            
        elif anom_type == 'oscillation':
            # 异常振荡
            osc_length = end - start
            osc_freq = 2 * np.pi / 10  # 快速振荡
            osc_t = np.arange(osc_length)
            oscillation = intensity * np.sin(osc_freq * osc_t) * np.exp(-osc_t / (osc_length * 0.5))
            base_signal[start:end] += oscillation
            
        elif anom_type == 'drop':
            # 突然下降
            drop_pattern = np.linspace(0, -intensity, end - start)
            base_signal[start:end] += drop_pattern
        
        labels[start:end] = 1
    
    # 确保信号在合理范围内
    base_signal = np.clip(base_signal, 0, 1.5)
    
    return base_signal.reshape(-1, 1), labels, window_size

def experiment_1_cnn_feature_extraction():
    """Experiment 1: CNN Feature Extraction Analysis - Highly periodic data"""
    print("\n🔍 Experiment 1: CNN Feature Extraction Analysis")
    
    # Generate highly periodic synthetic time series data
    data, labels, window_size = generate_synthetic_data_with_anomaly()
    
    # Create and train HTA-AD model
    model_params = {
        'window_size': window_size,
        'latent_dim': 16,
        'lr': 0.001,
        'epochs': 100,
        'batch_size': 32
    }
    
    hta_model = HTA_AD(HP=model_params)
    
    # Train model (use large training data)
    normal_data = data[:3000]  # Use first 3000 points for training
    hta_model.fit(normal_data)
    
    # Select test window containing clear anomaly
    test_start = 3500  # Use data from large scale dataset
    test_window = data[test_start:test_start+window_size]
    test_labels = labels[test_start:test_start+window_size]
    
    # Extract CNN features
    hta_model.model.eval()
    with torch.no_grad():
        input_tensor = torch.FloatTensor(test_window.reshape(1, window_size, 1)).to(hta_model.device)
        
        # Get CNN output (after local pattern summarizer)
        x_permuted = input_tensor.permute(0, 2, 1)  # (batch, input_dim, window_size)
        cnn_features = hta_model.model.encoder_cnn(x_permuted)  # (batch, cnn_channels, compressed_length)
        cnn_features = cnn_features.permute(0, 2, 1).cpu().numpy()[0]  # (compressed_length, cnn_channels)
    
    # Calculate compression ratio
    original_length = len(test_window)
    compressed_length = cnn_features.shape[0]
    compression_ratio = original_length / compressed_length
    
    # Create clean visualization without title
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    time_original = np.arange(original_length)
    
    # Correct time alignment for CNN output
    # CNN uses kernel_size=7, stride=2, padding=3
    # For stride=2, each compressed point represents 2 original points
    # The center of the receptive field for compressed[i] is at original[i*stride + (kernel_size-1)/2]
    kernel_size = 7
    stride = 2
    padding = 3
    
    # Calculate correct time positions for compressed signal
    time_compressed = np.arange(compressed_length) * stride + (kernel_size - 1) // 2
    
    # Left subplot: Original vs Compressed Signal
    ax1.plot(time_original, test_window.flatten(), linewidth=3, color='#2E86AB', 
             label='Original Signal', alpha=0.9)
    
    # Find the channel with highest variance (most responsive)
    channel_variances = np.var(cnn_features, axis=0)
    best_channel = np.argmax(channel_variances)
    compressed_signal = cnn_features[:, best_channel]
    
    # Normalize and scale the compressed signal to match original range
    compressed_signal = (compressed_signal - np.mean(compressed_signal)) / (np.std(compressed_signal) + 1e-8)
    compressed_signal = compressed_signal * np.std(test_window) + np.mean(test_window)
    
    ax1.plot(time_compressed, compressed_signal, linewidth=3, color='#E76F51', 
             label=f'Compressed Signal (Ch.{best_channel})', alpha=0.9, marker='o', markersize=6)
    
    # Mark anomaly regions very subtly - only core region
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        anomaly_regions = []
        start = None
        for i, is_anomaly in enumerate(anomaly_mask):
            if is_anomaly and start is None:
                start = i
            elif not is_anomaly and start is not None:
                anomaly_regions.append((start, i-1))
                start = None
        if start is not None:
            anomaly_regions.append((start, len(anomaly_mask)-1))
        
        # Only mark the largest anomaly region
        if anomaly_regions:
            largest_region = max(anomaly_regions, key=lambda x: x[1] - x[0])
            start, end = largest_region
            ax1.axvspan(start, end, alpha=0.1, color='red', zorder=0)
    
    # Add compression info box
    info_text = (f'Compression: {original_length}→{compressed_length} points\n'
                f'Ratio: {compression_ratio:.1f}×\n'
                f'Best Channel: {best_channel}')
    
    ax1.text(-0.02, 0.98, info_text, transform=ax1.transAxes, fontsize=12,
            bbox=dict(boxstyle="round,pad=0.4", facecolor="white", alpha=0.9,
                     edgecolor='#2E86AB', linewidth=2),
            verticalalignment='top', fontweight='bold')
    
    ax1.set_xlabel('Time Steps', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Signal Value', fontsize=12, fontweight='bold')
    ax1.legend(fontsize=11, loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # Right subplot: Show only top 4 most responsive channels for clarity
    n_channels_to_show = min(4, cnn_features.shape[1])
    # Select channels with highest variance for cleaner visualization
    channel_variances_all = np.var(cnn_features, axis=0)
    top_channels = np.argsort(channel_variances_all)[-n_channels_to_show:]
    
    colors = ['#E76F51', '#2E86AB', '#F4A261', '#E9C46A']  # Clear, distinct colors
    for i, (ch_idx, color) in enumerate(zip(top_channels, colors)):
        normalized_features = (cnn_features[:, ch_idx] - np.mean(cnn_features[:, ch_idx])) / (np.std(cnn_features[:, ch_idx]) + 1e-8)
        # Add vertical offset for clarity
        ax2.plot(time_compressed, normalized_features + i*2, linewidth=3, 
                color=color, alpha=0.9, label=f'Channel {ch_idx}')
    
    ax2.set_xlabel('Time Steps', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Normalized Feature Response', fontsize=12, fontweight='bold')
    ax2.legend(fontsize=11, loc='upper right')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/cnn_feature_extraction.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Original Length: {original_length} → Compressed Length: {compressed_length}")
    print(f"✅ Compression Ratio: {compression_ratio:.1f}×")
    print(f"✅ CNN Feature Channels: {cnn_features.shape[1]}")
    print(f"✅ Best Responsive Channel: {best_channel}")
    print(f"✅ Highly periodic data visualization saved to: experiments/cnn_feature_extraction.png")

def experiment_2_tcn_receptive_field():
    """Experiment 2: TCN Receptive Field Visualization - Clean without problematic annotations"""
    print("\n📡 Experiment 2: TCN Receptive Field Analysis")
    
    # Enhanced TCN configuration with more layers for visual impact
    kernel_size = 7
    dilations = [1, 2, 4, 8, 16, 32, 64]  # Extended to 7 layers for dramatic effect
    tcn_channels = [64, 128, 256, 512, 256, 128, 64]  # More layers
    
    def calculate_receptive_field(layer, kernel_size, dilations):
        """Calculate receptive field for TCN layer"""
        rf = 1
        for i in range(layer):
            rf += (kernel_size - 1) * dilations[i]
        return rf
    
    # Calculate receptive fields for comparison
    layers = np.arange(1, len(tcn_channels) + 1)
    tcn_rfs = []
    std_rfs = []
    
    for i in range(len(tcn_channels)):
        tcn_rf = calculate_receptive_field(i + 1, kernel_size, dilations[:i+1])
        std_rf = 1 + i * (kernel_size - 1)
        tcn_rfs.append(tcn_rf)
        std_rfs.append(std_rf)
    
    # Create clean visualization without title - with extra space for annotations
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    
    # Plot with enhanced visual impact
    ax.plot(layers, tcn_rfs, 'o-', linewidth=4, markersize=12, color='#E76F51', 
            label='TCN (Dilated Convolution)', markerfacecolor='white', 
            markeredgewidth=3, markeredgecolor='#E76F51', zorder=3)
    ax.plot(layers, std_rfs, 's--', linewidth=3, markersize=10, color='#264653', 
            alpha=0.8, label='Standard Convolution', markerfacecolor='white',
            markeredgewidth=2, markeredgecolor='#264653', zorder=2)
    
    # Add dramatic fill between curves to show the gap
    ax.fill_between(layers, tcn_rfs, std_rfs, alpha=0.2, color='#E76F51', 
                    label='Efficiency Gap', zorder=1)
    
    # Add value annotations for all key points - with smart positioning
    for i, (layer, tcn_rf, std_rf) in enumerate(zip(layers, tcn_rfs, std_rfs)):
        if i == 0 or i == len(layers)-1 or i == len(layers)//2:  # Show first, middle, last
            # Smart positioning for last point to avoid clipping
            if i == len(layers)-1:  # Last point (763)
                # Position annotation to the left and slightly below
                ax.annotate(f'{tcn_rf}', (layer, tcn_rf), textcoords="offset points", 
                           xytext=(-15, -15), ha='center', fontweight='bold', fontsize=14, 
                           color='#E76F51', bbox=dict(boxstyle="round,pad=0.3", 
                           facecolor='white', edgecolor='#E76F51', alpha=0.8))
            else:
                # Position other annotations above the point
                ax.annotate(f'{tcn_rf}', (layer, tcn_rf), textcoords="offset points", 
                           xytext=(0, 15), ha='center', fontweight='bold', fontsize=14, 
                           color='#E76F51', bbox=dict(boxstyle="round,pad=0.3", 
                           facecolor='white', edgecolor='#E76F51', alpha=0.8))
            
            ax.annotate(f'{std_rf}', (layer, std_rf), textcoords="offset points", 
                       xytext=(0,-25), ha='center', fontweight='bold', fontsize=12, 
                       color='#264653', bbox=dict(boxstyle="round,pad=0.3", 
                       facecolor='white', edgecolor='#264653', alpha=0.8))
    
    # Add dilation sequence annotation - positioned lower to avoid clipping
    dilation_text = "Dilation Sequence: " + " → ".join([f"{d}" for d in dilations])
    ax.text(0.02, 0.88, dilation_text, transform=ax.transAxes, fontsize=13,
            bbox=dict(boxstyle="round,pad=0.5", facecolor="#E3F2FD", alpha=0.9,
                     edgecolor='#1976D2', linewidth=2),
            verticalalignment='top', fontweight='bold')
    
    # Add dramatic efficiency statistics
    final_tcn_rf = tcn_rfs[-1]
    final_std_rf = std_rfs[-1]
    efficiency_gain = final_tcn_rf / final_std_rf
    
    stats_text = (f'Final Receptive Field:\n'
                 f'TCN: {final_tcn_rf} time steps\n'
                 f'Standard: {final_std_rf} time steps\n'
                 f'Efficiency Gain: {efficiency_gain:.1f}×')
    
    ax.text(0.98, 0.02, stats_text, transform=ax.transAxes, fontsize=13,
            bbox=dict(boxstyle="round,pad=0.5", facecolor="#E8F5E8", alpha=0.9,
                     edgecolor='#4CAF50', linewidth=2),
            verticalalignment='bottom', horizontalalignment='right', fontweight='bold')
    
    # Enhanced axis styling
    ax.set_xlabel('Network Layer', fontsize=14, fontweight='bold')
    ax.set_ylabel('Receptive Field Size (Time Steps)', fontsize=14, fontweight='bold')
    ax.set_xticks(layers)
    
    # Use log scale for y-axis to better show exponential growth
    ax.set_yscale('log')
    ax.set_ylabel('Receptive Field Size (Time Steps, Log Scale)', fontsize=14, fontweight='bold')
    
    # Enhanced legend
    ax.legend(fontsize=13, loc='upper left', frameon=True, fancybox=True, 
              shadow=True, framealpha=0.9)
    
    # Enhanced grid
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_xlim(0.5, len(layers) + 0.5)
    
    # Add extra space at top to prevent clipping
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)  # Leave 8% space at top for annotations
    
    plt.savefig('experiments/tcn_receptive_field.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ TCN Layers: {len(tcn_channels)} layers")
    print(f"✅ Final Receptive Field: {final_tcn_rf} time steps")
    print(f"✅ Efficiency Gain: {efficiency_gain:.1f}× over standard convolution")
    print(f"✅ Dilation sequence: {dilations}")
    print(f"✅ Fixed annotation clipping visualization saved to: experiments/tcn_receptive_field.png")

def experiment_3_reconstruction_error_analysis():
    """Experiment 3: Reconstruction Error Analysis - Ultra clean"""
    print("\n🎯 Experiment 3: Reconstruction Error Analysis")
    
    # Generate data
    data, labels, window_size = generate_synthetic_data_with_anomaly()
    
    # Create and train HTA-AD model
    model_params = {
        'window_size': window_size,
        'latent_dim': 16,
        'lr': 0.001,
        'epochs': 100,
        'batch_size': 32
    }
    
    hta_model = HTA_AD(HP=model_params)
    
    # Train model (using large normal data)
    normal_data = data[:3000]  # Use first 3000 points for training
    hta_model.fit(normal_data)
    
    # Select test window containing anomalies
    test_start = 3500  # Use data from large scale dataset
    test_window = data[test_start:test_start+window_size]
    test_labels = labels[test_start:test_start+window_size]
    
    # Get reconstruction results
    hta_model.model.eval()
    with torch.no_grad():
        input_tensor = torch.FloatTensor(test_window.reshape(1, window_size, 1)).to(hta_model.device)
        reconstructed = hta_model.model(input_tensor)
        reconstruction = reconstructed.cpu().numpy()[0, :, 0]
    
    # Calculate reconstruction error
    reconstruction_error = np.abs(test_window.flatten() - reconstruction)
    
    # Create ultra clean visualization without title
    fig, ax = plt.subplots(1, 1, figsize=(14, 6))
    
    time_steps = np.arange(window_size)
    
    # Mark anomaly regions very subtly - only the core anomaly points
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        # Only mark the most significant anomaly region (largest continuous block)
        anomaly_regions = []
        start = None
        for i, is_anomaly in enumerate(anomaly_mask):
            if is_anomaly and start is None:
                start = i
            elif not is_anomaly and start is not None:
                anomaly_regions.append((start, i-1))
                start = None
        if start is not None:
            anomaly_regions.append((start, len(anomaly_mask)-1))
        
        # Find the largest anomaly region only
        if anomaly_regions:
            largest_region = max(anomaly_regions, key=lambda x: x[1] - x[0])
            start, end = largest_region
            # Make the region smaller by trimming edges
            region_size = end - start + 1
            trim = max(1, region_size // 4)  # Trim 25% from each side
            start += trim
            end -= trim
            
            if start < end:  # Only mark if there's still a region left
                ax.axvspan(start, end, alpha=0.08, color='#FF6B6B', zorder=0)
    
    # Plot signals with clean styling
    ax.plot(time_steps, test_window.flatten(), linewidth=3, color='#2E86AB', 
            label='Original Signal', alpha=0.9, zorder=3)
    ax.plot(time_steps, reconstruction, linewidth=3, color='#A23B72', 
            label='Reconstructed Signal', alpha=0.9, linestyle='--', zorder=3)
    
    # Create secondary y-axis for reconstruction error
    ax2 = ax.twinx()
    
    # Simple error visualization
    ax2.fill_between(time_steps, 0, reconstruction_error, alpha=0.4, color='#E74C3C', 
                     label='Reconstruction Error', zorder=1)
    
    # Calculate and display key statistics only
    normal_error = reconstruction_error[~anomaly_mask] if np.any(anomaly_mask) else reconstruction_error
    anomaly_error = reconstruction_error[anomaly_mask] if np.any(anomaly_mask) else []
    
    if len(anomaly_error) > 0:
        amplification_factor = np.mean(anomaly_error) / np.mean(normal_error)
        
        # Ultra clean statistics box
        stats_text = (f'Normal Error: {np.mean(normal_error):.3f}\n'
                     f'Anomaly Error: {np.mean(anomaly_error):.3f}\n'
                     f'Amplification: {amplification_factor:.1f}×')
        
        # Add ROC calculation
        from sklearn.metrics import roc_auc_score
        if len(np.unique(test_labels)) > 1:
            auc_score = roc_auc_score(test_labels, reconstruction_error)
            stats_text += f'\nROC AUC: {auc_score:.3f}'
    else:
        stats_text = f'Average Error: {np.mean(normal_error):.3f}'
    
    # Minimal stats box
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=14,
            bbox=dict(boxstyle="round,pad=0.5", facecolor="white", alpha=0.9, 
                     edgecolor='#2E86AB', linewidth=2),
            verticalalignment='top', fontweight='bold')
    
    # Clean axis styling
    ax.set_xlabel('Time Steps', fontsize=14, fontweight='bold')
    ax.set_ylabel('Signal Value', fontsize=14, color='#2E86AB', fontweight='bold')
    ax2.set_ylabel('Reconstruction Error', fontsize=14, color='#E74C3C', fontweight='bold')
    
    # Color-code axis ticks
    ax.tick_params(axis='y', colors='#2E86AB', labelsize=12)
    ax2.tick_params(axis='y', colors='#E74C3C', labelsize=12)
    ax.tick_params(axis='x', labelsize=12)
    
    # Minimal legend
    lines1, labels1 = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines1 + lines2, labels1 + labels2, loc='upper right', 
              fontsize=12, frameon=True, fancybox=True, shadow=True)
    
    # Clean grid
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # Set clean axis limits
    ax.set_xlim(0, window_size-1)
    ax2.set_ylim(0, max(reconstruction_error) * 1.05)
    
    plt.tight_layout()
    plt.savefig('experiments/reconstruction_error_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Clean output statistics
    print(f"✅ Normal Error: {np.mean(normal_error):.6f}")
    if len(anomaly_error) > 0:
        print(f"✅ Anomaly Error: {np.mean(anomaly_error):.6f}")
        print(f"✅ Error Amplification: {amplification_factor:.1f}×")
        if len(np.unique(test_labels)) > 1:
            print(f"✅ ROC AUC Score: {auc_score:.3f}")
    print(f"✅ Ultra clean visualization saved to: experiments/reconstruction_error_analysis.png")

def run_all_experiments():
    """Run all three optimized visualization experiments"""
    print("=" * 80)
    print("🚀 HTA-AD Model Insights - Optimized Visualizations")
    print("=" * 80)
    
    # Create output directory
    os.makedirs('experiments', exist_ok=True)
    
    try:
        # Run three experiments
        experiment_1_cnn_feature_extraction()
        experiment_2_tcn_receptive_field()
        experiment_3_reconstruction_error_analysis()
        
        print("\n" + "=" * 80)
        print("🎉 All optimized visualizations completed!")
        print("📁 Clean, publication-ready figures saved:")
        print("   📊 cnn_feature_extraction.png")
        print("   📊 tcn_receptive_field.png") 
        print("   📊 reconstruction_error_analysis.png")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Experiment execution error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":