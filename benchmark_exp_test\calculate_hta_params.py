import torch
import sys
import os
import numpy as np

# --- Path Setup ---
# Add project root to path to allow imports from TSB_AD and benchmark_exp
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the model
from benchmark_exp.hta_ad import HTA_AD

def count_parameters(model):
    """Counts the total and trainable parameters of a PyTorch model."""
    if not isinstance(model, torch.nn.Module):
        print("Warning: The provided object is not a PyTorch model.")
        return 0, 0
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params

def main():
    """
    Instantiate HTA_AD and calculate its parameters.
    """
    print("🔬 Analyzing HTA_AD model from benchmark_exp/hta_ad.py...")

    # Define dummy hyperparameters for model instantiation.
    # These values should reflect a typical configuration.
    DUMMY_INPUT_DIM = 1 # Standard for univariate time series
    DUMMY_WINDOW_SIZE = 128
    DUMMY_HP = {
        'window_size': DUMMY_WINDOW_SIZE,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2,
        'lr': 1e-3,
        'epochs': 1, # Only need 1 epoch to initialize the model structure
        'batch_size': 32,
        'gpu': 0 # Use GPU 0 if available
    }

    try:
        # 1. Instantiate the detector
        detector = HTA_AD(HP=DUMMY_HP, normalize=True)

        # 2. The internal torch model is created lazily in `fit`.
        # We need to call `fit` with some dummy data to trigger initialization.
        print("  ... Running fit() with dummy data to initialize the internal model.")
        dummy_train_data = np.random.randn(DUMMY_WINDOW_SIZE * 2, DUMMY_INPUT_DIM)
        detector.fit(dummy_train_data)

        # 3. Get the torch model from the detector instance
        torch_model = detector.model
        
        if torch_model:
            total, trainable = count_parameters(torch_model)
            size_mb = total * 4 / (1024 * 1024) # 4 bytes per parameter (float32)
            
            print("\n" + "="*50)
            print(" " * 14 + "HTA_AD Model Complexity")
            print("="*50)
            print(f"  - Total Parameters:     {total:,}")
            print(f"  - Trainable Parameters: {trainable:,}")
            print(f"  - Model Size (MB):      {size_mb:.4f}")
            print("="*50)
        else:
            print("\n❌ Error: Could not find the PyTorch model inside the HTA_AD detector after fitting.")

    except Exception as e:
        print(f"\n❌ An error occurred during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main() 