import torch
import numpy as np
import pandas as pd
import os
import sys
from sklearn.preprocessing import MinMaxScaler
import itertools
import math
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# --- 1. Ensure project root is in sys.path ---
# This allows for smooth importing of other project modules.
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- 2. Model Imports ---
from benchmark_exp.hta_ad import HTA_AD
from TSB_AD.models.AnomalyTransformer import AnomalyTransformer
from TSB_AD.utils.dataset import ReconstructDataset

# --- 3. Configuration ---
# Use a specific file from the TSB-AD-U directory you provided.
DATASET_PATH = './Datasets/TSB-AD-U/178_SMD_id_1_Facility_tr_6000_1st_10609.csv'
# The training set size is inferred from the filename '..._tr_6000_...'.
TRAIN_SPLIT_POINT = 6000
# Define the output directory for the plot.
OUTPUT_DIR = './visualizations/reconstruction_comparison/'
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Plotting Parameters
WINDOW_SIZE = 128 # Input window size for the models.

def load_and_prepare_data():
    """
    Loads and prepares the univariate time series data.
    - Reads the single-column CSV.
    - Splits data based on the inferred split point.
    - Normalizes the data using MinMaxScaler.
    """
    print("Loading and preparing data...")
    if not os.path.exists(DATASET_PATH):
        raise FileNotFoundError(f"Dataset not found: {DATASET_PATH}")

    # Read CSV, use the first row as the header, and access columns by position
    # This is robust against varying column names in the header.
    df = pd.read_csv(DATASET_PATH, header=0)
    data_values = df.iloc[:, 0].values.reshape(-1, 1)
    labels = df.iloc[:, 1].values

    # Split into training and testing sets.
    train_raw = data_values[:TRAIN_SPLIT_POINT]
    test_raw = data_values[TRAIN_SPLIT_POINT:]
    test_labels = labels[TRAIN_SPLIT_POINT:]

    # Normalize the data.
    scaler = MinMaxScaler()
    train_data = scaler.fit_transform(train_raw)
    test_data = scaler.transform(test_raw)

    print(f"Data prepared. Train shape: {train_data.shape}, Test shape: {test_data.shape}")
    return train_data, test_data, test_labels, scaler

def get_reconstructions(model_instance, data, device):
    """Gets model reconstructions for the given data."""
    print(f"Generating reconstructions for {model_instance.__class__.__name__}...")
    model_instance.model.eval()

    # Determine window size dynamically from the model instance
    if isinstance(model_instance, AnomalyTransformer):
        window_size = model_instance.win_size
        dataset = ReconstructDataset(data, window_size=window_size)
        loader = torch.utils.data.DataLoader(dataset, batch_size=128, shuffle=False)
    elif hasattr(model_instance, 'HP') and 'window_size' in model_instance.HP:
        window_size = model_instance.HP['window_size']
        windows = model_instance._create_windows(data)
        if len(windows) == 0:
            print("Warning: No windows were created from the data.")
            return np.zeros_like(data)
        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=128, shuffle=False)
    else:
        raise NotImplementedError(f"Reconstruction logic not implemented for {model_instance.__class__.__name__}")

    all_reconstructions = []
    with torch.no_grad():
        if isinstance(model_instance, AnomalyTransformer):
            for (batch_windows, _) in loader:
                batch_windows = batch_windows.to(device)
                reconstructed_batch, _, _, _ = model_instance.model(batch_windows)
                all_reconstructions.append(reconstructed_batch.cpu().numpy())
        else:
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(device)
                reconstructed_batch = model_instance.model(batch_windows)
                all_reconstructions.append(reconstructed_batch.cpu().numpy())

    # Stitch windowed reconstructions back into a single time series.
    reconstructed_full = np.zeros_like(data)
    counts = np.zeros_like(data)
    if not all_reconstructions:
        return reconstructed_full
        
    full_recons_windows = np.concatenate(all_reconstructions)

    for i, recon_window in enumerate(full_recons_windows):
        start, end = i, i + window_size
        if end > len(reconstructed_full):
            break
        reconstructed_full[start:end] += recon_window
        counts[start:end] += 1

    # Average the reconstructions for overlapping windows.
    reconstructed_full[counts > 0] /= counts[counts > 0]
    
    # Simple fill for the initial part of the series.
    if window_size > 0 and len(reconstructed_full) >= window_size:
        reconstructed_full[:window_size-1] = reconstructed_full[window_size-1]
    
    return reconstructed_full

def plot_comparison(original, hta_recon, at_recon, labels, train_split_point, output_path):
    """Plots the comparison of original signal, reconstructions, and errors using Plotly."""
    print("Plotting results with Plotly...")

    # Calculate Mean Squared Error for each time step.
    hta_error = np.mean((original - hta_recon)**2, axis=1)
    at_error = np.mean((original - at_recon)**2, axis=1)

    fig = make_subplots(rows=2, cols=1, shared_xaxes=True,
                        subplot_titles=('<b>Model Reconstruction on Full Dataset (Train & Test)</b>',
                                        '<b>Reconstruction Error (MSE)</b>'),
                        vertical_spacing=0.1)

    time_steps = np.arange(len(original))

    # --- Plot 1: Reconstructions ---
    fig.add_trace(go.Scatter(x=time_steps, y=original[:, 0], name='Original Signal',
                             line=dict(color='grey', width=2.5), legendgroup='1'), row=1, col=1)
    fig.add_trace(go.Scatter(x=time_steps, y=hta_recon[:, 0], name='HTA-AD Reconstruction',
                             line=dict(color='#00BEFF', width=2), legendgroup='1'), row=1, col=1)
    fig.add_trace(go.Scatter(x=time_steps, y=at_recon[:, 0], name='AnomalyTransformer Reconstruction',
                             line=dict(color='#FF6347', width=1.5), opacity=0.8, legendgroup='1'), row=1, col=1)

    # --- Plot 2: Reconstruction Errors ---
    fig.add_trace(go.Scatter(x=time_steps, y=hta_error, name='HTA-AD Error',
                             line=dict(color='#00BEFF', width=2), legendgroup='2', showlegend=True), row=2, col=1)
    fig.add_trace(go.Scatter(x=time_steps, y=at_error, name='AnomalyTransformer Error',
                             line=dict(color='#FF6347', width=2), legendgroup='2', showlegend=True), row=2, col=1)

    # --- Add annotations and shapes ---
    # Add vertical line for train/test split
    fig.add_vline(x=train_split_point, line_width=2, line_dash="dash", line_color="green")
    
    # Dummy trace for the legend of the split line
    fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines',
                             line=dict(color='green', dash='dash', width=2),
                             name='Train/Test Split', legendgroup='1'), row=1, col=1)

    # Highlight anomaly regions
    anomaly_blocks = find_anomaly_blocks(labels)
    for start, end in anomaly_blocks:
        fig.add_vrect(x0=start, x1=end,
                      fillcolor="#FFBDBD", opacity=0.4,
                      layer="below", line_width=0)
                      
    # Dummy trace for the legend of anomaly regions
    if anomaly_blocks:
         fig.add_trace(go.Scatter(x=[None], y=[None],
                                  mode='markers',
                                  marker=dict(color='#FFBDBD', size=10, symbol='square'),
                                  name='Ground Truth Anomaly', legendgroup='1'), row=1, col=1)

    # --- Update layout ---
    fig.update_layout(
        title_text='<b>Full Timeseries Reconstruction: HTA-AD vs. AnomalyTransformer</b>',
        font=dict(family="Times New Roman", size=16),
        plot_bgcolor='white',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1,
            font_size=14,
            tracegroupgap=20
        ),
        height=800,
        width=1800,
    )
    
    fig.update_yaxes(title_text="Normalized Value", row=1, col=1, gridcolor='#E5E5E5', zeroline=False, linecolor='black', linewidth=1, showline=True)
    fig.update_yaxes(title_text="Mean Squared Error", row=2, col=1, gridcolor='#E5E5E5', zeroline=False, linecolor='black', linewidth=1, showline=True)
    fig.update_xaxes(title_text="Time Step", row=2, col=1, gridcolor='#E5E5E5', zeroline=False, linecolor='black', linewidth=1, showline=True)
    fig.update_xaxes(showticklabels=False, row=1, col=1, gridcolor='#E5E5E5', zeroline=False, linecolor='black', linewidth=1, showline=True)

    pio.write_image(fig, output_path, scale=2)
    print(f"Comparison plot saved to: {output_path}")

def find_anomaly_blocks(labels):
    """Finds contiguous blocks of anomalies and returns their start and end indices."""
    runs = np.where(np.diff(labels, prepend=0, append=0) != 0)[0]
    runs = runs.reshape(-1, 2)
    return [(start, end) for start, end in runs if labels[start] == 1]

def plot_hpo_results(search_history, model_name, param_names, output_path):
    """Visualizes the hyperparameter search results as a heatmap using Plotly."""
    if not search_history:
        print(f"No search history for {model_name} to plot.")
        return

    df = pd.DataFrame(search_history)

    if len(param_names) == 2:
        param1, param2 = param_names
        pivot_table = df.pivot(index=param1, columns=param2, values='loss')
        
        fig = go.Figure(data=go.Heatmap(
            z=pivot_table.values,
            x=pivot_table.columns.astype(str),
            y=pivot_table.index.astype(str),
            colorscale="Viridis_r",
            text=pivot_table.values,
            texttemplate="%{text:.4f}",
        ))
        
        fig.update_layout(
            title_text=f'<b>HPO Results for {model_name}: Validation Loss (MSE)</b>',
            xaxis_title=param2,
            yaxis_title=param1,
            font=dict(family="Times New Roman", size=16),
            plot_bgcolor='white',
            xaxis=dict(type='category'),
            yaxis=dict(type='category', autorange='reversed')
        )
        pio.write_image(fig, output_path, width=800, height=700, scale=2)

    elif len(param_names) == 3:
        param1, param2, facet_param = param_names
        facet_values = sorted(df[facet_param].unique())
        
        cols = min(len(facet_values), 3)
        rows = math.ceil(len(facet_values) / cols)
        
        subplot_titles = [f'<b>{facet_param} = {val}</b>' for val in facet_values]
        
        fig = make_subplots(
            rows=rows, cols=cols,
            subplot_titles=subplot_titles,
            shared_xaxes=True, shared_yaxes=True,
            vertical_spacing=0.2, horizontal_spacing=0.08
        )

        for i, val in enumerate(facet_values):
            row_idx = i // cols + 1
            col_idx = i % cols + 1
            
            sub_df = df[df[facet_param] == val]
            pivot_table = sub_df.pivot(index=param1, columns=param2, values='loss')
            
            x_vals = sorted(sub_df[param2].unique())
            y_vals = sorted(sub_df[param1].unique())

            fig.add_trace(go.Heatmap(
                z=pivot_table.values,
                x=[str(x) for x in x_vals],
                y=[str(y) for y in y_vals],
                colorscale="Viridis_r",
                text=pivot_table.values,
                texttemplate="%{text:.4f}",
                colorbar_len=1/rows if rows > 1 else 1,
                colorbar_yanchor='top' if rows > 1 else 'middle',
                colorbar_y=(1 - (row_idx - 1) * (1/rows + 0.05)) if rows > 1 else 0.5,
                showscale=True,
            ), row=row_idx, col=col_idx)
            
            fig.update_yaxes(type='category', autorange='reversed', row=row_idx, col=col_idx)
            fig.update_xaxes(type='category', row=row_idx, col=col_idx)

        fig.update_layout(
            title_text=f'<b>HPO Results for {model_name} (faceted by {facet_param})</b>',
            font=dict(family="Times New Roman", size=16),
            plot_bgcolor='white',
            height=rows * 450 + 50,
            width=cols * 500
        )
        
        for i in range(1, rows + 1):
            fig.update_yaxes(title_text=param1, row=i, col=1)
        for i in range(1, cols + 1):
            fig.update_xaxes(title_text=param2, row=rows, col=i)
            
        fig.update_annotations(font_size=18)

        pio.write_image(fig, output_path, scale=2)

    print(f"HPO visualization saved to {output_path}")

def run_hyperparameter_search(model_name, param_grid, train_data, val_data, device):
    """Runs a grid search for the given model and hyperparameter grid."""
    print(f"\n--- Starting Hyperparameter Search for {model_name} ---")
    
    keys, values = zip(*param_grid.items())
    search_history = []
    best_params = None
    best_val_loss = float('inf')

    for v in itertools.product(*values):
        params = dict(zip(keys, v))
        print(f"\n--- Testing {model_name} with {params} ---")

        if model_name == 'AnomalyTransformer':
            model = AnomalyTransformer(
                input_c=1, num_epochs=15, batch_size=128, device=device, **params
            )
        elif model_name == 'HTA_AD':
            # HTA_AD expects HP dict and other params
            hp_dict = params.copy()
            # These are not part of the search grid but required by HTA_AD's HP dict
            hp_dict.setdefault('epochs', 15)
            hp_dict.setdefault('batch_size', 128)
            hp_dict.setdefault('gpu', 0 if 'cuda' in device else -1)
            model = HTA_AD(HP=hp_dict, normalize=False)
        else:
            raise ValueError(f"Unknown model_name: {model_name}")

        model.fit(train_data)
        
        recons = get_reconstructions(model, val_data, device)
        loss = np.mean((val_data - recons)**2)
        
        print(f"Validation Loss (MSE): {loss:.6f}")
        search_history.append({**params, 'loss': loss})

        if loss < best_val_loss:
            best_val_loss = loss
            best_params = params
            print(f"!!! New best parameters found for {model_name}: {best_params} with loss {best_val_loss:.6f} !!!")

    print(f"\n--- Best {model_name} Hyperparameters: {best_params} ---")
    return best_params, search_history

def main():
    """Main execution function."""
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    train_data, test_data, test_labels, scaler = load_and_prepare_data()
    
    # --- Split validation set for HPO ---
    val_split_point = int(len(train_data) * 0.8)
    sub_train_data = train_data[:val_split_point]
    val_data = train_data[val_split_point:]
    print(f"Splitting training data for HPO: {len(sub_train_data)} for training, {len(val_data)} for validation.")

    # --- Hyperparameter Search ---
    hyperparams_at = {
        'win_size': [50, 100, 150],
        'lr': [1e-3, 1e-4, 1e-5]
    }
    hyperparams_hta = {
        'window_size': [50, 100, 150],
        'lr': [1e-3, 1e-4, 1e-5],
        'latent_dim': [8, 16, 32]
    }
    
    best_at_params, at_search_history = run_hyperparameter_search(
        'AnomalyTransformer', hyperparams_at, sub_train_data, val_data, device
    )
    best_hta_params, hta_search_history = run_hyperparameter_search(
        'HTA_AD', hyperparams_hta, sub_train_data, val_data, device
    )

    # --- Visualize HPO Results ---
    plot_hpo_results(
        at_search_history, 'AnomalyTransformer', list(hyperparams_at.keys()),
        os.path.join(OUTPUT_DIR, 'hpo_results_AnomalyTransformer.png')
    )
    plot_hpo_results(
        hta_search_history, 'HTA_AD', list(hyperparams_hta.keys()),
        os.path.join(OUTPUT_DIR, 'hpo_results_HTA_AD.png')
    )

    # --- Train final models with best parameters ---
    print("\n--- Training final models with best hyperparameters ---")
    hta_detector = HTA_AD(
        HP={**best_hta_params, 'epochs': 15, 'batch_size': 128, 'gpu': 0}, 
        normalize=False
    )
    hta_detector.fit(train_data)

    at_detector = AnomalyTransformer(
        win_size=best_at_params['win_size'],
        input_c=1,
        num_epochs=15,
        batch_size=128,
        lr=best_at_params['lr'],
        device=device
    )
    at_detector.fit(train_data)

    # --- Get Reconstructions for both Train and Test sets ---
    print("\n--- Generating Reconstructions ---")
    hta_recon_train = get_reconstructions(hta_detector, train_data, device)
    at_recon_train = get_reconstructions(at_detector, train_data, device)
    hta_recon_test = get_reconstructions(hta_detector, test_data, device)
    at_recon_test = get_reconstructions(at_detector, test_data, device)

    # --- Combine train and test sets for a full plot ---
    full_data = np.concatenate((train_data, test_data), axis=0)
    full_hta_recon = np.concatenate((hta_recon_train, hta_recon_test), axis=0)
    full_at_recon = np.concatenate((at_recon_train, at_recon_test), axis=0)
    
    train_labels = np.zeros(len(train_data))
    full_labels = np.concatenate((train_labels, test_labels))

    plot_comparison(
        full_data,
        full_hta_recon,
        full_at_recon,
        full_labels,
        train_split_point=len(train_data),
        output_path=os.path.join(OUTPUT_DIR, 'reconstruction_comparison_full_tuned.png')
    )

if __name__ == '__main__':
    main()