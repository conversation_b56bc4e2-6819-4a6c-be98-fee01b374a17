{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Statistical Analysis for TSB-AD"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### TSB-AD-U"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>VUS_PR_Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Sub-PCA</th>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KShapeAD</th>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>POLY</th>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Series2Graph</th>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MOMENT (FT)</th>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MOMENT (ZS)</th>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KMeansAD</th>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USAD</th>\n", "      <td>8.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sub-KNN</th>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MatrixProfile</th>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SAND</th>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CNN</th>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LSTMAD</th>\n", "      <td>13.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SR</th>\n", "      <td>14.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TimesFM</th>\n", "      <td>15.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IForest</th>\n", "      <td>16.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OmniAnomaly</th>\n", "      <td>17.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>g-<PERSON><PERSON><PERSON></th>\n", "      <td>18.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chronos</th>\n", "      <td>19.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TimesNet</th>\n", "      <td>20.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AutoEncoder</th>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TranAD</th>\n", "      <td>22.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FITS</th>\n", "      <td>23.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sub-LOF</th>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OFA</th>\n", "      <td>25.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sub-MCD</th>\n", "      <td>26.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sub-HBOS</th>\n", "      <td>27.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sub-OCSVM</th>\n", "      <td>28.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sub-IForest</th>\n", "      <td>29.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Donut</th>\n", "      <td>30.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LOF</th>\n", "      <td>31.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AnomalyTransformer</th>\n", "      <td>32.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    VUS_PR_Rank\n", "Sub-PCA                     1.0\n", "KShapeAD                    2.0\n", "POLY                        3.0\n", "Series2Graph                4.0\n", "MOMENT (FT)                 5.0\n", "MOMENT (ZS)                 6.0\n", "KMeansAD                    7.0\n", "USAD                        8.0\n", "Sub-KNN                     9.0\n", "MatrixProfile              10.0\n", "SAND                       11.0\n", "CNN                        12.0\n", "LSTMAD                     13.0\n", "SR                         14.0\n", "TimesFM                    15.0\n", "IForest                    16.0\n", "OmniAnomaly                17.0\n", "Lag-Llama                  18.0\n", "Chronos                    19.0\n", "TimesNet                   20.0\n", "AutoEncoder                21.0\n", "TranAD                     22.0\n", "FITS                       23.0\n", "Sub-LOF                    24.0\n", "OFA                        25.0\n", "Sub-MCD                    26.0\n", "Sub-HBOS                   27.0\n", "Sub-OCSVM                  28.0\n", "Sub-IForest                29.0\n", "Donut                      30.0\n", "LOF                        31.0\n", "AnomalyTransformer         32.0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "df_VUS_PR = pd.read_csv('benchmark_eval_results/uni_mergedTable_VUS-PR.csv')\n", "Comparaed_Solution_Pool = ['Sub-IForest', 'IForest', 'Sub-LOF', 'LOF', 'POLY', 'MatrixProfile', 'KShapeAD', 'SAND', 'Series2Graph', 'SR', 'Sub-PCA', 'Sub-HBOS', 'Sub-OCSVM', \n", "            'Sub-MCD', 'Sub-KNN', 'KMeansAD', 'AutoEncoder', 'CNN', 'LSTMAD', 'TranAD', 'AnomalyTransformer', 'OmniAnomaly', 'USAD', 'Donut', \n", "            'TimesNet', 'FITS', 'OFA', 'Lag-Llama', 'Chronos', 'TimesFM', 'MOMENT (ZS)', 'MOMENT (FT)']\n", "\n", "mean_df = pd.DataFrame()\n", "mean_df['VUS_PR_Rank'] = df_VUS_PR[Comparaed_Solution_Pool].mean().rank(ascending=False)\n", "sorted_mean_df = mean_df.sort_values(by='VUS_PR_Rank', ascending=True)\n", "sorted_mean_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAcEAAAFUCAYAAABcEIcYAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABvzUlEQVR4nO3dd1xT1/sH8M8NI+wtCAjuvbfitlQcdQ8UB2q1jjrQaq0LAcX51eLGLSqo1SruiXtX3FW0KIplKciSEdb5/cGP1JgAQW4G5Hm/Xrza3Jx7nxMMeXLOPYNjjDEQQgghGkig6goQQgghqkJJkBBCiMaiJEgIIURjURIkhBCisSgJEkII0ViUBAkhhGgsSoKEEEI0FiVBQgghGktb1RVQtby8PERHR8PY2Bgcx6m6OoQQQnjAGENqairs7OwgEBTe3tP4JBgdHQ0HBwdVV4MQQogCvH//HpUqVSr0eY1PgsbGxgDyf1EmJiYqrg0hhBA+pKSkwMHBQfwZXxiNT4IFXaAmJiaUBAkhpJwp7jYXDYwhhBCisdQqCV67dg29e/eGnZ0dOI5DcHBwsedcuXIFzZo1g1AoRI0aNbB7926F15MQQkj5oFZJMC0tDY0bN8bGjRvlKh8REYFevXqhS5cuePToETw8PDBu3DicO3dOwTUlhBBSHqjVPcEePXqgR48ecpf39/dH1apVsXr1agBA3bp1cePGDfz+++9wcXFRVDUJIYSUE2rVEiyp27dvw9nZWeKYi4sLbt++raIaEUIIKUvUqiVYUrGxsbCxsZE4ZmNjg5SUFGRkZEBfX1/qHJFIBJFIJH6ckpIiV6zMzExERkaWuI6Ojo7Q09Mr8XllPTYf8VUZuzTxKbbyY/MRX9XvN1XS1H9zoIwnwW+xbNkyeHt7l/i8yMhITJgwocTnbdmyBbVq1SrxeWU9Nh/xVRm7NPEptvJj8xFf1e83VSYiTf03BwCOMcZKdQUF4TgOR48eRb9+/Qot07FjRzRr1gx+fn7iY7t27YKHhweSk5NlniOrJejg4IDk5OQi5wkW9gaNjIyEr68v5s+fD0dHR6nnFflNSZ1j8xG/qA8FdX7tFFv5sfmIr+r326tXr1SWiMrjv3lKSgpMTU2L/Wwv0y3Btm3b4vTp0xLHLly4gLZt2xZ6jlAohFAoLHEsPT29It9ojo6OpX4jUuySxVZ1fIqtWbEVHd/R0RFbtmyROi5PIiotdf69KzI2oGZJ8PPnzwgPDxc/joiIwKNHj2BhYQFHR0fMnTsXUVFR2LNnDwBg4sSJ2LBhA3799VeMHTsWly5dwh9//IFTp06p6iUQQsg3UXUy0FRqNTr0/v37aNq0KZo2bQoAmDlzJpo2bQpPT08AQExMjESzuWrVqjh16hQuXLiAxo0bY/Xq1di+fTtNjyCEECIXtWoJdu7cGUXdopS1Gkznzp3x8OFDBdaKEEJIeaVWLUFCCCFEmSgJEkII0ViUBAkhhGgsSoKEEEI0FiVBQgghGouSICGEEI1FSZAQQojGoiRICCFEY1ESJIQQorEoCRJCCNFYlAQJIYRoLEqChBBCNBYlQUIIIRqLkiAhhBCNRUmQEEKIxqIkSAghRGNREiSEEKKxKAkSQgjRWJQECSGEaCxtVVdA3cTFxSE5OVnu8pGRkRL/lYepqSlsbGxKXDdCCCH8oiT4hbi4OLiPGgVRVlaJz/X19ZW7rFBXFwF79lAiJIQQFaMk+IXk5GSIsrIwuW5L2BsaKyRGVFoqNr34C8nJyZQECSFExSgJymBvaIyqxuaqrgYhhBAFo4ExhBBCNBYlQUIIIRpL7bpDN27ciFWrViE2NhaNGzfG+vXr0apVq0LL+/n5YfPmzYiMjISVlRUGDRqEZcuWQU9PT4m15k9JRqd+y8hUgEanEkJIAbVKggcPHsTMmTPh7++P1q1bw8/PDy4uLnj58iWsra2lygcFBeG3337Dzp074eTkhFevXmH06NHgOA5r1qxRwSsonW8dnVqSkakAjU4lhJACapUE16xZg/Hjx2PMmDEAAH9/f5w6dQo7d+7Eb7/9JlX+1q1baNeuHdzc3AAAVapUwbBhw3D37l2l1psvBaNTRzTQgY2hYnqq49LysO9ZFo1OJYQQqFESzMrKQmhoKObOnSs+JhAI4OzsjNu3b8s8x8nJCfv27cO9e/fQqlUrvHnzBqdPn8bIkSMLjSMSiSASicSPU1JS+HsRPLExFMDBhG7XEkKIoqlNEoyPj0dubq5U68TGxgZhYWEyz3Fzc0N8fDzat28PxhhycnIwceJEzJs3r9A4y5Ytg7e3N691J4QQUjaV6ebGlStXsHTpUmzatAkPHjzAkSNHcOrUKSxevLjQc+bOnYvk5GTxz/v375VYY0IIIepEbVqCVlZW0NLSQlxcnMTxuLg4VKxYUeY5CxcuxMiRIzFu3DgAQMOGDZGWloaffvoJ8+fPh0AgneOFQiGEQiH/L4AQQkiZozZJUFdXF82bN0dISAj69esHAMjLy0NISAimTJki85z09HSpRKelpQUAYIwptL6EkNKh6UBEHahNEgSAmTNnwt3dHS1atECrVq3g5+eHtLQ08WjRUaNGwd7eHsuWLQMA9O7dG2vWrEHTpk3RunVrhIeHY+HChejdu7c4GRJC1E9cXBxGubsj64tBavIo6XQgXaEQewICKBGSQqlVEnR1dcXHjx/h6emJ2NhYNGnSBGfPnhW/gSMjIyVafgsWLADHcViwYAGioqJQoUIF9O7du8R/KIQQ5UpOTkaWSASd9t3AmVooJAZL/oSsG+dpOhApklolQQCYMmVKod2fV65ckXisra2NRYsWYdGiRUqoGSGEb5ypBQSW0gth8CFPIVcl5U2ZHh1KCCGElAYlQUIIIRqLkiAhhBCNRUmQEEKIxlK7gTGEKFtJ5qsB3zZnjearEaKeKAkSjZY/X20UskQl274KKNmcNV2hLvYE0PZVhKgbSoJEo+XPV8uCaUdA21QxMXKSgeRrhW9fRSunEKI6lAQJQX4C1LHiFHT1wpfw+9aWaMlXTqGWKCGyUBIkRIUKWqJchzrgzAwUEoMlpSPrehitnEKIDJQECVEDnJkBOEtjhV2flpMnRDZKgkQt0AhNQogqUBIkYqoaoEEjNAkhqkJJkADIT0Tuo0ZBlKXYARpCXV0E7JFMRAX3xZq0AoxNSnQ5uaWmAI/uFT5CkxCimSgJEgD5iUiUlQXnZhzMjRUzSjIxleHig8ITkbEJYGqukNCEECITJUEiwdyYQwUzRU0VAGiIBiGqRfNSJVESJIQQDZF/28MdoixRic4r+W0PIQL2BJSJREhJkBBCNET+bQ8RxtaYCFt9O4XEiMmIxs5w/zJz/52SICEajLrGNJOtvh0cDauouhpqQWFJMDU1FcbGipv8SwgpnfypKe7IEim2a0xXKMSegLLRNUY0D+9J8MOHD/Dz88PmzZuRmJjI9+UJITzJn5oiglb71uBMFTM3hSWnIOvG3TLTNUY0T4mS4IcPH7Bnzx68fv0a5ubmGDhwIJo3bw4AiIqKgq+vL3bv3o3MzEx07txZEfUlhPCMMzUBZ2mh6mooFa1QRArInQTDwsLQsWNHJCQkgLH8Ye4rV67Evn37wHEcxo0bh8zMTAwcOBCzZ88WJ0dCCFEn39oNDJR0hSLqBi4L5E6CCxcuxOfPn7Fp0yZ06NABERERmDFjBjw8PJCcnIzevXtj+fLlqFatmiLrSwghpVLQDWzYYTS0TCsqJEZucizSru+W2Q1MrVD1IncSvHbtGiZNmoQJEyYAAOrVqwdtbW306NED7u7u2LVrl8IqSQghfNMyrQhtS0elxoyLi4O7uztECm6FCoVCBFArVC5yJ8GEhAQ0atRI4ljjxo0BAP379+etQhs3bsSqVasQGxuLxo0bY/369WjVqlWh5ZOSkjB//nwcOXIEnz59QuXKleHn54eePXvyVidCCOFDcnIyRCIRnNv+DAsTe4XE+JQShYu3N9JgJDnJnQTz8vKgo6MjcazgsZGRES+VOXjwIGbOnAl/f3+0bt0afn5+cHFxwcuXL2FtbS1VPisrC99//z2sra1x+PBh2Nvb4927dzAzM+OlPoQQoggWJvaoYFFV1dUgKOHo0Pv370NPT0/8ODU1FRzH4caNG0hKSpIqP2DAgBJVZs2aNRg/fjzGjBkDAPD398epU6ewc+dO/Pbbb1Lld+7ciU+fPuHWrVvihFylSpUSxSSEEKK5SpQE/fz84OfnJ3Xcy8tL6hjHccjNzZX72llZWQgNDcXcuXPFxwQCAZydnXH79m2Z5xw/fhxt27bFzz//jGPHjqFChQpwc3PDnDlzoKWlJXdsQgghmknuJHj58mVF1gPx8fHIzc2V6sO2sbFBWFiYzHPevHmDS5cuYfjw4Th9+jTCw8MxefJkZGdnY9GiRTLPEYlEEjelU1JS+HsRhBBCCqWOy/TJnQQ7depUooooQ15eHqytrbF161ZoaWmhefPmiIqKwqpVqwpNgsuWLYO3t7eSa0oIIZpN2TtY6Ovry1W+RN2hmZmZOHbsGCIiImBpaYkffvgBtra2JapgYaysrKClpYW4uDiJ43FxcahYUfZcHltbW+jo6Eh0fdatWxexsbHIysqCrq6u1Dlz587FzJkzxY9TUlLg4ODAy2sghBAiW8EOFpNq9IOdvpVCYkRnxGNzeDCSk5P5T4IfPnyAk5MTIiIixCvGGBgYIDg4GM7Ozt9W4y/o6uqiefPmCAkJQb9+/QDkt/RCQkIwZcoUmee0a9cOQUFByMvLg0AgAAC8evUKtra2MhMgkD9/RigUlrq+hBBCSs5O3wpVjfhpPPFBIG/BxYsX4+3bt5gxYwZOnjwJPz8/6OvriyfP82HmzJnYtm0bAgIC8OLFC0yaNAlpaWni0aKjRo2SGDgzadIkfPr0CdOnT8erV69w6tQpLF26FD///DNvdSKEEFJ+yd0SPH/+PEaNGoX//e9/4mM2NjZwc3PDy5cvUbt27VJXxtXVFR8/foSnpydiY2PRpEkTnD17VnyTMzIyUtziAwAHBwecO3cOM2bMQKNGjWBvb4/p06djzpw5pa4LIYSQ8k/uJBgZGSmVXNq3bw/GGOLi4nhJggAwZcqUQrs/r1y5InWsbdu2uHPnDi+xCSGEaBa5u0NFIpHERHkA4sc5OTn81ooQQghRghKNDn379i0ePHggflww3+Off/6RuVRZs2bNSlc7QgghRIFKlAQXLlyIhQsXSh2fPHmyxGPGWIlXjCGEEEKUTe4kSFslEUIIKW/kToLu7u6KrAchhBCidHIPjClKdnY2rl27VqLdkgkhhBBV4yUJfvr0CV26dEFoaCgflyOEEEKUgpckCEC8lBohhBBSVvCWBDmO4+tShBBCiFJQS5AQQojGKtE8wcJUqFABERERhW55RAghhKgjXlqCAoEAAoEAjx8/xqdPn/i4JCGEEKJwcifBu3fvwsfHB/Hx8RLHo6Oj0alTJ1SpUgVt27aFjY0NZs2axXtFCSGEEL7JnQQ3bdqEoKAgWFlJ7gg8atQoXL9+HR07dsTMmTPRoEED/P7777TCDCGEELUn9z3BO3fuoGfPnhLHXr58iUuXLqFnz544efIkgPyJ861atcKOHTvEm+ESQggh6kjulmBMTIzUnoGnTp0Cx3GYOHGi+JiOjg6GDRuGZ8+e8VdLQgghRAHkToI6OjpS+wbevHkTANCuXTuJ49bW1sjMzOSheoQQQojiyJ0Ea9asiUuXLokfZ2Rk4MqVK2jWrBnMzc0lysbGxsLGxoa/WhJCCCEKIPc9wcmTJ2P06NGYNGkSnJyccOjQISQlJWHs2LFSZUNCQlC/fn1eK0oIIYTwTe4kOHLkSNy7dw+bN2/Gli1bAOSPDJ00aZJEuRcvXuDSpUtYu3YtvzUlhBBCeCZ3EuQ4Dhs2bICnpyciIiJQuXJlmSvEWFhY4N69e1KDaAghhBB1U+Jl06ytrWFtbV3o8zY2NnQ/kBBCSJkgdxKMjIws9DmO46CnpwcrKyvaTYIQQkiZIXcSrFKlSrEJzsDAAC4uLvD19aXuUEIIIWpP7iS4cuXKIpNgeno6wsLCcPLkSVy6dAl37txBrVq1eKmkMolEIryMi0Zq6mfxMTN9fTiaWyEzOxuvPsZKndPIzgEAEB4fh/SsLInnHMwsYG5giIS0VEQlJyE6PQUikUixL4IQQohc5E6C8i6KHRkZiebNm8PHxwf79u37pkpt3LgRq1atQmxsLBo3boz169ejVatWxZ534MABDBs2DH379kVwcPA3xY6OjsbY27cljg1o1AIbBrojJiUJ3beslD7Hez0AYMbRfQj9963Ec+sHjMLAxi1x/NlDzD99CABQuXLlb6obIYQQfvGyn+CXHB0dMX78+G9eQPvgwYOYOXMm/P390bp1a/j5+cHFxQUvX74sckDO27dvMWvWLHTo0OFbqw4AsLOzw6KOPWBnYCI+ZqavDwCwNTHD2Qm/Fnru7/1HyGwJAkCfBk3R3KEqotNTsC38canqSAghhB+8J0EAqFq16jfvK7hmzRqMHz9evPi2v78/Tp06hZ07d+K3336TeU5ubi6GDx8Ob29vXL9+HUlJSd9adQiFQtS2sUNVY3Op5/R0dMRdn7LUsCp8VKyloTEsDY1hnJoI4fuwb64fIYQQ/igkCb59+xYWFhYlPi8rKwuhoaGYO3eu+JhAIICzszNuf9VF+SUfHx9YW1vjxx9/xPXr14uMIRKJJO7JpaSklLieiiQSiRAeK8Ln1P/uv5ro68DewgCi7Fy8+fBZ6py69qYAgLcfPyMjK1fiOTtzfZga6OJTmghxSZmITWOgW5KEEJKP9yT4/v17bN26Fd9//32Jz42Pj0dubq7UPEMbGxuEhcluPd24cQM7duzAo0eP5IqxbNkyeHt7l7huyhIdHY0Zt99JHOvZ2A5LhzRGXEomhm26JXXOI98eAADPP5/iyfskied8BzdCryb2OP80FstPPAdA9yQJIaSA3ElwzZo1RT6fkZGBly9f4sSJEwAALy+vUlVMHqmpqRg5ciS2bdsmtdlvYebOnYuZM2eKH6ekpMDBofAuTmWzs7ODR2c7VDSUbAkCgI2JHvZPdir0XJ+BDWW2BAGgW8OKaOxghtg0hj9eyT5fJBIhMiYHqan/HTMy0IG1pT6ysnPxb2ya1DnVHPLvnUZ/SEOmSDK2tYU+jAx1kPI5C/GJmUj8DIhECul8IISQb8Lr6FADAwN069YNS5cu/abpEVZWVtDS0kJcXJzE8bi4OJlLtL1+/Rpv375F7969xcfy8vIAANra2nj58iWqV68ucY5QKIRQKCxx3ZRFKBSiRkUhHEykN/gQ6miJuz5lqVLBqNDnLAyFsDAUwiglD8J3svtDo6OjseKrVmj75hUxbUR9fEoS4bc1f0md88fv3wEANgY9xz/vJLuWpwyvh44tbHHrURx2/pmfeQtrhYpEIsREZ0kkYH0DbVha6SM7OxdxMelS51RyNAYAfIhLR9ZXCdjCUg8Ghjr4nJqFpEQRPqcCIpFuobET/82C9uf/vnjoGmrDqII+crNykRwtHduiSn7slJh05HwV29BKD0IjHWSmZCH9kwg5SazQ2IQQ1ZI7Cd6/fx+WlpaFPq+np4cKFSpAIJB7dyYpurq6aN68OUJCQtCvXz8A+UktJCQEU6ZMkSpfp04dPH36VOLYggULkJqairVr16pVC68ssLOzw1AXa5h/kUuNDPJboRZmQiyf2bLQc392qyezJQgATk1sUKuyKRI/A9eeyn7LRUdH4/ZXCbhZS2u4ja6H5EQR/FaESp3zv42dAQAH94bhXYRkAh7mXgfNW1XE4wcfcfSPfwAUnoCjo6Px7nfJ2JXb2qDtxHpITxTh/KL7UucMDegCALi77QUSXkvGbvNTXVRpVxHv731A6N6iYwP5STjn3zjgi7mpOoZ60K9gjtysHKRFf5Q6x6SKLQAgLSYeuaJsief0rcygY6SPrJQ0ZH5KAZLToE03ggmRSe4k6OTkhB49emD48OHo3bu3wlpTM2fOhLu7O1q0aIFWrVrBz88PaWlp4tGio0aNgr29PZYtWwY9PT00aNBA4nwzMzMAkDpOiicUCuFoq48KZtKLIujqaIm7PmWxszYs9DkTI12YGOniYxKD8FWe7PPt7PB9LysYGf93TN8g/+1pai6Ex5zmhV7fdWQdmS1BAGjcrAIqVzXB51Tg74eyW2N2dnaoPsAK2maSLUEAMDAXopt3i0Jjtx5fV2ZLEAAcWlnDsoYpcpIYMu4U3hLMT8KSA78qtm2I+hMGQJSYgr+8tkqd893uRQCA59uPIeX1vxLP1fupP2ydGiHu3t94te8MALoPTEhh5E6CAwcOxPHjx3H8+HEYGxtjwIABGD58OLp27crreqGurq74+PEjPD09ERsbiyZNmuDs2bPiwTKRkZGlam0S9SQUCmFrJ4Sp9MwU6Ohoibs+ZbG2MSj0OSNjXRgZ6yI5EQh/Xnhs80pC6FhJv4+1dLXEXZ+ymNgWHlvPRBd6JrrIjmfIK+I7o52dHawHtAdM//sioWOYn0iF5iZo6fVToefWG9dXZksQAGxa1YdpDYf8luCdNzLPF4lESP83CtznL1qhBgbQr2CF3OxspEXHSJ1jUtkRAJAWE4vcr+bF6ltaQsfIEFkpqchMTARLSoYWtUKJGpM7CQYGBiIjIwPBwcEICgpCYGAgAgICYGNjg2HDhsHNzQ3Nmxf+bb0kpkyZIrP7EwCuXLlS5Lm7d+/mpQ6EKItQKIR+JRtwltLJVktXW9z1KYuhbeEDwnRNDKFrYgiWkIo8YZTMMtHR0Xi39qtWaJtWaPjTGIg+JeKu9zKpc77fuRkA8PfOPUh+HSHxXIPxo2HbtjXi/gpFWOBBAEXfB878NxLc5/9uBOsYGMGggg1ys7PwOfq91DmmlfPv8X+OiUJuVqbEc/qW1tA1MkZWSjIyEuPBkhLBUQImxSjRUD19fX0MGzYMw4YNQ2JiIv744w8EBQXBz88Pfn5+qFmzJkaMGAE3NzdUq1ZNUXUmhPDEzs4ONv16gTP7b8CVjkF+61ZoYY7Wi+YWdirqjx0lsyUIADYtm8O0RrX8luC9hzLPz0/ASyWO2bbpiMY/eSDzUwJue8+WOqf7ziMAgKc71yP5teQw50bjp8OubSfE/HULLwK3AaBuYFK8bx6vbm5ujgkTJmDChAmIiopCUFAQ9u/fD09PTyxatAitW7fGrVvSc9oIIepDKBRCu5I9OEvpxS20dHTEXZ+yGNpKj9guoGtiDF0TYzCjT8h5LLsf2s7ODrb9hoMz+68PXMcgf1SWnoUl2i5aVej1G46dKrMlCAC2LZ1gVqNWfkvw3mWZ54tEIiT9+w+0vhiMpGtoAsMKlZCbLUJK1Gupc8yr1AMApMZEIEeUIfGcoZUddI3MIEr5hPRPschNjkF2Ia1QkUiEyKiXSP2iBWxoYAYbK0dkZWfi3xjpOUzVHBsBAKJiwyHKkhytXMHSAcaG5khOjUdCYjQSk6Npkf4S4GXSlr29PWbPno3u3bvD09MTx44dw927d/m4tEpEpaUWX0gNr01IWSIUCqFbyRECS+k1gbV0dMVdn7IY2doX+pyuiSl0TUyRZ/QBWY9l34yNjo7GO7+pEscc2vyAlhNWIuNTLC57D5I6Z8Cu/GQeumMePr2WXP+3xfjlcHTqg3//OovH+5YAKHo08vLbYySOdWg1ANN+3IiExBjM8XWROufQlvx7sxt3e+CfCMmR0lPHrEfHNoNw+/4J7Dgwr8jYQH4S/ufDS6Tq//dZZKJnCgczR4hyMhEe/4/UOfUrNgQAvEl4jYxsySRsb+oAM30zfEpPQExKNGIzylYSLnUSjIyMFLcCnz17BsYYnJycMHz4cD7qpxKbXkjPhyOElB92dnao3H8WtEz/u9+qa5g/+lnfoiK6LDpc6LnNf1wqsyUIAJVadodF9Sb5LcG7QYXGHtbbC+amdv+db2AGALA0t8WK+ecKjf3zaD+ZLUEAaNuiN2pVb47E5GhcDZUeUVwgOjoak2+PlzjWu35//K/3WsSmxmLA7l5S57z8LX9T9bmnfsGj6AcSz638wQ99GwzAmRcn4XNhIYCi7wO//BCBz1+0gk31jOFoZovMnCz8E/9W6pyGFfPnnL9OeI/0bMnfeyXTijDXN0FCehKiUz4gKj2+xAn4m5JgfHy8+H7g7du3wRhDnTp14OPjg+HDh6NKlSrfclm1MbluS9gbFj4isDSi0lLVOskmprIyeW1CSkIoFMKkUk1oW0p392rpCMVdn7IY21Yt/LomFhCaWCAnwQgpj2S3QoVCIRzta6OChfR1dHX0xF2fsthXrFHoc6bGVjA1tsLHT8YQPit8OLKdnR3mtFmAivr/JWETvfx7whWNK+LI6FOFnrus12qZLUEA6FH3BzSxb4bYjGjsjdwp8/zo6GiMu71Q4lj/+t9hfZ95iEn5iB67Jkmd8+/cEADAjJMr8CD6hcRza3v/hoENvseJF1ew4Hz+lnYlvQ8sdxJMS0vD0aNHERQUhJCQEGRnZ8PW1hYeHh4YPnw4mjVrVqLA6sze0FjmLhKa4OIDBoCSFSHllVAoRE3r2nA0rCL9nLaeuOtTlmqWhXdRWxhYwsLAEsZpxhDGyU7CdnZ2WNhmHOwN/hvVbKqX3+CwNamAM2M2F3r933+YI7MlCAC963ZGc/t6iEqPx47I04VeQxa5k6C1tTUyMzNhZGQENzc38RxBmrNXvjg342BuzN+8zy8lprL/T7KEEE0kFApR27oqqhpJT/vR09YVd33KUt2y8BXALA3MYGlgBqPPhSfgwsidBJ2dnTF8+HD06dMHenp6JQpCyg5zY07mijH8oSRICFEfcifBY8eOKbIehBBCiNJRXyYhhBCNRZu7EbXxOaX4Mup4bUJI2UVJkKiNh/dUXQNCiKahJEjURtNWgFHhuzWVyucUSrKEEGmUBInaMDKBzK2UlCEnCVDUyNX8axeNJUnvXs8XRV6bkLKOkiAhAJKvqzY+ux5Gk0cIUQFKgoQAMO0AaJsp5to5ScUnWa5DHXBmhW/QWxosKR3sephCrk1IWUdJkBDkJ0BZO8vzo/g2HmdmIHNTXeXVgBDNREmQEKKRcpNjy+S1Cb8oCRJCNFLa9d0qi52YElUmr10eURIkhGgkww6jofX/uxDwLTc5tsgke+H2RoXEJSVHSZAQopG0TCvK3E9QGb5v+zPMTewVcu3ElChKsiVASZAQQpTM3MRe5qa6RPkoCaqhuLS8MnltQggpaygJqqF9z7JVXQVCCNEIapkEN27ciFWrViE2NhaNGzfG+vXr0apVK5llt23bhj179uDZs2cAgObNm2Pp0qWFli8LRjTQgY2hYna5ikvLoyRLCFGZ6Ix4tbq22iXBgwcPYubMmfD390fr1q3h5+cHFxcXvHz5EtbW1lLlr1y5gmHDhsHJyQl6enpYsWIFunXrhr///hv29oq58axoNoYCOJjQVo+EkPJnc3iwqqsgQe2S4Jo1azB+/HiMGTMGAODv749Tp05h586d+O2336TKBwYGSjzevn07/vzzT4SEhGDUqFFKqTMhZRlLVtxmi4q8NimbJtXoBzt9K4VcOzojvsRJVq2SYFZWFkJDQzF37lzxMYFAAGdnZ9y+fVuua6SnpyM7OxsWFhaKqiYh5UrujbuqrgLRIHb6VqhqZKvqaoipVRKMj49Hbm4ubGxsJI7b2NggLEy+BYDnzJkDOzs7ODs7y3xeJBJBJBKJH6ek0DdVotm02rcGZ6qYjRxZcgolWaLW1CoJltby5ctx4MABXLlyBXp6ejLLLFu2DN7e3kquGSHqizM1AWdJPSeaJCYjukxeWxHUKglaWVlBS0sLcXFxEsfj4uJQsWLRyxv973//w/Lly3Hx4kU0atSo0HJz587FzJkzxY9TUlLg4OBQuooTQkgZsjPcX9VVUBtqlQR1dXXRvHlzhISEoF+/fgCAvLw8hISEYMqUKYWet3LlSvj6+uLcuXNo0aJFkTGEQiGEQiGf1SaEkDJlbI2JsNW3U8i1YzKiy1SSVaskCAAzZ86Eu7s7WrRogVatWsHPzw9paWni0aKjRo2Cvb09li1bBgBYsWIFPD09ERQUhCpVqiA2Nn8LEyMjIxgZGansdRBCiLqy1beDo2EVVVdDLahdEnR1dcXHjx/h6emJ2NhYNGnSBGfPnhUPlomMjIRA8N8cus2bNyMrKwuDBg2SuM6iRYvg5eWlzKoTQkooL/lTmbw2KT/ULgkCwJQpUwrt/rxy5YrE47dv3yq+QoQQhci5cV7VVSAaTi2TICFEM2i37waBqWJGpuYlf6IkS4pFSZAQojICUwsILKWXQyREWWiBSkIIIRqLkiAhhBCNRUmQEEKIxqIkSAghRGNREiSEEKKxKAkSQgjRWJQECSGEaCxKgoQQQjQWJUFCCCEai5IgIYQQjUVJkBBCiMaiJEgIIURjURIkhBCisSgJEkII0Vi0lRJRG6kpZfPapGzKTY4tk9cm/KIkSCQkpjKlX9vU1BS6Ql08upelsNgAoCvUhampqUJjEPWX/34TIu36boXG0RUKC32/fUqJUlhcRV67PKIkSADkfzAIdXVx8UEWAMUlQqGudCKysbHBnoA9SE5Olvs6kZGR8PX1xfz58+Ho6CjXOaamprCxsSlRfTUBS1ZcM1mR1/5W+e+3AJW830xNTSEUCnHx9sYS1bmkhEUkYCKJkiABkP/BELBH/kT0LR8KQOGJyMbG5psSlKOjI2rVqlXi88h/LaKsG3cVGqeoFpGqqOr9ZmNjgwAVJWAiGyVBIvYtHwyUhMqukraI+P7io6noC596oSRIiAajLz5E2aIz4tXq2pQE1VBcWl6ZvHZZlpMMKOpeaI4cDS2WlK6Q2Iq+NimbYjKilX7t/HEHQmwOD1ZYbAAQ6pas+52SoBopGJyy75liR0nKGpyiqQpGpiZfU83I1IL4WdfDFDgciUbGknwFiWhnuL9C48hKRPnjDpTX/Z6SIt+gLEqCakTVg1M0kapHppY0Pv2bk9JQdiKSFV/dut/VMglu3LgRq1atQmxsLBo3boz169ejVatWhZY/dOgQFi5ciLdv36JmzZpYsWIFevbs+c3xo9JSv/nc0l5bHd8k5Z2qBypo8r85S/4ERXXQs+RPCrpy2abJ7zdZ1C4JHjx4EDNnzoS/vz9at24NPz8/uLi44OXLl7C2tpYqf+vWLQwbNgzLli3DDz/8gKCgIPTr1w8PHjxAgwYNShS7oDty04u/+Ho5MlF3JNF0/03POK/QOOo4PYOoF7VLgmvWrMH48eMxZswYAIC/vz9OnTqFnTt34rfffpMqv3btWnTv3h2zZ88GACxevBgXLlzAhg0b4O9fsn7vknZHAjSHh5BvQdMziLpQqySYlZWF0NBQzJ07V3xMIBDA2dkZt2/flnnO7du3MXPmTIljLi4uCA4O/qY6FNZVkJmZicjIyBJfz9HREXp6et9UF3VQ2OsuOFbU76Ssv3ZN9a3/5iX991a3brmi/sb5fu0lia+M2Kqk6s8YtUqC8fHxyM3NlfrDsLGxQVhYmMxzYmNjZZaPjZW9gK1IJIJIJBI/lncEUWRkJCZMmFDo876+vjKPb9mypdR/tKr84/jW1w2U/rVr8odSWfw3V+V7HSj9ay/udQOKfe1l8fde1j9jAIBjjClyZHaJREdHw97eHrdu3ULbtm3Fx3/99VdcvXoVd+9KL++kq6uLgIAADBs2THxs06ZN8Pb2RlxcnFR5Ly8veHt7Sx1PTk6GiYlJoXVTZUvw1atXxf5xyqLIPw55lPa1f+vrBvh57ar8vZfFf3NVvtcBxX7pKo4iv/goI3ZZfL8BRb/2lJQUmJqaFvvZrlZJMCsrCwYGBjh8+DD69esnPu7u7o6kpCQcO3ZM6hxHR0fMnDkTHh4e4mOLFi1CcHAwHj9+LFVeVkvQwcGh2F+UKlFXbMmV9Q8l+jcvubL+2lWpPL7fymQSBIDWrVujVatWWL9+PQAgLy8Pjo6OmDJlisyBMa6urkhPT8eJEyfEx5ycnNCoUSO5BsbI+4sihBBSdsj72a5W9wQBYObMmXB3d0eLFi3QqlUr+Pn5IS0tTTxadNSoUbC3t8eyZcsAANOnT0enTp2wevVq9OrVCwcOHMD9+/exdetWVb4MQgghZYDaJUFXV1d8/PgRnp6eiI2NRZMmTXD27Fnx4JfIyEgIBAJxeScnJwQFBWHBggWYN28eatasieDg4BLPESSEEKJ51K47VNmoO5QQQsofeT/bBYU+QwghhJRzatcdqmwFDWF55wsSQghRfwWf6cV1dmp8EkxNzV/Q2sHBQcU1IYQQwrfU1NQi14/V+HuCeXl5iI6OhrGxMTiOK/H5BfMM379/r/R7ipoaW9XxKbZmxVZ1fIr9bbEZY0hNTYWdnZ3EYMqvaXxLUCAQoFKlSqW+jomJicoG1mhqbFXHp9iaFVvV8Sl2ycmzgwgNjCGEEKKxKAkSQgjRWJQES0koFGLRokUQCoUUW0PiU2zNiq3q+BRbsbE1fmAMIYQQzUUtQUIIIRqLkiAhhBCNRUmQEEKIxqIkSAghRGNREiSEEKKxNH7FGFK0jIwM6Ovrq7oaaiEzMxMcxyl1uPjz58/x/PlzxMfHg+M4WFlZoW7duqhXr57S6kBIeUZTJBQgMTER5ubmqq4GL6pVq4bNmzfDxcVF1VVRuitXruDYsWO4efMmnj9/joyMDACAgYEB6tatCycnJ/Tr1w+dO3fmPe7u3btx4sQJJCUlSa2Cz3EcTE1N0bt3b4wZM4b3+LJ8/vwZqampMDY2hpGRkcLjfRn37du34thVq1aFoaGh0uKrmkgkwoMHD/Dhwwe0a9cOVlZWqq5SuUNJkCcikQjHjx9HYGAgzp49i8zMTN5jxMXFYcOGDTh//jxev34t/mCoUaMGunfvjsmTJ8Pa2prXmI0aNcLff/8NV1dX+Pn58X79knr+/DnevHmDxMREmVukjBo1qlTXz87OxpYtW7BmzRq8ffsWFhYWaNasGapVqwZzc3MwxpCYmIiIiAg8ePAAnz59QuXKlfHLL79gwoQJ0NHR+ebYZ8+excKFCxEaGooGDRrg+++/R/PmzWXGDg0NxYULF/Ds2TM0a9YMvr6+vH9Refr0KVauXIkLFy7g48eP4uPW1tZwcXHBrFmz0KBBA15jFjh79ix8fX1x584d5OXliY9raWnByckJ8+fPx/fff89rzGnTppWoPMdxWLt2La91+NK6devg5eWF5ORkAMCFCxfQtWtXxMfHo06dOli5ciXGjh3La8zU1FTcuHFD6vOlffv2Cvny06hRoxKV5zgOjx8/5rcSjHyzvLw8duHCBTZ69GhmamrKOI5j1tbWbMyYMbzHunTpErOwsGAcxzEDAwPWpEkT1r59e9akSRNmYGDAOI5jVlZW7Pr167zGzcnJYStXrmRGRkbMwsKCbdu2jdfryys8PJy1adOGCQQCxnGczB+BQFDqOI6OjqxixYpszpw5LDQ0tNjy9+/fZ3PmzGG2trascuXKpYptaGjIpk+fzl68eCH3OS9evGDTp09nxsbGpYr9tQMHDjA9PT3GcRyrUaMG69evHxsxYgTr168fq1GjBuM4junp6bHDhw/zGpcxxtasWcMEAgHT0dFhzs7OzMPDgy1YsIB5eHiw7777juno6DAtLS22bt06XuMW9r6S9T4r+FGUnTt3Mo7j2LBhw9iuXbsYx3EsJCRE/PzgwYPZ999/z1u8nJwc9ttvvzEjIyOpvzGBQMCMjIzY/PnzWW5uLm8xGWOsU6dOrHPnzsX+1KlTh7e/8a9REvwG9+/fZzNmzGC2trbifxg3Nzd269YtlpeXx3u8jx8/MisrK2ZjY8P279/PsrOzJZ7Pzs5mQUFBzNramllbW7P4+Hje6/Du3TvWp08fxnEc69ixY4k+qPnw3XffMUNDQ7Zu3Tr28OFD9vbtW5k/peXv788yMzNLfJ5IJGL+/v6lip2QkKCSc78WGRnJDA0NWe3atdnt27dllrl16xarVasWMzIyYu/fv+ct9vPnz5mOjg5zcnJikZGRMsu8e/eOOTk5MV1dXaW/D4ODg1nTpk0Zx3GsZs2aCotTv3591q9fP8YYY/Hx8VJJcPny5czOzo63eK6urozjOFa/fn22ZMkSFhwczC5evMiCg4PZ4sWLWd26dZlAIGDDhw/nLaY8YmJimIeHB9PX12fa2toKaWBQEpTT69evmY+PD6tduzYTCATMwcGBzZo1i/3xxx+M4zj2559/Kiz20qVLma6uLnvy5EmR5R49esR0dHTY8uXLFVaXY8eOMUtLS6atrc2MjY2lfkxMTBQSV09Pj/n4+Cjk2kTSvHnzmKGhYaFJqMDbt2+ZgYEBW7BgAW+xp0+fzszNzdmnT5+KLJeQkMDMzc3ZzJkzeYtdlODgYNasWTMmEAhYzZo12e7du1lOTo7C4gmFQrZlyxbGmOwkuHXrViYUCnmJdeHCBcZxHJsyZUqhLb3c3Fw2efJkJhAIJOqhKLGxsczDw4MZGBgwHR0dNmbMGBYeHq6QWDQ6VA5t27bFvXv3YGVlhUGDBmH79u1o3749AOD169cKj3/+/Hn07t0bDRs2LLJc48aN0adPH5w9exZz5szhvR7x8fE4dOgQPn36hEaNGqFp06a8xyiMlZWVXHuD8UFLSwt79+6Fm5ubUuLJKz4+HmfOnEFMTAxq166N3r17F7lZ6Le6cuUKBgwYAAcHhyLLVa5cGQMHDkRISAgWL17MS+wbN25g8ODBxQ4ss7CwwODBg3H16lVe4hbm2LFj8PHxwcOHD1GjRg3s2rULI0aMUMjv/UtmZmaIj48v9Pnnz5+jYsWKvMTas2cPqlevjnXr1hW6sbhAIMD69etx/vx5BAQEoGvXrrzE/lpsbCxWrFiBrVu3Ijs7GyNGjMCCBQtQrVo1hcQDQPcE5cFxHKtWrRoLDg6W6ooMDw9XeEvQxsaGrV69Wq6ya9asYTY2NrzXYdu2bczS0pIZGRmx1atX835voDhLlixhLVu2VOi37wIcx7HAwECFx5ElKCiIde3alX38+FHi+K1bt5ilpaX4fo1AIGBt2rRhnz9/5r0OVlZWct9vW7duHbOysuIttoWFBdu4caNcZTdu3MgsLCx4i/2lo0ePsqZNmzKBQMBq1arF9uzZo9T3/JgxY1jlypVZYmKiVEvw2bNnzNDQkE2dOpWXWLVr12a//vqrXGV//fVXVrt2bV7ifikmJoZNnz5d3PIbO3Yse/PmDe9xZKGWoBw2bNiAoKAg9O/fHxYWFhg4cCCGDh2qlKHpAJCUlIQKFSrIVdbS0hJJSUm8xX7+/DkmTpyIGzduoGfPnti0aRMcHR1llo2KisK7d+/g5ORU6rhHjhyReFy7dm3k5uaicePGGDt2LBwcHKClpSV13oABA0odW5X279+P7OxsiaHwjDGMHDkSycnJ8PT0RIsWLXDq1Cn4+/tj5cqV8Pb25rUOycnJck/xMTMzQ0pKCm+xU1JS5G7xm5iY8BobAI4ePQofHx88fvwYNWvWxO7duzF8+HCFt/y+tmTJErRu3RoNGjRA7969wXEcAgICsHPnTvz555+wtbWFp6cnL7FiYmJQo0YNucrWqFEDMTExvMQtiL18+XJs27YNOTk5GDVqFObPn4+qVavyFqM4lATlMHnyZEyePBkREREIDAxEUFAQtm3bhooVK6JLly7gOK7QbgQ+ZGVlyfzAl0VLSwvZ2dm8xW7atCksLS1x8OBBDB48uMiyu3fvhqenJ3Jzc0sdd9CgQeA4TjwN4sv/nzVrlsxzOI7jJXbBtVTh8ePHGDlypMSxW7du4c2bN5gyZQoWLVoEAOjVqxf+/fdfHDlyhPckmJOTI/eHvkAgQE5ODm+xc3Nz5f7dcxwnMX2itJo0aYKnT5+iVq1a4u5wVb0P7OzsEBoainnz5uHgwYNgjGHv3r0wNjbGsGHDsHz5ct7mDH7+/FnuuZcGBgb4/PkzL3EBoHr16hCJRGjSpAnmzZuHqlWrIjExEYmJiYWe06xZM97iA5QES6Rq1apYsGABFixYgNDQUAQGBorfoJMnT8aZM2fQp08fODs7Q09Pj9fY9+/fl+uaf/31F69xx44di+XLlyvtflyBy5cvKzXe15YsWYJt27bJVZbjOISEhPAS98OHD1Lfgs+fPw+O4+Dq6ipx/Pvvv8dvv/3GS9yvnT59GrGxscWWCw0N5T32nj17cOfOnWLLvXr1ite4T548AQBERkZi0qRJmDRpUpHlOY4Tz+FTBGtra2zfvh3bt2/Hx48fkZeXhwoVKvDeKmWMqSzZF8ynfvjwIYYMGVJk2YJ68vVFtwBNli+lvLw8XLp0Cfv27cPRo0eRmprK+7elkr7pFfFGkYevry9vLUFVEggEqFSpUokS/9OnT3mJXalSJXh4eEi0dr/77jvcvHkTSUlJEl+Edu7ciWnTpvH6XgNU+35TZezRo0eXOBns2rWLl9iqJBAI4ODgINf7PTk5Gf/++y9vv/OAgIASn+Pu7s5L7ALUEiwlgUAAZ2dnODs7w9/fH8eOHUNQUBCvMVTdKlInubm5CA0Nxdu3bwEAVapUQfPmzeXuLpbX8uXLVTI6tFGjRjhw4AA8PDygra2NqKgo3Lx5E126dJHqCXj9+jXs7Ox4r0NERATv15QXn92bJbV7926Vxfbx8SnxORzHYeHChaWO3bFjR7mTv6WlJa8jNflOaN9EKcNvyoGoqCgWFRVVbJno6Ggl1Uj9LFmyRKGraOzatYtVrFhRYkULgUDAbGxs2I4dO3iLo8rRodevX2cCgYA1bNiQjR07llWuXJkJBAJ2/vx5qbINGzZkbm5uKqilJGWPFP5ScfMJywp5V6vhe4UkQqND5RIaGorWrVtj5cqVmDlzZqHlDhw4gDlz5uDBgwfFzulThNjYWBw4cACBgYG83Rv8epRmUf7++29eYsqyZcsWTJo0CU2aNIGXlxdq1aoFAHj58iW2bNmC8ePHIysrCxMnTlRYHZShffv2OHDgALy9vREUFITKlStj+/btUutkXrp0CREREViwYIGKapp//7ngvjifIwaLo6h1ej99+lTicywsLHiJrcoWsCqV5POlAO8jwFWdhcuCMWPGsLp16xa7JFpeXh6rX78++/HHH5VUM8ZSU1PZ7t272ffff8+0tbUZx3GsevXqvF2/4Bunqr+dVq1alXXs2JFlZWVJPZeVlcXat2/PqlatykssVbYE1d0///zDvLy8WK1atcTre3bp0kXhcZWxTu/X64IW96OlpcVbbE0l7+eLIn/n1BKUw+XLl+Hu7l5svznHcRg8ePA33ewtiZycHJw+fRqBgYE4efIkMjMz0bBhQ/j4+KBPnz6oX78+b7HU5X5kbGwsfvnlF5m7NOjo6GDo0KH49ddfeYl15swZ3odhK0JGRgY+fvxY6LxNvnz48EHcw3D//n0A+YN1vLy80LNnT4WOHC4YhX3gwAHExsaC4zgMHToUU6ZMQZs2bXgd1ejp6Vns9RhjOH78OB49esRbXFUzNjYu0e+Rz1Gx8ny+xMTEYOXKlXj06BHv9/4BGhgjl5iYGFSpUkWuso6OjoiOjlZIPa5fv47AwEAcPnwYnz59QvPmzTF58mSsXr0anp6eCpko3qlTJ96v+S2aNm1a5JD4V69eoUmTJrzE6tmzp8SyaRkZGVi1ahVGjRol9/vgW2lpaaFNmzbYv39/scntyJEjGDVqlEJG46alpeHIkSMIDAzEpUuXoK2tjV69emHo0KH45ZdfMHHiRIUtTPDmzRsEBgYiMDAQ//zzD+zt7TF8+HC0atUKrq6uGDhwINq2bct7XC8vryKfP378OLy9vfHo0SNUr16d167oqlWrQiAQICwsDDo6OqhatapcX7r5WLZx4MCBciXBR48e4fHjx7x+8Sjq8yUuLg4rVqzAli1bkJWVBXd3d4V0/1MSlIOhoaHc9wsSExNhYGDAa/y5c+di//79iIyMRN26deHh4YGhQ4eiRo0aeP36NVavXs1rvKKIRCKEh4cjJSUFxsbGqFmzplJ2Wl+/fj169eqFatWq4aeffhLvdp+RkQF/f3/88ccfOH36NC+x2FezhtLT0+Ht7Y327dsrPAkyxnD79m00adIE27Ztw8CBAxUaT5Zhw4bhxIkTyM7OhrOzM3bu3Il+/frByMgIr1+/xi+//KKw2Kpep1eWgvVDC5JfwSoyfLZKOnXqBI7jxFNECh4rQ3GjYh89egRvb288fvwYpqam8PDwUGh94uLisHz5cuWtH8p7B2s5VLCnlTy6dOnCOnXqxGv8grVLr1y5IvWcMtYuZYyx27dvsx49ejChUChxX0QoFLKePXuyO3fuKDR+w4YNmb29PRMIBExXV5dVqVKFValShenq6jKBQMAqVarEGjZsKPHTqFGjb4r19T1BWav4KwrHceL1GQUCAZs4cWKhWzvt27dPIfdgVfl+U/U6vV8qWD+0YNukgIAAhY2ETU5Olnq9qvbo0SPWr18/JhAImLm5OfP29mZJSUkKiydr/dDXr18rLF4B5S6IV0aNGjUKV69exfr164sst2HDBly9epX3uS+DBg1CTEwMXFxc0KdPH+zfvx9paWm8xijKxo0b0aFDB1y4cAFt27bF1KlTMW/ePEydOhVt27bF+fPn0b59e2zatElhdbCwsEDNmjXRsWNHODk5oUqVKqhSpQqcnJzQsWNH1KhRA5aWlhI/fI3cU7bGjRvjwYMHGDFiBLZs2YJWrVohLCxMafFnzZqF7OxsdO3aFQ0bNsSyZcvw5s0bpcTesGEDbG1t0b9/f1SsWBETJkzA5cuXpVrnihQcHIxmzZphwIABSEtLQ0BAAMLCwjBq1CiFrSFqbm6OQ4cOiR+PHTsWd+/eVUis4jx69Aj9+/dHs2bNcPXqVSxatAhv376Fp6enQu7/xsbGwsPDA9WrV8emTZswdOhQvHz5Ejt27FDs7hEFFJ5my4Hc3FzWvXt3JhAIWPfu3dm+ffvY48eP2Zs3b9jjx4/Zvn37xM+7uLgoZGPdlJQUtnPnTubs7My0tLSYoaEhc3V1ZWvXrmUCgUBh345v3rzJBAIB69ixI4uIiJBZJiIignXu3JlpaWmxW7duKaQeysRxHAsKChI/LmgJXrp0SSmxv2yF7t69mxkZGTFDQ0O2fft2ibKKagkWuHTpEvvxxx+Zubk5EwgErFWrVmz27NkKfb8VePPmjXgzV47jmK2tLXNzc2MCgYAdOXJEITGPHDnCmjRpwgQCAatduzbbu3ev0uZA6unpsZ07d4ofq2KE8sOHD1nfvn2ZQCBgFhYWzMfHhyUnJys05rRp05i+vj7T1dVl48aNK/QzRpFo2TQ5ZWZmYtasWdi6davUQATGGLS0tDB+/HisXr1afL9KUWJiYhAUFISgoCA8fPgQANC1a1eMHTsWPXv2hJmZGW+x+vfvj+fPn+PJkydF3vsTiURo1KgR6tev/01zf9SJQCBA06ZNYW9vDwDIzs7GuXPn0KZNG5mLFnMch2PHjvEWe9++fRKr1YSFhcHV1RXPnj2Dq6srtmzZAmNjYwQGBipsYMyXsrKycOLECQQFBeH06dMQiURo2LAhXF1d5drnsrS+XKc3JiYGNjY26N27N+/r9AoEAnAchyZNmmDo0KHF3vPjOA4zZszgJXbTpk1hYGCAJUuWwNTUFC1atMCSJUvQvXv3Is/jYxTzw4cP4eXlhZMnT8LMzAwzZszAtGnTYGJiUuprF6fgd16/fn257rfz+bcmviYlwZKJiorC6dOnERYWhpSUFJiYmKBOnTro0aMHKlWqpPT6hIWFYe/evdi/fz/evn0LbW1ttGvXjrepDTY2NvDw8MDcuXOLLbt06VKsXbsWcXFxvMSWJTs7G2FhYUhOTpY5wbhjx46ljlGlSpUSDxnnq7tQVhIE8r9kTJ8+HVu3bkX16tWxf/9+vHz5UilJ8EvJycn4448/EBQUhGvXrgHI31xXGd2lil6nV5Xrlp49exaurq5yvxbG42LSXyb/qVOnypX8+BoZrMq/NfE1KQmWHzdu3BBPofj48SMv19TT08PmzZsxZsyYYsvu2rULkyZN4m0Fjy/l5eVh7ty52LRpE9LT0wstVx4W75aVBAscOnQIP/30EzIyMtChQwdcunRJZa/5/fv34h6Jx48fKzV2ZmameJ1evloG7969K/E5lStX5iU2kL9izV9//YW4uDiMHj0aP/30U7FTQfgYf/Bl8i9ISEWlBVUt0K8olATldO7cOfj5+SEiIgKWlpYYMmQIpk+frupqyZSTkwNtbX5mv1StWhWDBw/GypUriy07e/ZsHD58WCELMC9ZsgSenp6YMGEC2rdvj5EjR2LFihUwMzPDpk2bwHEcVq5cCWdnZ95jK1NxSRDIn0c3dOhQ3L9/v9x9IJUlL168QN26dRVy7S5dumDBggX47rvvFHL9L129erXE5/A1fzgzMxMeHh6oX78+pk6dWmi5devWISwsDGvXrpW5YEapKP0uZBl05coV8dI+FSpUYNra2kwgELDZs2erpD7Jycls2bJlrFu3bqxJkybs7t27jDHGEhIS2OrVq9k///zDW6wJEyYwMzOzYm9Yv3nzhpmZmbEJEybwFvtL1atXZ66urowx6SkLIpGINWvWjM2dO7fUcdLS0lRybgEvLy/29OnTYstlZ2ezOXPmyD1151tdvHiRrVy5UuLYjh07mIODA7O2tmYeHh4sJyeHt3hpaWns3bt3TCQSST23Y8cO1rVrV1a3bl3Wv39/9tdff/EWlzFWouUO7927x6ysrHiNr4nWrl3LhEJhsVMhwsPDmVAoZJs2beK9DpQE5dCtWzdma2sr/nD69OkT++6775iBgQFLT09Xal3ev3/PqlWrxnR0dFi9evWYQCCQmL9Wq1YtNm3aNN7iRUZGMhMTE2Zra8sCAwOl1u7MyspigYGBzM7OjpmamrJ3797xFvtLQqGQbdmyhTGWv14qx3Hs1KlT4ufXrFnDKlWqVOo4FhYWzNvbu0S7gfz7779s4cKFzNLSstTx1U379u3Z8OHDxY+fPHnCtLW1WbNmzdiQIUOYQCBgy5cv5y3enDlzmKmpKUtNTZU4vnjxYvHakVZWVozjOGZgYMAePXrEW2yO49jYsWOLLXfp0iVmbGzMrK2teYstS3JyMvPy8mItW7Zk1tbWzNramrVs2ZJ5e3srfNSmLDExMez3339nLVq04O2a7dq1k3snlBEjRrAOHTrwFrsAJUE52NnZscWLF0scu3v3LuM4jvdvo8UZOnQoq1ChAvv777/Zx48fpSZx//rrr6xevXq8xrxx44Z4CyMDAwPWtGlT1rFjR9a0aVNmYGAg3s7o+vXrvMb9kp2dHVu9erX4sampKVu/fr348Zo1a5ihoWGp4xw8eJDVrVuXaWlpsY4dOzIfHx928uRJ9vfff7Po6GgWFRXFnj17xk6cOMEWLVrE2rVrx7S0tFidOnXYwYMHSxVb1a1QWSwtLdnvv/8ufjx79mxmYWEhjjdhwgRe32+tWrWSSLqM5ScDoVDIHBwc2Js3bxhj+X9/ZmZmbOjQobzF9vX1ZRzHFbkod3BwMNPX12eOjo4sLCyMt9hfi4qKYtWqVWMcx4lbvv3792f16tUTL5KvjG3bFL1Av4mJidytu02bNjFTU1PeYhegJCgHgUDA9u3bJ3GsIAEpY+7YlywsLMQJWdZKJv7+/szY2Jj3uImJiWz58uWsXbt2zNzcnGlrazNzc3Pm5OTEli5dyhISEniP+aVevXpJfOD169ePVa9end24cYNdu3aNValShTk5OfESKzc3lx09epT17duXCYVCmbsLcBzHhEIh6927Nzt69Cgv88nUsRWqp6cnsVfj13sYbt++nZcvHwWsra3ZihUrJI7t37+fcRzHNmzYIHF8xowZzN7enrfYjDG2bNkyxnEcc3d3l5rvu3v3bqajo8Pq1KnD3r9/z2vcr40YMYIZGBhI9HYUOH36NDM0NGSjRo1SSOzs7Gx27NgxNmTIEPGX3MaNG7OlS5eyZ8+e8RpLT09Pav5rYbZv38709PR4jc8Y7SIhF/b/w5G/JM8oKkXIyMhAhQoVCn0+NTWV95iZmZk4d+4cGGNwd3fHDz/8AFtbW97jFGX8+PEICAiASCSCUCiEr68vOnbsiI4dO4IxBnNzc+zfv5+XWAKBAP369UO/fv0gEokQGhqKsLAwJCQkAMjfXbtOnTpo3rw5r+umbt68GV5eXvDx8UG7du3g7OyMZs2aoWrVqjA3NwdjDImJiYiIiMD9+/dx8eJF3LlzBzVr1lTYaj0ODg7466+/MHbsWISHh+PZs2cSa4d++vSJ199BamoqLC0tJY5du3YNHMfBxcVF4ni9evV4GwVd4LfffoOWlhbmzJmDvLw8BAQEgOM4rF27FjNnzkTTpk1x9uxZmfNF+XT27Fl4eHigZ8+eUs/16NED06ZNw7Zt23iNqYoF+u3s7PDs2TO5yj579gx2dna814GSoJz27NmDO3fuiB9nZmaC4zhs2LABwcHBEmUL/mgUoV69erh27RomTJgg8/ng4GA0bdqUt3gfPnyAk5MTIiIixF8GDAwMcPToUaWOxOzbty/69u0rflyvXj28fv0aV65cgZaWFpycnBSyTJpQKISTkxOcnJx4v/bXhgwZgkGDBuH48ePYvXs3fH19kZWVJfUFjDEGXV1ddOvWDYcPH0afPn0UtpzX8OHD4ePjg6ioKPz9998wNzeX+HcIDQ0Vb3DMh8qVK0stEXflyhXY2NigRo0aEsezsrIUMqF79uzZ0NLSwqxZs8AYQ5UqVeDr64tOnTrh+PHjMDY25j3m19LS0mBjY1Po8xUrVuRt6URVLtDv7OyMPXv2YO7cubC2ti603IcPH7Bnzx4MHjyY/0rw3rYsh+TdUFbRG8syxtjevXvFgxFev37NOI5jFy5cYP/88w8bMWIEEwgE7OjRo7zFmzJlCtPS0mK//PILO3XqFFu7di2zsrJi1apV4y1GcdLT09mMGTPY8ePHlRZTHWRmZrKbN2+yHTt2sJUrV7KVK1eyHTt2sJs3bxa6qDbfsrOz2bx581iTJk1Y586d2bVr18TPJSQkMBsbG7Z06VLe4k2bNo1ZWFiIF2QPCAhgHMexyZMnS5UdO3Ysr4M0vrZ27Vrx33Pfvn1ljlhVlObNm7M2bdrIjJmVlcXatGnDmjdvzkssVS6Y/vr1a2ZgYMDq169f6CL8d+7cYQ0aNGAGBgYsPDyc9zrQPMEyyNfXF15eXmCMIS8vDwKBAIwxCAQCLFmyBHPmzOEtVu3atdGuXTvs3LlTfOzgwYNwc3PD8+fPUbt2bd5iFcXQ0BBr167FuHHjlBKPqEZ8fDyaN2+Of//9F1paWsjJyUGFChXw5MkTiZZReno6KlWqhJ9++gnLly/nJfa0adOkjp04cQIfPnyAu7u71NxbRfb4HDp0CK6urmjUqBEmT54sbm2/fPkS/v7+ePLkCQ4ePIhBgwaVOtaQIUNw8uRJ5OXloVu3bhg2bBj69OkDQ0NDvH79GjVr1sThw4cVtn/kqVOnMGzYMKSlpaFatWpo2LAhjI2NkZqaimfPnuH169cwMDBAUFAQevfuzX8FeE+rRCnevXvH1qxZwyZPnswmTpzIVq1apZBtR74eGMFY/mAMjuPY1atXeY9XmA4dOrDp06crLR5RnU+fPrGVK1eyiRMnsmXLlrG4uDipMnfv3mVTpkzhdaCGOvX4MMbYrl27mI2NjcTALI7jmI2NDdu9ezevsVS1QH+BiIgINnHiRFapUiWJ37G9vT2bMGGCQrdUopZgKaWkpMDDwwO//vor6tSpo+rq8E7WCiYJCQmoUKECLl68iK5duyqlHg8ePEDPnj2xZMkSjB49mrcVcUjxXrx4gV27duHNmzdITEyUGgzGcRxCQkJUVLvyhzGG1NRU6OrqQltbG/fv3xcv6Va5cmW0aNFCoe9/ZS7QL0tqaqp4XWZl3H+lJFhKcXFxsLOzw4ULF5SWEID8kVKnT5/G27dvAeQvb9a9e3feV/Qv6GL9cjX75ORkfPfdd9i8eTNatmwpdQ4fK9t/rVGjRoiPj0dcXByEQiHs7e2lduvgOE7pa1iWd3v37sWYMWOgo6OD2rVrw9zcXGY5vhZsL+pviOM46OnpoXLlyujZsyd++OEHXmKqG5FIBENDQyxduhS//vqrSuvy4sUL7Nu3T2EL9KsDSoKlFBcXB1tbW6W1ikQiESZMmIC9e/eK7wMC+QtMcxyH4cOHY/v27dDV1eUlXsEK819jMqaNMB5Xtv9a586d5Vptno8/zmrVqsHPzw99+vQp9bXKuurVq8PCwgJnzpxR+LQAAGjQoEGR/87p6el4//49cnNz4eLigmPHjvG/lqQMyu7xqVSpEmbPnq1W6xPfvHkT+/bt43WBfnVAfUplzJw5c7Bnzx5MnjwZU6dORfXq1cFxHMLDw7Fu3Tps3rwZFhYW8PPz4yXerl27eLlOaV25ckVpsd6+fcvb9jxlXXR0NGbNmqWUBAhArjljGRkZ2LJlC2bOnImVK1di/vz5Cq9XRkYGAgICMGLECKUkwdGjR2PPnj2YNGkSb19ov0VYWBgOHTqEmJgY1KlTBytWrMD69etVVh9FoCRYSrq6uujUqVOh3UR827dvH0aOHIkNGzZIHK9duzY2btyIlJQU7Nu3j7ckyMdWLUR+6tYKbdSoEaKjo1VdDQn6+vrw8PDAvXv3EBQUpJQkCCh3YYyGDRsiODgY9evXx+jRo1GlShWZm3XzMWJzw4YNWLduHW7duiXxZefEiRMYPHgwsrKyxMfWrVuHO3fuKO1LkTJQEiwlc3NzpfaPZ2dno02bNoU+7+TkhBMnTiitPopW8MFT0EWWnZ0t8/VVrFhRKRPaFU3dWqFr1qzB4MGD0aNHD7X7/bZr105qoYryYtiwYeL/X7hwocwyfN16OH78OKpXry6R2HJycjBu3DhoaWlh165daNGiBU6dOoX58+fD19cXv//+e6njqgtKgt/o64EpVapUQY8ePXgfmPI1FxcXnDt3DpMmTZL5/NmzZ9GtWzeF1kFZIiIiULduXcyZMwfe3t4A8u/NDBo0CBzHSXwzFwqFeP78OapWrcpL7K1bt+LixYtyleU4Djt27OAlrrpZsWIFTE1N0aFDB9SrVw+Ojo7Q0tKSKMNxHG8b25ZEenq60kYJK7vH59KlSyXacb00nj9/jvHjx0scu3z5Mj5+/Ih58+aJe4Pq16+Px48f4/Tp05QENVlRA1Pmzp3L+8CUry1evBhDhgzBgAED8PPPP4uXkvrnn3+wceNGvHv3DgcPHsSnT58kzlPEkmKK5u/vDwsLC5ndXf/73//Eo1Dz8vLg6uoKf39/rFixgpfYd+/eRWhoqFxly3MSfPLkCTiOg6OjIz5//oznz59LlVHWh/WXGGM4fvy4wr90FlB2j0/nzp2VFishIQEODg4Sx0JCQsBxHPr37y9xvF27djhy5IjS6qYMlARLSNkDU75WsJP106dPpb59F7SM6tWrJ3VeWdx9/Pz58xg4cKDMLxSNGzeW2N16+PDhOH/+PG9JcMeOHUXu7q5I6tQKLejpUJavv7x9LSMjAy9fvsTmzZtx69Yt7Nu3T6H1UXaPz7lz5+Dn54eIiAhYWlpiyJAhCh8hamNjg9jYWIlj169fh4GBARo3bixxXFdXV6UDdRSBkmAJKXtgytc8PT1V8s1bFcLDwzFx4kSJYwKBAKamplLD4mvVqlVuWmOa3Aq1srKS6/2to6ODxYsXS9w745MqenyuXr2Knj17gjEGKysrvH79Gnfu3EFUVBRWrlzJW5yvtWjRAgEBAZg6dSqMjY3x999/4969e+jbt69Ud3NYWBgqVaqksLqoAiXBElL1wBQvLy+FXVvd5OTkSP0RmpubIzExUaqsrq4usrOzlVU1hVJlK7QoqampSE5ORl5entRzjo6OvMQo7ktewWT57777rsgtxUpLFT0+S5cuhY2NDc6fP48GDRogMTERgwcPxsaNG+Ht7S1zdCgfFi1ahJYtW6JmzZqoX78+QkNDwXEc5s6dK1X26NGjSl0URCkUtiBbOTV48GDWt2/fQp/v06cPGzx4sPIqVI5VrVqVzZo1S66ys2bNYlWqVOElLsdxLDAwkJdrlaXYhdm0aROrUaOG1MbCX/6UN5aWlkVuWjtixAjeNzK2s7MTb5hd4O7du4zjOPbXX3/xGutrN2/eZD179mR16tRhLi4u7MKFC1JlLl++zBo0aMDOnTun0LooG7UEi/H1PQp5B6Yo2s2bN/HgwQOZ38w5jit0WHVZ0qlTJwQGBsLb2xsGBgaFlktLS0NgYKDUpqvfateuXWjbti0v1yrr/P398fPPP8PFxQVjx47F/PnzMWPGDOjp6WH37t2wsbGRuftCWaeKHp/Y2Fip0c3VqlUDoJjNsr/k5OSEU6dOFVmmc+fOePr0qULroRKqzsLq7ssV3L9cyb2o41paWgqrT0JCAmvTpo043tf1Ucbq9sry5MkTpq2tzbp06cKio6NllomOjmZdu3ZlOjo67PHjx0quIf/UrSVYr1491r17d8YYY/Hx8YzjOBYSEsIYYywpKYnVqFGD/e9//1NlFRVCFT0+sv7tv/6dE/5RS7AY6jYQZfbs2Xjy5AmCgoLQunVrVKtWDefOnUPVqlXx+++/4/bt2zhz5oyqq8mLhg0bYtOmTZg8eTKqVKmCzp07o0GDBjAyMsLnz5/x7NkzXL16FTk5OdiwYQMaNWrES1xjY+MS/ZtzHIfk5GReYqtbK/T169f4+eefAUA8GKlgBRFTU1OMGzcOmzZtwi+//KKyOvJBXXp89uzZgzt37ogfZ2ZmguM4bNiwQWphAEXuZ6hJaAHtMsbW1hbDhg3DmjVrxFsaXbhwAd999x2A/GWUhEIh9u/fr+Ka8ufWrVvw9vbG5cuXkZOTIz6ura2Nzp07w9PTE+3bt+ct3ujRo+VKgo8ePcLjx48Vtmi4OrCxscH8+fPFXZ5GRkb4/fffxZOrN23ahNmzZyMtLU2V1Sw1WQvFs69WK/r6uEAgkHg/8lGHkijP7ztlopZgGZOUlIT69esDyP9AAiCxzFa3bt0wb948ldRNUZycnHDu3DlkZGQgPDwcKSkpMDY2Ro0aNYq8V/itdu/eXeTzjx49gre3Nx4/fgxTU1N4eHjwFluVrVBZGjRoILE9VZs2bbB582b07NkTeXl52LJli3jX87JMHXp8ZI26JYpHSbCEfHx8ii2jyIEpdnZ24omtQqEQ1tbWePz4Mfr27QsAiIqKUvkfs6Lo6+srbYUQWR4/fgwvLy8cP34cpqam8PLywvTp02FqaspbjIEDB5a4FapII0aMgL+/P0QiEYRCIby9veHs7CyeEqGjo4M///xToXVQBk2aekS+otpbkmVPwSAUWT/KGJgyevRo1qlTJ/HjadOmMUNDQ7ZkyRLm4+PDjIyM2MCBAxUWX1U+f/7M8vLyCn3+w4cP7OrVqwqJ/fDhQ9avXz8mEAiYubk58/b2ZsnJyQqJJW9dOI5jZmZmzMvLS+l1eP36NfPz82Pr169nL1++VHp8TXDmzJliy6xatUoJNSn/6J4gD/Ly8vDu3Tts3LgR165dw5kzZ2BpaamQWE+fPsWFCxfw888/QygUiifUXrp0CQDQsWNH7N+/H7a2tgqJr2wBAQHw9PTEv//+CwMDAwwaNAjLli1DxYoVJcoFBgZi1KhRvN4jefToEby8vHDixAmYmZnBw8MD06dPh4mJCW8x5PV1K7SgLny2Qsl/VN3jIxAIMH78eKxevVp826NAeHg43N3dcefOHbonyAdVZ+Hyxs3NjQ0bNkzpcRMTE1lKSorS4yrSxYsXGcdxrG7duuyXX35hbm5uTF9fn1laWrIrV65IlN23bx9vLfAHDx6wPn36MIFAwCwsLNjixYtV3vJTdSv09u3bbOnSpczDw4O9evWKMcZYWloaCw0NZampqUqvj6KpusfH19eXCYVCVrVqVXb58mXx8d9//50ZGhqyihUrsuPHjyssviahliDPtmzZgjlz5iApKUnVVSnzunTpgs+fP+PmzZviNRrDw8MxePBghIWFISAgAEOGDAHAb0uwYKRgkyZNMHXqVLlafnxsbvoldWmFZmVlYejQoTh27BgYY+A4DhcuXEDXrl2RmZmJSpUqYcaMGUrb2FaVlNnjA+T3+ri7u+PJkycYP348nj9/juvXr2Po0KHYsGFDmdwZRh1REuTZ+PHj8eeffxa7Gn5p5Obm4ty5c3jz5g0SExOldrwuLyvGVKhQAZ6enpg6darE8YyMDLi6uuL06dPw8/PDlClTeE+CBQoGnhT1Z8LnUPWHDx/Cy8sLJ0+ehJmZGWbMmIFp06appAsWyF9D8/fff8eGDRvQpUsX1K5dGxcvXhSvHzlp0iSEhobi3r17KqmfqgwfPhyMMQQFBSk0TnZ2Njp06IB79+6B4zgsW7YMv/76q0JjahoaHVpCe/bskXk8KSkJ165dw5EjRzBu3DiFxb9//z4GDhyIf//9t9AP5vKSBHNzc2Vumqqvr4/g4GCMGzcO06dPx4cPH1C7dm3e4ipz37ivNW/eXKoVWty2Sny3Qr+0f/9+TJo0CT/99BMSEhKknq9bty4OHTqksPjqqmPHjpgzZ45CY0RFReHHH3/EvXv30KtXL9y/fx+LFy+GkZERJk+erNDYGkVlHbFlVFH3CipUqMDmzp3LMjIyFBa/ZcuWrEKFCuz48eMsMTFRYXHUQatWrdjw4cOLLDNr1izGcRyrXbt2uVgu7ut7T18ui1fY/SlFEgqFbNu2bYwx2Ut4bdiwgenr6yu0Dupo3LhxzNzcXGHX37VrFzM3N2eWlpbswIEDjLH8JRNdXV0Zx3HM2dmZvX//XmHxNQm1BEsoIiJC6hjHcTA3N4exsbHC4z958gS+vr7o3bu3wmOpWs+ePbFq1Sp8+vSp0Psfq1atgrW1NebMmaOS+ZEZGRn4+PEjb1sJqbIVKouDgwPCwsIKff7mzZviJcXKE1X3+IwdOxY//PADtm3bBhsbGwCAhYUFDhw4gEGDBmHSpElo2LChzG3FSAmpOguXdS9evGA+Pj5s0qRJbO3atQofuVe9enWNmR/0/v17tmHDBvb8+fNiyx45coS3OXP6+vrib9+MMZaSksJ69Oghc4FuPkelqiNPT09mZGTEbt26JW4JXrp0iTHG2NatW5mWlla5fD+qusdn165dRT4fFxdXLucDqwIlQTmsX7+e1axZk338+FHi+PHjx5lQKJT4A6levbpUOT5t3bqV1apVS2VD9jXB16v5F7WSvyqTYHp6Onv37p1CY4hEItatWzemra3NGjZsyAQCAWvcuDFzcHBgHMexXr16sZycHIXWQRXevn0r9fPu3btyNw2JUHeoXI4fP47q1avDyspKfCwnJwfjxo2DlpYWdu3ahRYtWuDUqVOYP38+fH198fvvv/MSe82aNVLHjIyMUKNGDQwdOhQODg7Q0tKSeJ7jOMyYMYOX+OqMMYbLly9DJBKhffv2SumOVjQDAwPs2rULrq6uAPL3kXN1dcXy5culdsk4cuQI7wsEfE1XVxdnz55FYGAgDh8+jNzcXIhEIjRq1AhLlizByJEjy+UyfZUrV5Z4HBYWhkOHDiEmJgZ16tTB6NGjVTZil/BM1Vm4LLC3t5fqajt//jzjOI7Nnz9f4ribmxurVasWb7GL6pZR1WAJVZg3bx7r3Lmz+HFeXh5zdnYWDxypXLkyCw8P5yWWKluCZaUVWh6pssenYB9SkUgkfvz1fqVf/yhy31JNQi1BOSQkJMDBwUHiWEhICDiOQ//+/SWOt2vXDkeOHOEttqyBOJrozz//FC8SDgCHDx9GSEgIfH190bhxY0yYMAFeXl7Yu3evCmtJyjJV9vgU7GJRMCVIHXa10BSUBOVgY2Mj3rmhwPXr12FgYIDGjRtLHNfV1RWvbsKHr7tlNFVUVJTEKMQjR46gXr16mDt3LoD8SdubN2/mLZ6sDyBN+VAq2DtQXuVlc9fnz5+L90kscPnyZXz8+BHz5s2Du7s7AKB+/fp4/PgxTp8+zVsS/HoXC9rVQnkoCcqhRYsWCAgIwNSpU2FsbIy///4b9+7dQ9++faUmc4eFhaFSpUpKq1t8fDzOnDmDmJgY1K5dG7179y7x5pxlgba2NkQiEYD8e4EhISEYNWqU+HkbGxvEx8fzFu/HH3/EhAkTJI798MMPUvdf+dxUVV1s2LABHMcVuUrOl8pLElRlj8+X0tPT0aFDB4wfPx4TJ05USAzyH0qCcli0aBFatmyJmjVron79+ggNDQXHceJWyJeOHj0qXlKKL/v378f27dtx8OBBia6a27dvo3fv3uKl0ziOQ6tWrXDx4kUYGhryWgdVa9CgAfbt24fhw4fj6NGjSEhIQK9evcTPv3v3TuJ3UxoF3/hVRdWtUKFQCI7j8MMPP8DNzQ29evWCjo6O0uKriip7fL5kYGCAiIgIjel5UDkV35MsM27evMl69uzJ6tSpw1xcXNiFCxekyly+fJk1aNCAnTt3jtfYvXv3Zh06dJA4lpeXx6pXr860tbWZl5cXO3nyJJs0aRLjOI55enryGl8dnD9/ngmFQvGggK9/H82aNWP9+/dXUe34w3Ec09fXZ8bGxuIfjuOYgYGBxDFjY2Omr6+vkIExKSkpbPfu3eKpEebm5mzcuHHi+YHl1cCBA1mtWrXE0yCePXvGtLW1Zc7H++WXX1i9evUUVpdhw4aVi/dzWUALaJcBlStXxsiRI7FkyRLxsZs3b6JDhw6YMmUK1q1bJz7ep08fRERE4OnTp6qoqkI9f/4cFy5cgJmZGVxdXaGnpwcASExMhI+PD/r27YvOnTurtpKlNGbMmBKfs2vXLgXUJF9cXBwOHDiAoKAg/PXXX7C1tYWrqyvc3NzQokULhcVVhadPn6Jly5YwMzMT9/ikp6fj9u3baN68uUTZ6tWro2vXrti2bZtC6vLixQsMHjwYTZs2xYQJE1C1alXo6+tLlaOdJHig6ixMiqenp8e2b98ucczT05MJBAJ248YNiePr1q1jBgYGyqxeuZOQkFDiH00QHh7OvL29xeu0rlixQtVV4p0qe3y+JGsNWVk/pPTonmAZYGlpKbVG4I0bN6CjoyP1DdXQ0LBc30u4c+cOLl++jA8fPmDy5MmoWbMm0tPTERYWhlq1akntwv0tKlSoUOJzNGGH76SkJCQnJyM1NRWMMZktk7LOyckJp06dKrJM586dFd7TQlMklIeSYBnQqFEjHDhwAB4eHtDW1kZUVBRu3ryJLl26iLsEC7x+/Rp2dnYqqqniyNrctXfv3qhZsyYEAgG6devG2+auBR/wvXr1Qv369Xmovfy+ZR9KRXaJvXr1CkFBQdi/fz/++ecf1KlTB5MmTYKbmxuqVaumsLiajqZIKJGKW6JEDtevX2cCgYA1bNiQjR07llWuXJkJBAJ2/vx5qbINGzZkbm5uKqilYv36669MR0eHbdmyhb169UpqFZWJEyeyli1b8hJr3rx5rFq1aozjONa4cWO2YsUKFhkZycu1i1PcKiHK6BL7999/2apVq1izZs0Yx3HMwcGBzZ49mz18+JD3WES2MWPGsDt37hT6/N27d9mYMWOUWKPyiwbGlBGHDh2Ct7c3Xr9+jcqVK2POnDlSgyguXbqEvn37YsuWLXBzc1NRTRXD0dER/fv3x9q1a5GQkIAKFSpI7HC+bt06+Pj48DpX8NatWwgKCsKhQ4eQkJAAJycnDB8+HIMHD1ZY60sgEJS4Fbpo0SJe66ClpQU9PT307NkTbm5u6NixY7FdczRAg18CgQD79u0r9O/44MGDcHNz04hueIVTdRYmxSvJDfjMzEzWp08fBdZGNVS5uWtOTg47deoUGz58ODMyMmK6urrshx9+KPKb+rdSZSu0gDwDMmiAhmJ9vYbs19asWcMMDQ2VWKPyi+4JlgH9+vVDcHAwunXrVmS5z58/o0+fPrh69aqSaqY8qtzcVUtLCz179kTPnj3x/v17uLu74/Tp02jZsiVat27NayxfX1/4+vqKW6GrV6/GvHnzlNIKLVDSlmV5XDVHFY4dO4Zjx46JH2/duhUXL16UKpeUlISLFy+iZcuWyqxe+aXqLEyK17JlS6avr8/OnDlTaJn4+HjWokULxnEcW716tRJrpxyq3Nw1LS2NBQYGsh9++IEJhUJmbGzMRo4cKXOTXb4psxVagHoeVGPp0qXMyMiIGRkZMYFAwPT09MSPC36MjY1ZxYoVWa9evdjLly9VXeVygZJgGZCcnMxatWrF9PT02OnTp6We//fff1ndunWZtrZ2sTtSl1XK3tw1OzubHT9+nA0dOpQZGhoyoVDI+vbtyw4ePKjQHcWLEhkZybp06cIEAgHz9vZWWBx9fX25EmFqaqq4PoRfxXWHEv5QEiwjUlJSWJs2bZienh47efKk+PirV69Y5cqVmZ6eHjt69KjqKqgEeXl5bO/evaxv376sXr16rE6dOqxXr14sICCA5eXl8RZn/PjxzMLCgmlra7OuXbuybdu2scTERN6uXxKqaIVSzwPRJDQ6tAz5/PkzXFxcEBoaisOHD6NSpUpwcXFBRkYGgoODeV+4W1MVjNDs3r077O3tiy3P9y4KOTk5OHPmDIKCgnDixAnk5OSge/fucHNzQ58+faTmhvItJSUF33//PZ48eYIjR46gR48eEs9HRUXh+++/xz///INt27Zh9OjRCq0PIQql6ixMSubz58+sffv2TCgUMlNTU2ZlZcX++usvVVerXPlyySp5fvjsDlSXVij1PKje6dOnmbOzM7OwsGBaWlo0KldBqCVYBjx48EDi8efPn+Hu7o4PHz5gy5YtqFevntQ5zZo1U1b1FKJLly4QCAQ4d+4ctLW15WrlchyHkJAQJdROcVTdCv0S9Tyozp9//okhQ4agfv366NChAzZv3gw3NzcwxnDs2DHUrFkT/fr1432OqCaiJFgGCAQCqcnKBf9sso5zHFfmJ9F27twZHMfhwoUL0NbWFj8uzuXLl5VQO8Up6YbIiv63TktLQ/fu3fHXX39BT08POjo6OHPmTLnbQULdtGjRAjo6Orhx4wYSExNhbW0tXhzi7du3aNOmDVauXCmxsTT5NpQEy4CAgIASn6PqjWHLC5FIhJs3b+LFixdISUmBsbEx6tWrh3bt2kEoFKq6egqhiT0P6sbAwADLli3D9OnTkZSUBAsLC5w5cwYuLi4AAB8fHxw8eBB///23imta9tFk+TJA0xNaRkYG5s+fjy5duqB3795KickYw//+9z+sWLECiYmJ+PK7IsdxMDc3x5w5czBr1qxyt9p/ixYtCu15+Pq9WF56HtSNgYGBeOd6MzMzCIVCxMTEiJ+3sbFBRESEqqpXrlASJGpPX1+/0BaIogwfPhwHDhxAzZo1MXXqVDRu3BjGxsZITU3F48ePERQUhN9++w2PHj1CYGCgQuqgqlaoIjfpJfKpXbs2nj9/Ln7cpEkT7N27FyNGjEBOTg6CgoLg6OiowhqWIyoZjkNICXXo0IFNnz5dKbH27NnDOI5js2fPLnQCfm5uLpszZw4TCARs7969vMbPy8tjK1euZJaWlkwgEEiNRLW0tGQrV67kdW4kUS+rVq1iDg4OLDMzkzHG2IkTJ5hAIGCGhobMyMiIcRxXbhfGUDa6J0jKhAcPHqBnz55YsmQJRo8eDW1txXVidOzYEVpaWnINsunatStycnJw7do13uK7ubmJW6Fubm4yW6Hh4eEYOnSowlqhRP1cv34df/75J7S1tdGrVy906dJF1VUqFygJkjKhUaNGiI+PR1xcHIRCIezt7aV2Nuc4Do8fPy51LDMzMyxZsgRTpkwptuyGDRuwYMECJCUllTouAOzduxfu7u6YNWsWli1bBi0tLakyeXl5mDdvHlatWoWAgACMGDGCl9iEaCK6J0jKBAsLC1haWqJ27doKj5WdnS33qixCoZDXXRS2bduGTp06YeXKlYWWEQgEWL58Oe7du4etW7dSEiwn+vTpU6LyHMdJ7DpBvg0lQVImXLlyRWmxatSogWvXrmHcuHHFlr1+/TqqVavGW+wnT55gyZIlcpUdMGAAFixYwFtsolonT56Enp4eKlasCHk66MrbqGRVoSRIyFcGDRoEHx8fuLq6olevXoWWO3XqFPbv3w9PT0/eYquyFUpUy97eHlFRUbCysoKbmxuGDh2KihUrqrpa5V7JlqcgRIVSUlKwfPlyuLi4oGnTprh37x4A4NOnT1izZg3Cw8N5ifPLL7+gdu3a6NevH3766Sdcv34dKSkpYIwhJSUFN27cwE8//YR+/fqhdu3a+OWXX3iJC/zXCpUH361Qolrv37/H5cuX0bRpUyxevBgODg5wdnbGrl27kJqaqurqlV+qHJpKiLzev3/PqlWrxnR0dFi9evWYQCBgISEh4udr1arFpk2bxlu8uLg45uLiIp6W8PUPx3Hs+++/ZzExMbzFZIwxHx8fpq2tLbFotSwnT55k2trazMfHh9f4RD1kZWWx4OBgNmTIEGZgYMD09PRY//792aFDh8TTJgg/aHQoKROGDRuGkJAQXLlyBdbW1hJrKQLAnDlzcPLkSd6Xkbp79y5OnDiB58+fIzU1FcbGxqhbty5++OEHtG3bltdYAJCeno5WrVrh5cuXGDNmDEaOHCkxReLJkyfYs2cPdu3ahdq1a+PevXswMDDgvR5EfXz+/BlHjhyBv78/7t69Cy8vLyxcuFDV1So36J4gKRPOnz+PGTNmoF69ekhISJB6vlq1anj//j3vcVu3bo3WrVvzft3CGBgY4NKlSxg1ahS2b9+OHTt2SJVhjMHZ2Rl79uyhBFjOiUQinDt3DseOHcPDhw+hp6eHKlWqqLpa5QolQVImZGRkoEKFCoU+X57umVhbW+Ps2bNKb4US9ZCXl4cLFy5g//79CA4ORnp6OpydnbFt2zb0798fhoaGqq5iuUJJkJQJ9erVw7Vr1zBhwgSZzwcHB6Np06a8xFKX+VrKboUS1bp16xaCgoJw6NAhJCQkoE2bNli6dCmGDBkCKysrVVev3KIkSMoEDw8PuLu7o1GjRhg8eDCA/G/M4eHh8Pb2xu3bt/Hnn3/yEovmaxFVaN++PfT19dGzZ08MGzZM3O0ZGRmJyMhImefQFlalRwNjSJnh6+sLLy8vMMaQl5cHgUAAxhgEAgGWLFmCOXPm8BLHwcEBUVFRaNGihdLna6lLK5Qo35cbKhf3xYrRFla8oSRIypTIyEj8+eefCA8PR15eHqpXr44BAwbwPl/u6tWrCAoKwuHDh5GSkoJOnTph+PDhGDRoEIyNjXmN9SWBQFDiVuibN28UVh+iPLR5tmpQEiRlUlhYGA4dOoSYmBjUqVMHo0ePhomJCe9xsrOzcfr0aQQFBeHkyZPIy8tDjx494Obmht69e/O+r58qW6GEaCRVTE4kRB7r169nNWvWZB8/fpQ4fuLECSYUCiX22qtevbpUOb6lpqaygIAA1rZtWyYQCBQ2Uf3KlSvsp59+YhYWFkxbW5t99913bOfOnSwlJUUh8QjRZLRsGlFbx48fR/Xq1SVGxuXk5ODHH3+ElpYWdu7ciadPn2L58uV49+4dfH19FVYXZc7X6tSpE7Zs2YLY2FgcPnwYlpaWmDJlCqytrTFgwAAcPnwYIpFIIbEJ0TiqzsKEFMbe3p55eXlJHDt//jzjOI7Nnz9f4ribmxurVasWr/Fzc3PZ2bNnmbu7OzM1NWU6OjqsR48ebO/evezz58+8xiqOslqhhGgamiJB1FZCQgIcHBwkjoWEhIDjOPTv31/ieLt27XDkyBFe4qrbfC1aNYQQxaEkSNSWjY0NYmNjJY5dv34dBgYGaNy4scRxXV1d6Orq8hJXHeZr0aohhCgHjQ4lamvQoEF4+vQp7t+/D2NjY/z9999o0qQJ+vbti8OHD0uUnTVrFs6cOcPLAtqqnK8lqxXq5uZGq4YQoiCUBInaevr0KVq2bAkzMzPUr18foaGhSE9Px+3bt9G8eXOJstWrV0fXrl2xbdu2UsdV5XwtgUAgsxVaFFo1hJBvR0mQqLVbt27B19cXb968QeXKlTFr1iw4OztLlLly5QqmTp2K1atXo1u3biqqKT9o1RBClIuSICFqhFYNIUS5KAkSQgjRWDRZnhBCiMaiJEgIIURjURIkhBCisSgJEkII0ViUBAkhhGgsSoKEEEI0FiVBQgghGouSICGEEI1FSZAQQojG+j+Fw51rFfg99wAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 460x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "rank_list = sorted_mean_df.index[:12]\n", "\n", "df_acc = df_VUS_PR\n", "plt.figure(figsize=(4.6, 3.5))\n", "sns.reset_orig()\n", "ax = sns.boxplot(data=df_acc[rank_list], showfliers=False, \n", "                 meanprops=dict(color='k', linestyle='--'), showmeans=True, meanline=True)\n", "plt.xticks(ticks=range(len(rank_list)), labels=rank_list, rotation=90, fontsize=12)\n", "plt.ylabel('VUS-PR', fontsize=12)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### TSB-AD-M"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>VUS_PR_Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CNN</th>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OmniAnomaly</th>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PCA</th>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LSTMAD</th>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USAD</th>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AutoEncoder</th>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KMeansAD</th>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CBLOF</th>\n", "      <td>8.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MCD</th>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OCSVM</th>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Donut</th>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RobustPCA</th>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FITS</th>\n", "      <td>13.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OFA</th>\n", "      <td>14.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EIF</th>\n", "      <td>15.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>COPOD</th>\n", "      <td>16.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IForest</th>\n", "      <td>17.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HBOS</th>\n", "      <td>18.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TimesNet</th>\n", "      <td>19.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KNN</th>\n", "      <td>20.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TranAD</th>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LOF</th>\n", "      <td>22.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AnomalyTransformer</th>\n", "      <td>23.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    VUS_PR_Rank\n", "CNN                         1.0\n", "OmniAnomaly                 2.0\n", "PCA                         3.0\n", "LSTMAD                      4.0\n", "USAD                        5.0\n", "AutoEncoder                 6.0\n", "KMeansAD                    7.0\n", "CBLOF                       8.0\n", "MCD                         9.0\n", "OCSVM                      10.0\n", "Donut                      11.0\n", "RobustPCA                  12.0\n", "FITS                       13.0\n", "OFA                        14.0\n", "EIF                        15.0\n", "COPOD                      16.0\n", "IForest                    17.0\n", "HBOS                       18.0\n", "TimesNet                   19.0\n", "KNN                        20.0\n", "TranAD                     21.0\n", "LOF                        22.0\n", "AnomalyTransformer         23.0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "df_VUS_PR = pd.read_csv('benchmark_eval_results/multi_mergedTable_VUS-PR.csv')\n", "Comparaed_Solution_Pool = ['IForest', 'LOF', 'PCA', 'HBOS', 'OCSVM', 'MCD', 'KNN', 'KMeansAD', 'COPOD', 'CBLOF', 'EIF', 'RobustPCA', 'AutoEncoder', \n", "                    'CNN', 'LSTMAD', 'TranAD', 'AnomalyTransformer', 'OmniAnomaly', 'USAD', 'Donut', 'TimesNet', 'FITS', 'OFA']\n", "\n", "mean_df = pd.DataFrame()\n", "mean_df['VUS_PR_Rank'] = df_VUS_PR[Comparaed_Solution_Pool].mean().rank(ascending=False)\n", "sorted_mean_df = mean_df.sort_values(by='VUS_PR_Rank', ascending=True)\n", "sorted_mean_df"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 460x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "rank_list = sorted_mean_df.index[:12]\n", "\n", "df_acc = df_VUS_PR\n", "plt.figure(figsize=(4.6, 3.5))\n", "sns.reset_orig()\n", "ax = sns.boxplot(data=df_acc[rank_list], showfliers=False, \n", "                 meanprops=dict(color='k', linestyle='--'), showmeans=True, meanline=True)\n", "plt.xticks(ticks=range(len(rank_list)), labels=rank_list, rotation=90, fontsize=12)\n", "plt.ylabel('VUS-PR', fontsize=12)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CD Diagram"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "import os\n", "import operator\n", "import math\n", "from scipy.stats import friedmanchisquare\n", "from scikit_posthocs import posthoc_nemenyi_friedman\n", "import networkx\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "from matplotlib.backends.backend_agg import FigureCanvasAgg\n", "import seaborn as sns\n", "\n", "def <PERSON>(alpha=0.05, df_perf=None):\n", "    df_counts = pd.DataFrame({'count': df_perf.groupby(\n", "        ['classifier_name']).size()}).reset_index()\n", "    # Record the maximum number of datasets\n", "    max_nb_datasets = df_counts['count'].max()\n", "    # Create a list of classifiers\n", "    classifiers = list(df_counts.loc[df_counts['count'] == max_nb_datasets]\n", "                       ['classifier_name'])\n", "\n", "    # print('classifiers: ', classifiers)\n", "\n", "    '''\n", "    Expected input format for friedmanchisquare is:\n", "                Dataset1        Dataset2        Dataset3        Dataset4        Dataset5\n", "    classifer1\n", "    classifer2\n", "    classifer3 \n", "    '''\n", "\n", "    # Compute friedman p-value\n", "    friedman_p_value = friedmanchisquare(*(\n", "        np.array(df_perf.loc[df_perf['classifier_name'] == c]['accuracy'])\n", "        for c in classifiers))[1]\n", "\n", "    # Decide whether to reject the null hypothesis\n", "    # If p-value >= alpha: we cannot reject the null hypothesis. No statistical difference.\n", "    if friedman_p_value >= alpha:\n", "        print('No statistical difference...')\n", "        return None,None,None\n", "    # Friedman test OK\n", "    # Prepare input for Nemenyi test\n", "    data = []\n", "    for c in classifiers:\n", "        data.append(df_perf.loc[df_perf['classifier_name'] == c]['accuracy'])\n", "    data = np.array(data, dtype=np.float64)\n", "    # Conduct the Nemenyi post-hoc test\n", "    # print(classifiers)\n", "    # Order is classifiers' order\n", "    nemenyi = posthoc_nemenyi_friedman(data.T)\n", "\n", "    # print(nemenyi)\n", "    \n", "    # Original code: p_values.append((classifier_1, classifier_2, p_value, False)), True: represents there exists statistical difference\n", "    p_values = []\n", "\n", "    # Comparing p-values with the alpha value\n", "    for nemenyi_indx in nemenyi.index:\n", "        for nemenyi_columns in nemenyi.columns:\n", "            if nemenyi_indx < nemenyi_columns:\n", "                if nemenyi.loc[nemenyi_indx, nemenyi_columns] < alpha:\n", "                    p_values.append((classifiers[nemenyi_indx], classifiers[nemenyi_columns], nemenyi.loc[nemenyi_indx, nemenyi_columns], True))\n", "                else:\n", "                    p_values.append((classifiers[nemenyi_indx], classifiers[nemenyi_columns], nemenyi.loc[nemenyi_indx, nemenyi_columns], False))\n", "            else: continue\n", "\n", "    # Nemenyi test OK\n", "\n", "    m = len(classifiers)\n", "\n", "    # Sort by classifier name then by dataset name\n", "    sorted_df_perf = df_perf.loc[df_perf['classifier_name'].isin(classifiers)]. \\\n", "        sort_values(['classifier_name', 'dataset_name'])\n", "\n", "    rank_data = np.array(sorted_df_perf['accuracy']).reshape(m, max_nb_datasets)\n", "\n", "    df_ranks = pd.DataFrame(data=rank_data, index=np.sort(classifiers), columns=np.unique(sorted_df_perf['dataset_name']))\n", "\n", "    dfff = df_ranks.rank(ascending=False)\n", "    # compute average rank\n", "    average_ranks = df_ranks.rank(ascending=False).mean(axis=1).sort_values(ascending=False)\n", "    \n", "    return p_values, average_ranks, max_nb_datasets\n", "\n", "def graph_ranks(avranks, names, p_values, cd=None, cdmethod=None, lowv=None, highv=None,\n", "                width=200, textspace=1, reverse=False, filename=None, **kwargs):\n", "    \n", "    width = width\n", "    textspace = float(textspace)\n", "    '''l is an array of array \n", "        [[......]\n", "         [......]\n", "         [......]]; \n", "    n is an integer'''\n", "    # n th column\n", "    def nth(l, n):\n", "        n = lloc(l, n)\n", "        # Return n th column\n", "        return [a[n] for a in l]\n", "    \n", "    '''l is an array of array \n", "        [[......]\n", "         [......]\n", "         [......]]; \n", "    n is an integer'''\n", "    # return an integer, count from front or from back.\n", "    def lloc(l, n):\n", "        if n < 0:\n", "            return len(l[0]) + n\n", "        else:\n", "            return n\n", "    # lr is an array of integers\n", "    # Maximum range start from all zeros. Returns an iterable element of tuple.\n", "    def mxrange(lr):\n", "        # If nothing in the array\n", "        if not len(lr):\n", "            yield ()\n", "        else:\n", "            index = lr[0]\n", "            # Check whether index is an integer.\n", "            if isinstance(index, int):\n", "                index = [index]\n", "            # *index: index must be an iterable []\n", "            for a in range(*index):\n", "                for b in mxrange(lr[1:]):\n", "                    # Form a tuple, and generate an iterable value\n", "                    yield tuple([a] + list(b))\n", "\n", "    def print_figure(fig, *args, **kwargs):\n", "        canvas = FigureCanvasAgg(fig)\n", "        canvas.print_figure(*args, **kwargs)\n", "\n", "    sums = avranks\n", "\n", "    nnames = names\n", "    ssums = sums\n", "    # lowv: low value\n", "    if lowv is None:\n", "        '''int(math.floor(min(ssums))): select the minimum value in ssums and take floor.\n", "           Then compare with 1 to see which one is the minimum.'''\n", "        lowv = min(1, int(math.floor(min(ssums))))\n", "    # highv: high value\n", "    if highv is None:\n", "        highv = max(len(avranks), int(math.ceil(max(ssums))))\n", "\n", "    cline = 0.4\n", "    # how many algorithms\n", "    k = len(sums)\n", "\n", "    lines = None\n", "\n", "    linesblank = 0\n", "    scalewidth = width - 2 * textspace\n", "    \n", "    # Position of rank\n", "    def rankpos(rank):\n", "        if not reverse:\n", "            a = rank - lowv\n", "        else:\n", "            a = highv - rank\n", "        # Set up the format\n", "        return textspace + scalewidth / (highv - lowv) * a\n", "\n", "    distanceh = 0.25\n", "\n", "    cline += distanceh\n", "\n", "    # set up the formats\n", "    minnotsignificant = max(2 * 0.2, linesblank)\n", "    height = cline + ((k + 1) / 2) * 0.2 + minnotsignificant + 2\n", "\n", "    # matplotlib figure format setup\n", "    fig = plt.figure(figsize=(width, height))\n", "    fig.set_facecolor('white')\n", "    ax = fig.add_axes([0, 0, 1, 1])\n", "    ax.set_axis_off()\n", "\n", "    hf = 1. / height\n", "    wf = 1. / width\n", "\n", "    def hfl(l):\n", "        return [a * hf for a in l]\n", "\n", "    def wfl(l):\n", "        return [a * wf for a in l]\n", "\n", "    \n", "    ax.plot([0, 1], [0, 1], c=\"w\")\n", "    ax.set_xlim(0, 1)\n", "    ax.set_ylim(1, 0)\n", "\n", "    # Line plots\n", "    def line(l, color='k', **kwargs):\n", "        ax.plot(wfl(nth(l, 0)), hfl(nth(l, 1)), color=color, **kwargs)\n", "\n", "    # Add text to the plot\n", "    def text(x, y, s, *args, **kwargs):\n", "        ax.text(wf * x, hf * y, s, *args, **kwargs)\n", "\n", "    line([(textspace, cline), (width - textspace, cline)], linewidth=0.7)\n", "\n", "    bigtick = 0.1\n", "    smalltick = 0.05\n", "    linewidth = 2.0\n", "    linewidth_sign = 4.0\n", "\n", "    tick = None\n", "\n", "    # [lowv, highv], step size is 0.5\n", "    for a in list(np.arange(lowv, highv, 0.5)) + [highv]:\n", "        tick = smalltick\n", "        # If a is an integer\n", "        if a == int(a):\n", "            tick = bigtick\n", "        # Plot a line\n", "        line([(rankpos(a), cline - tick / 2),\n", "              (rankpos(a), cline)],\n", "             linewidth=0.7)\n", "\n", "    # Add text to the plot, only for integer value\n", "    for a in range(lowv, highv + 1):\n", "        text(rankpos(a), cline - tick / 2 - 0.05, str(a),\n", "             ha=\"center\", va=\"bottom\", size=16)\n", "\n", "    k = len(ssums)\n", "\n", "    def filter_names(name):\n", "        return name\n", "\n", "    space_between_names = 0.24\n", "\n", "    # Format for the first half of algorithms\n", "    for i in range(math.ceil(k / 2)):\n", "        chei = cline + minnotsignificant + i * space_between_names\n", "        line([(rankpos(ssums[i]), cline),\n", "              (rankpos(ssums[i]), chei),\n", "              (textspace - 0.1, chei)],\n", "             linewidth=linewidth)\n", "\n", "        color = 'k'\n", "        text(textspace - 0.2, chei, filter_names(nnames[i]), color=color, ha=\"right\", va=\"center\", size=16)\n", "        # text(textspace - 0.2, chei, filter_names(name_mapping[nnames[i]] if nnames[i] in name_mapping.keys() else nnames[i]), color=color, ha=\"right\", va=\"center\", size=16)\n", "\n", "\n", "    # Format for the second half of algorithms\n", "    for i in range(math.ceil(k / 2), k):\n", "        chei = cline + minnotsignificant + (k - i - 1) * space_between_names\n", "        line([(rankpos(ssums[i]), cline),\n", "              (rankpos(ssums[i]), chei),\n", "              (textspace + scalewidth + 0.1, chei)],\n", "             linewidth=linewidth)\n", "\n", "        color = 'k'\n", "        text(textspace + scalewidth + 0.2, chei, filter_names(nnames[i]), color=color, ha=\"left\", va=\"center\", size=16)\n", "        # text(textspace + scalewidth + 0.2, chei, filter_names(name_mapping[nnames[i]] if nnames[i] in name_mapping.keys() else nnames[i]), color=color, ha=\"left\", va=\"center\", size=16)\n", "        \n", "\n", "    # no-significance lines\n", "    def draw_lines(lines, side=0.05, height=0.1):\n", "        start = cline + 0.2\n", "\n", "        for l, r in lines:\n", "            line([(rankpos(ssums[l]) - side, start),\n", "                  (rankpos(ssums[r]) + side, start)],\n", "                 linewidth=linewidth_sign)\n", "            start += height\n", "            \n", "    start = cline + 0.2\n", "    side = -0.02\n", "    height = 0.1\n", "\n", "\n", "    #Generate cliques and plot a line to connect elements in cliques    \n", "    cliques = form_cliques(p_values, nnames)\n", "    i = 1\n", "    achieved_half = False\n", "    # Plot a line to connect elements in cliques\n", "    for clq in cliques:\n", "        if len(clq) == 1:\n", "            continue\n", "        min_idx = np.array(clq).min()\n", "        max_idx = np.array(clq).max()\n", "        if min_idx >= len(nnames) / 2 and achieved_half == False:\n", "            start = cline + 0.25\n", "            achieved_half = True\n", "        # Test\n", "        # print(\"ssums[min_idx]: {}; ssums[max_idx]: {}\".format(ssums[min_idx], ssums[max_idx]))\n", "        line([(rankpos(ssums[min_idx]) - side, start),\n", "              (rankpos(ssums[max_idx]) + side, start)],\n", "             linewidth=linewidth_sign)\n", "        start += height\n", "\n", "def form_cliques(p_values, nnames):\n", "    m = len(nnames)\n", "    g_data = np.zeros((m, m), dtype=np.int64)\n", "    for p in p_values:\n", "        if p[3] == False:\n", "            i = np.where(nnames == p[0])[0][0]\n", "            j = np.where(nnames == p[1])[0][0]\n", "            min_i = min(i, j)\n", "            max_j = max(i, j)\n", "            g_data[min_i, max_j] = 1\n", "    g = networkx.Graph(g_data)\n", "\n", "    #Test\n", "    # print(\"p_values in form_cliques:\\n{}\".format(p_values))\n", "    # print(\"g_data:\\n{}\".format(g_data))\n", "\n", "    # Returns all maximal cliques in an undirected graph.\n", "    return networkx.find_cliques(g)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ranking: ['SR', 'CNN', 'IForest', 'LSTMAD', 'POLY', 'MOMENT (FT)', 'MOMENT (ZS)', 'Series2Graph', 'Sub-PCA', 'USAD']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1270497/1644961721.py:21: UserWarning: This figure includes Axes that are not compatible with tight_layout, so results might be incorrect.\n", "  plt.tight_layout()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x415 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_acc = pd.read_csv('benchmark_eval_results/uni_mergedTable_VUS-PR.csv')\n", "Comparaed_Solution_Pool = ['Sub-PCA', 'MOMENT (FT)', 'MOMENT (ZS)', 'POLY', 'CNN', 'SR', 'Series2Graph', 'LSTMAD', 'IForest', 'USAD']\n", "\n", "df_filter = df_acc[df_acc['point_anomaly'] == 1]\n", "# df_filter = df_acc[df_acc['seq_anomaly'] == 1]\n", "# df_filter = df_acc[df_acc['num_anomaly'] == 1]\n", "# df_filter = df_acc[df_acc['num_anomaly'] > 1]\n", "\n", "eval_list = []\n", "for index, row in df_filter.iterrows():\n", "    for method in Comparaed_Solution_Pool:\n", "        eval_list.append([method, row['file'], row[method]])\n", "eval_df = pd.DataFrame(eval_list, columns=['classifier_name', 'dataset_name', 'accuracy'])\n", "p_values, average_ranks, _ = <PERSON>yi(df_perf=eval_df, alpha=0.05)\n", "ranking = average_ranks.keys().to_list()[::-1]\n", "\n", "graph_ranks(average_ranks.values, average_ranks.keys(), p_values,\n", "            cd=None, reverse=True, width=5, textspace=1.5)\n", "print('ranking:', ranking[:10])\n", "plt.title(\"Critical Diagram ({}=0.05)\".format(r'$\\alpha$'),fontsize=20)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}