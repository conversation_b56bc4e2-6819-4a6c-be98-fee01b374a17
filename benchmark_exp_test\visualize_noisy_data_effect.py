import os
import sys
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.io as pio

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Copied Functions for Self-Containment ---

def load_clean_dataset(dataset_path):
    """Loads a dataset and returns the training part."""
    if not os.path.exists(dataset_path):
        raise FileNotFoundError(f"Dataset not found at {dataset_path}")
    df = pd.read_csv(dataset_path)
    try:
        train_size_str = os.path.basename(os.path.dirname(dataset_path)).split('_tr_')[-1].split('_')[0]
        split_point = int(train_size_str)
    except (ValueError, IndexError):
        file_base = os.path.basename(dataset_path)
        if '_tr_' in file_base:
             train_size_str = file_base.split('_tr_')[-1].split('_')[0]
             split_point = int(train_size_str)
        else:
            split_point = int(len(df) * 0.7)
    train_data = df.iloc[:split_point, [0]].values.astype(np.float32)
    return train_data

def inject_noise(data, noise_ratio, magnitude_std=1.5):
    """Injects spike-like noise into a dataset by adding/subtracting large values."""
    if noise_ratio == 0:
        return data
    noisy_data = data.copy()
    num_points = len(data)
    num_noise_points = int(num_points * noise_ratio)
    if num_noise_points > num_points:
        num_noise_points = num_points
    noise_indices = np.random.choice(num_points, num_noise_points, replace=False)
    signal_std = data.std()
    if signal_std == 0:
        signal_std = 1.0
    noise_magnitude_std = signal_std * magnitude_std
    for idx in noise_indices:
        noise = np.random.normal(0, noise_magnitude_std)
        noisy_data[idx, 0] += noise
    return noisy_data

def main():
    """Visualize the effect of adding 100% noise to the training data using Plotly."""
    # --- Config ---
    DATASET_NAME = '836_Exathlon_id_27_Facility_tr_10766_1st_12590'
    PLOT_WINDOW = slice(0, 500) # Visualize the first 500 points

    # --- Load and Process Data ---
    print(f"Loading clean dataset: {DATASET_NAME}")
    dataset_path = os.path.join(project_root, 'Datasets/TSB-AD-U/', DATASET_NAME + '.csv')
    clean_train_data = load_clean_dataset(dataset_path)

    print("Injecting 100% noise...")
    noisy_train_data = inject_noise(clean_train_data, 1.0)

    # --- Visualization with Plotly ---
    print("Generating comparison plot with Plotly...")
    fig = go.Figure()

    # Plot the noisy data as a scatter plot
    fig.add_trace(go.Scatter(
        y=noisy_train_data[PLOT_WINDOW].flatten(),
        mode='markers',
        marker=dict(color='#ff7f0e', size=4, opacity=0.6),
        name='Training Data (Signal + 100% Noise)'
    ))

    # Plot the original signal as a clean line
    fig.add_trace(go.Scatter(
        y=clean_train_data[PLOT_WINDOW].flatten(),
        mode='lines',
        line=dict(color='#1f77b4', width=3),
        name='Original Clean Signal (Hidden Pattern)'
    ))

    # Update layout for a professional look
    fig.update_layout(
        title_text='<b>Visualizing the Effect of 100% Additive Noise</b>',
        title_x=0.5,
        xaxis_title='Time Step',
        yaxis_title='Signal Amplitude',
        font=dict(family="Times New Roman", size=16),
        legend=dict(yanchor="top", y=0.98, xanchor="right", x=0.98),
        template='plotly_white',
        height=700,
        width=1400
    )

    # --- Save Figure ---
    output_dir = os.path.join(project_root, 'visualizations', 'robustness_analysis')
    os.makedirs(output_dir, exist_ok=True)
    
    output_filename = f'noise_effect_visualization_{DATASET_NAME}_plotly.png'
    output_path = os.path.join(output_dir, output_filename)
    pio.write_image(fig, output_path, scale=2)
    print(f"\nVerification plot saved to: {output_path}")

if __name__ == '__main__':
    main() 