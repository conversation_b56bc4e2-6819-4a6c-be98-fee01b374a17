import json
import os
import matplotlib.pyplot as plt
import numpy as np

def visualize_summary(project_root):
    """
    使用matplotlib创建一个2x2的子图网格，显示模型对噪声的鲁棒性分析
    """
    results_path = os.path.join(project_root, 'visualizations', 'robustness_analysis', 'robustness_results.json')
    output_path = os.path.join(project_root, 'visualizations', 'robustness_analysis', 'robustness_summary_subplots.png')

    if not os.path.exists(results_path):
        print(f"Error: Results file not found at {results_path}")
        return

    with open(results_path, 'r') as f:
        all_results = json.load(f)

    # 设置matplotlib样式
    plt.style.use('default')
    plt.rcParams.update({
        'font.family': 'Arial',
        'font.size': 14,
        'axes.linewidth': 1.2,
        'axes.grid': True,
        'grid.alpha': 0.3,
        'figure.facecolor': 'white'
    })
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    markers = ['o', 's', '^', 'D']
    
    dataset_clean_names = {
        "001_NAB_id_1_Facility_tr_1007_1st_2014": "NAB",
        "815_Exathlon_id_6_Facility_tr_10766_1st_12590": "Exathlon", 
        "002_MSL_id_1_Sensor_tr_500_1st_900": "MSL",
        "018_Daphnet_id_1_HumanActivity_tr_9693_1st_20732": "Daphnet"
    }

    # 过滤数据
    filtered_results = {
        name: data for name, data in all_results.items() 
        if name in dataset_clean_names and data['scores'][0] >= 0.2
    }
    
    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(12, 9))
    fig.suptitle('', fontsize=1)  # 空的总标题
    
    # 绘制每个数据集
    for i, (dataset_name, results) in enumerate(filtered_results.items()):
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        clean_name = dataset_clean_names.get(dataset_name, dataset_name)
        
        # 绘制主线
        ax.plot(results['noise_levels'], results['scores'], 
                color=colors[i], marker=markers[i], linewidth=2.5, 
                markersize=8, label=clean_name)

        # 添加基线
        baseline_score = results['scores'][0]
        ax.axhline(y=baseline_score, color='gray', linestyle='--', linewidth=2, alpha=0.7)
        
        # 设置子图标题
        ax.set_title(f'{clean_name}', fontsize=18, fontweight='bold', pad=15)

        # 设置y轴范围和网格
        ax.set_ylim(0, 1.1)
        ax.grid(True, alpha=0.3)
        
        # 设置x轴刻度
        ax.set_xticks(np.arange(0, 101, 20))
        ax.set_xticklabels([f'{int(x)}%' for x in np.arange(0, 101, 20)])

        # 只在左列显示y轴标签
        if col == 0:
            ax.set_ylabel('VUS-PR Score', fontsize=16, fontweight='bold')
        else:
            ax.set_yticklabels([])
            
        # 只在底行显示x轴标签
        if row == 1:
            ax.set_xlabel('Percentage of Noise in Training Data (%)', fontsize=16, fontweight='bold')
        else:
            ax.set_xticklabels([])
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(hspace=0.3, wspace=0.15)

    # 保存图像
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"\nFinal subplot summary plot created successfully at: {output_path}")

if __name__ == '__main__':
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root_dir = os.path.dirname(current_dir)
    visualize_summary(project_root_dir) 