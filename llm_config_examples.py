"""
LLM Configuration Examples for HTA-AD Explanation Module

This file provides configuration examples for different LLM providers
that can be used with the HTA-AD explanation module.
"""

import os
from typing import Dict, Any

# =============================================================================
# OpenAI Configuration
# =============================================================================

def get_openai_config() -> Dict[str, Any]:
    """
    Configuration for OpenAI GPT models
    
    Requirements:
    - pip install openai
    - Set OPENAI_API_KEY environment variable
    
    Returns:
    -------
    Dict[str, Any]
        OpenAI configuration
    """
    return {
        'provider': 'openai',
        'model': 'gpt-3.5-turbo',  # or 'gpt-4', 'gpt-4-turbo'
        'api_key': os.getenv('OPENAI_API_KEY'),
        'base_url': None,  # Use default OpenAI endpoint
        'max_tokens': 500,
        'temperature': 0.3
    }

def get_openai_azure_config() -> Dict[str, Any]:
    """
    Configuration for Azure OpenAI Service
    
    Requirements:
    - pip install openai
    - Set AZURE_OPENAI_KEY and AZURE_OPENAI_ENDPOINT environment variables
    
    Returns:
    -------
    Dict[str, Any]
        Azure OpenAI configuration
    """
    return {
        'provider': 'openai',
        'model': 'gpt-35-turbo',  # Azure deployment name
        'api_key': os.getenv('AZURE_OPENAI_KEY'),
        'base_url': os.getenv('AZURE_OPENAI_ENDPOINT'),
        'max_tokens': 500,
        'temperature': 0.3
    }

# =============================================================================
# Anthropic Claude Configuration
# =============================================================================

def get_anthropic_config() -> Dict[str, Any]:
    """
    Configuration for Anthropic Claude models
    
    Requirements:
    - pip install anthropic
    - Set ANTHROPIC_API_KEY environment variable
    
    Returns:
    -------
    Dict[str, Any]
        Anthropic configuration
    """
    return {
        'provider': 'anthropic',
        'model': 'claude-3-sonnet-20240229',  # or 'claude-3-opus-20240229', 'claude-3-haiku-20240307'
        'api_key': os.getenv('ANTHROPIC_API_KEY'),
        'max_tokens': 500,
        'temperature': 0.3
    }

# =============================================================================
# Ollama Local Configuration
# =============================================================================

def get_ollama_config(model_name: str = 'llama2') -> Dict[str, Any]:
    """
    Configuration for Ollama local LLM
    
    Requirements:
    - Install Ollama: https://ollama.ai/
    - Pull model: ollama pull llama2
    - Start Ollama service: ollama serve
    
    Parameters:
    ----------
    model_name : str
        Name of the Ollama model (llama2, mistral, codellama, etc.)
    
    Returns:
    -------
    Dict[str, Any]
        Ollama configuration
    """
    return {
        'provider': 'ollama',
        'model': model_name,
        'base_url': 'http://localhost:11434',
        'max_tokens': 500,
        'temperature': 0.3
    }

# =============================================================================
# Local Hugging Face Configuration
# =============================================================================

def get_local_huggingface_config(model_name: str = 'microsoft/DialoGPT-medium') -> Dict[str, Any]:
    """
    Configuration for local Hugging Face transformers
    
    Requirements:
    - pip install transformers torch
    
    Parameters:
    ----------
    model_name : str
        Hugging Face model name
    
    Returns:
    -------
    Dict[str, Any]
        Local Hugging Face configuration
    """
    return {
        'provider': 'local',
        'model': model_name,
        'max_tokens': 200,  # Smaller for local models
        'temperature': 0.3
    }

# =============================================================================
# Custom API Configuration
# =============================================================================

def get_custom_api_config(base_url: str, api_key: str = None) -> Dict[str, Any]:
    """
    Configuration for custom OpenAI-compatible API
    
    Parameters:
    ----------
    base_url : str
        Base URL of the custom API
    api_key : str, optional
        API key if required
    
    Returns:
    -------
    Dict[str, Any]
        Custom API configuration
    """
    return {
        'provider': 'openai',  # Use OpenAI-compatible format
        'model': 'custom-model',
        'api_key': api_key,
        'base_url': base_url,
        'max_tokens': 500,
        'temperature': 0.3
    }

# =============================================================================
# Configuration Selector
# =============================================================================

def get_llm_config(provider: str, **kwargs) -> Dict[str, Any]:
    """
    Get LLM configuration based on provider name
    
    Parameters:
    ----------
    provider : str
        LLM provider name
    **kwargs
        Additional configuration parameters
    
    Returns:
    -------
    Dict[str, Any]
        LLM configuration
    """
    if provider.lower() == 'openai':
        return get_openai_config()
    elif provider.lower() == 'azure':
        return get_openai_azure_config()
    elif provider.lower() == 'anthropic':
        return get_anthropic_config()
    elif provider.lower() == 'ollama':
        model_name = kwargs.get('model', 'llama2')
        return get_ollama_config(model_name)
    elif provider.lower() == 'local':
        model_name = kwargs.get('model', 'microsoft/DialoGPT-medium')
        return get_local_huggingface_config(model_name)
    elif provider.lower() == 'custom':
        base_url = kwargs.get('base_url')
        api_key = kwargs.get('api_key')
        if not base_url:
            raise ValueError("base_url is required for custom provider")
        return get_custom_api_config(base_url, api_key)
    else:
        raise ValueError(f"Unsupported provider: {provider}")

# =============================================================================
# Setup Instructions
# =============================================================================

SETUP_INSTRUCTIONS = {
    'openai': """
OpenAI Setup:
1. Install: pip install openai
2. Get API key from: https://platform.openai.com/api-keys
3. Set environment variable: export OPENAI_API_KEY="your-api-key"
4. Usage: config = get_openai_config()
""",
    
    'azure': """
Azure OpenAI Setup:
1. Install: pip install openai
2. Create Azure OpenAI resource in Azure portal
3. Get API key and endpoint from Azure portal
4. Set environment variables:
   export AZURE_OPENAI_KEY="your-api-key"
   export AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
5. Usage: config = get_openai_azure_config()
""",
    
    'anthropic': """
Anthropic Claude Setup:
1. Install: pip install anthropic
2. Get API key from: https://console.anthropic.com/
3. Set environment variable: export ANTHROPIC_API_KEY="your-api-key"
4. Usage: config = get_anthropic_config()
""",
    
    'ollama': """
Ollama Setup:
1. Install Ollama from: https://ollama.ai/
2. Pull a model: ollama pull llama2
3. Start Ollama service: ollama serve
4. Usage: config = get_ollama_config('llama2')

Available models: llama2, mistral, codellama, vicuna, etc.
""",
    
    'local': """
Local Hugging Face Setup:
1. Install: pip install transformers torch
2. Usage: config = get_local_huggingface_config('microsoft/DialoGPT-medium')

Popular models:
- microsoft/DialoGPT-medium (conversational)
- google/flan-t5-base (instruction following)
- facebook/blenderbot-400M-distill (chatbot)
""",
    
    'custom': """
Custom API Setup:
1. Ensure your API is OpenAI-compatible
2. Usage: config = get_custom_api_config('http://your-api-url', 'your-api-key')
"""
}

def print_setup_instructions(provider: str = None):
    """
    Print setup instructions for LLM providers
    
    Parameters:
    ----------
    provider : str, optional
        Specific provider to show instructions for. If None, shows all.
    """
    if provider:
        if provider.lower() in SETUP_INSTRUCTIONS:
            print(SETUP_INSTRUCTIONS[provider.lower()])
        else:
            print(f"No setup instructions available for: {provider}")
    else:
        print("LLM Provider Setup Instructions:")
        print("=" * 50)
        for provider_name, instructions in SETUP_INSTRUCTIONS.items():
            print(f"\n{provider_name.upper()}:")
            print(instructions)

# =============================================================================
# Example Usage
# =============================================================================

if __name__ == "__main__":
    print("LLM Configuration Examples for HTA-AD")
    print("=" * 40)
    
    # Show all setup instructions
    print_setup_instructions()
    
    # Example configurations
    print("\n" + "=" * 40)
    print("Example Configurations:")
    print("=" * 40)
    
    try:
        # OpenAI example
        openai_config = get_llm_config('openai')
        print(f"OpenAI Config: {openai_config}")
    except Exception as e:
        print(f"OpenAI Config Error: {e}")
    
    try:
        # Ollama example
        ollama_config = get_llm_config('ollama', model='llama2')
        print(f"Ollama Config: {ollama_config}")
    except Exception as e:
        print(f"Ollama Config Error: {e}")
    
    try:
        # Local example
        local_config = get_llm_config('local')
        print(f"Local Config: {local_config}")
    except Exception as e:
        print(f"Local Config Error: {e}")
