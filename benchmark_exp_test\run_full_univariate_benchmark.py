# -*- coding: utf-8 -*-
# run_full_univariate_benchmark.py

import os
import sys
import pandas as pd
import time
from tqdm import tqdm
import numpy as np
import warnings

warnings.filterwarnings('ignore')

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- TSB-AD & Model Imports ---
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.evaluation.metrics import get_metrics
from visualize_results import plot_detection_results

# Import all models to be tested
from benchmark_exp.Lightweight_TS_Model import LTS_AD
from benchmark_exp.gru_tcn_ae import GruTcnAe_AD
from benchmark_exp.gru_tcn_vae import GruTcnVae_AD
from benchmark_exp.attn_tcn_vae import AttnTcnVAE_AD
from benchmark_exp.temporal_attn_tcn_vae import TemporalAttn_TCN_VAE

# --- Data Loader ---
class UnivariateDataset:
    """A simple data loader for univariate CSV files."""
    def __init__(self, dataset_path):
        df = pd.read_csv(dataset_path).dropna()
        try:
            train_size_str = os.path.basename(dataset_path).split('_')[-3]
            train_size = int(train_size_str)
        except (ValueError, IndexError):
            train_size = int(len(df) * 0.7)

        self.data = df.iloc[:, 0].values.reshape(-1, 1)
        self.label = df.iloc[:, 1].values
        self.train = self.data[:train_size]
        self.name = os.path.basename(dataset_path)

# --- Configuration ---
DATASET_ROOT = 'Datasets/TSB-AD-U/'
OUTPUT_DIR = 'full_univariate_benchmark_results'
GPU_ID = 0 # Corresponds to 'gpu1'

# --- Model Definitions ---
MODELS = {
    'LTS_AD': {'class': LTS_AD, 'params': {'window_size': 100, 'epochs': 30, 'lr': 1e-3, 'batch_size': 128, 'latent_dim': 16, 'gpu': GPU_ID}},
    'Attn-TCN-VAE': {'class': AttnTcnVAE_AD, 'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-4, 'batch_size': 64, 'latent_dim': 32, 'tcn_layers': 4, 'gpu': GPU_ID}},
    'GRU-TCN-VAE': {'class': GruTcnVae_AD, 'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-4, 'batch_size': 64, 'latent_dim': 32, 'tcn_layers': 4, 'gru_hidden_dim': 64, 'gpu': GPU_ID}},
    'GRU-TCN-AE': {'class': GruTcnAe_AD, 'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-4, 'batch_size': 64, 'latent_dim': 32, 'tcn_layers': 4, 'gru_hidden_dim': 64, 'gpu': GPU_ID}},
    'TemporalAttn-TCN-VAE': {'class': TemporalAttn_TCN_VAE, 'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-4, 'batch_size': 64, 'latent_dim': 32, 'tcn_layers': 4, 'gpu': GPU_ID}},
}

def run_single_experiment(model_name, model_info, dataset_path, dataset_output_dir):
    """Runs a single model on a single dataset and saves the results."""
    try:
        dataset = UnivariateDataset(dataset_path)
        
        start_time = time.time()
        model = model_info['class'](HP=model_info['params'])
        model.fit(dataset.train)
        
        scores = model.decision_function(dataset.data)
        runtime = time.time() - start_time
        
        min_len = min(len(scores), len(dataset.label))
        scores, label = scores[:min_len], dataset.label[:min_len]
        
        slidingWindow = find_length_rank(dataset.data[:min_len], rank=1)
        results = get_metrics(scores, label, slidingWindow=slidingWindow)
        results['runtime(s)'] = runtime
        results['model'] = model_name
        results['dataset'] = dataset.name

        # --- Save detailed results ---
        pd.DataFrame([results]).to_csv(os.path.join(dataset_output_dir, f"{model_name}_metrics.csv"), index=False)
        plot_detection_results(
            data=dataset.data, label=label, score=scores,
            model_name=model_name, filename=dataset.name,
            train_size=len(dataset.train), save_dir=dataset_output_dir
        )
        
        return results
    except Exception as e:
        print(f"❌ Error running {model_name} on {dataset.name}: {e}")
        return None

def main():
    """Main function to run the full univariate benchmark."""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    all_datasets = sorted([f for f in os.listdir(DATASET_ROOT) if f.endswith('.csv')])
    if not all_datasets:
        print(f"❌ No datasets found in {DATASET_ROOT}. Exiting.")
        return
        
    print(f"🔬 Starting full univariate benchmark on {len(all_datasets)} datasets...")
    print("   Models to be tested:", list(MODELS.keys()))

    all_results = []
    
    for dataset_name in tqdm(all_datasets, desc="Total Progress"):
        dataset_path = os.path.join(DATASET_ROOT, dataset_name)
        dataset_output_dir = os.path.join(OUTPUT_DIR, os.path.splitext(dataset_name)[0])
        os.makedirs(dataset_output_dir, exist_ok=True)
        
        print(f"\n--- Processing Dataset: {dataset_name} ---")
        
        for model_name, model_info in MODELS.items():
            print(f"  -> Running Model: {model_name}")
            results = run_single_experiment(model_name, model_info, dataset_path, dataset_output_dir)
            if results:
                all_results.append(results)
                print(f"     ✅ Finished. PA-F1: {results.get('PA-F1', 'N/A'):.4f}, Runtime: {results.get('runtime(s)', 'N/A'):.2f}s")
    
    if not all_results:
        print("\n❌ No results were generated. Exiting.")
        return

    # --- Create and save final summary report ---
    summary_df = pd.DataFrame(all_results)
    avg_per_model = summary_df.drop(columns=['dataset']).groupby('model').mean()
    
    # Save detailed summary
    summary_df.to_csv(os.path.join(OUTPUT_DIR, '_summary_detailed.csv'), index=False)
    # Save average summary
    avg_per_model.to_csv(os.path.join(OUTPUT_DIR, '_summary_average.csv'))
    
    print("\n\n🎉 Full Univariate Benchmark Finished!")
    print(f"📄 Average performance report saved to: {os.path.join(OUTPUT_DIR, '_summary_average.csv')}")

if __name__ == '__main__':
    main() 