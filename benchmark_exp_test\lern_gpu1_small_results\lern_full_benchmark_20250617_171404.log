2025-06-17 17:14:04,409 - INFO - 🚀 开始LERN v2.3 全数据集基准测试
2025-06-17 17:14:04,410 - INFO - 💻 使用GPU: 1
2025-06-17 17:14:04,410 - INFO - 📁 数据集目录: ../Datasets/TSB-AD-U/
2025-06-17 17:14:04,410 - INFO - 💾 结果保存到: ./lern_gpu1_small_results/
2025-06-17 17:14:04,410 - INFO - 🔧 超参数配置: {'window_size': 50, 'epochs': 100, 'lr': 0.001, 'batch_size': 64, 'latent_dim': 64, 'poly_degree': 3, 'patience': 10}
2025-06-17 17:14:04,410 - INFO - 📂 总共需要处理 50 个数据集
2025-06-17 17:14:04,411 - INFO - 🚀 开始处理: 001_NAB_id_1_Facility_tr_1007_1st_2014.csv
2025-06-17 17:14:04,413 - INFO -    数据形状: (4031, 1), 异常率: 0.085, 训练集: 1007
2025-06-17 17:14:05,284 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:14:51,549 - INFO -    ✅ 成功! 时间: 47.1s, AUC-ROC: 0.7559, AUC-PR: 0.5177
2025-06-17 17:14:51,550 - INFO - 🚀 开始处理: 002_NAB_id_2_WebService_tr_1500_1st_4106.csv
2025-06-17 17:14:51,551 - INFO -    数据形状: (6000, 1), 异常率: 0.106, 训练集: 1500
2025-06-17 17:14:51,592 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:15:57,603 - INFO -    ✅ 成功! 时间: 65.7s, AUC-ROC: 0.3895, AUC-PR: 0.2904
2025-06-17 17:15:57,603 - INFO - 🚀 开始处理: 003_NAB_id_3_WebService_tr_1362_1st_1462.csv
2025-06-17 17:15:57,605 - INFO -    数据形状: (15852, 1), 异常率: 0.096, 训练集: 1362
2025-06-17 17:15:57,645 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:16:27,881 - ERROR -    ❌ 失败: Input contains NaN.
2025-06-17 17:16:27,881 - INFO - 🚀 开始处理: 004_NAB_id_4_Facility_tr_1007_1st_1437.csv
2025-06-17 17:16:27,882 - INFO -    数据形状: (4031, 1), 异常率: 0.100, 训练集: 1007
2025-06-17 17:16:27,918 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:16:44,247 - INFO -    ✅ 成功! 时间: 16.3s, AUC-ROC: 0.7453, AUC-PR: 0.4088
2025-06-17 17:16:44,247 - INFO - 🚀 开始处理: 005_NAB_id_5_Traffic_tr_594_1st_1645.csv
2025-06-17 17:16:44,248 - INFO -    数据形状: (2379, 1), 异常率: 0.100, 训练集: 594
2025-06-17 17:16:44,310 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:17:05,895 - INFO -    ✅ 成功! 时间: 21.6s, AUC-ROC: 0.4248, AUC-PR: 0.1040
2025-06-17 17:17:05,895 - INFO - 🚀 开始处理: 006_NAB_id_6_Traffic_tr_2579_1st_5839.csv
2025-06-17 17:17:05,897 - INFO -    数据形状: (10319, 1), 异常率: 0.100, 训练集: 2579
2025-06-17 17:17:05,932 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:18:55,910 - INFO -    ✅ 成功! 时间: 109.2s, AUC-ROC: 0.6589, AUC-PR: 0.2578
2025-06-17 17:18:55,910 - INFO - 🚀 开始处理: 007_NAB_id_7_Traffic_tr_624_1st_2087.csv
2025-06-17 17:18:55,912 - INFO -    数据形状: (2499, 1), 异常率: 0.099, 训练集: 624
2025-06-17 17:18:55,949 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:19:15,777 - INFO -    ✅ 成功! 时间: 19.5s, AUC-ROC: 0.6844, AUC-PR: 0.5214
2025-06-17 17:19:15,778 - INFO - 🚀 开始处理: 008_NAB_id_8_Synthetic_tr_1007_1st_2734.csv
2025-06-17 17:19:15,780 - INFO -    数据形状: (4031, 1), 异常率: 0.100, 训练集: 1007
2025-06-17 17:19:15,816 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:19:28,344 - ERROR -    ❌ 失败: Input contains NaN.
2025-06-17 17:19:28,344 - INFO - 🚀 开始处理: 009_NAB_id_9_Traffic_tr_500_1st_438.csv
2025-06-17 17:19:28,345 - INFO -    数据形状: (2161, 1), 异常率: 0.100, 训练集: 500
2025-06-17 17:19:28,380 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:19:52,640 - INFO -    ✅ 成功! 时间: 24.1s, AUC-ROC: 0.5894, AUC-PR: 0.1722
2025-06-17 17:19:52,640 - INFO - 🚀 开始处理: 010_NAB_id_10_WebService_tr_500_1st_271.csv
2025-06-17 17:19:52,641 - INFO -    数据形状: (1000, 1), 异常率: 0.148, 训练集: 500
2025-06-17 17:19:52,680 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:20:04,350 - INFO -    ✅ 成功! 时间: 11.3s, AUC-ROC: 0.9973, AUC-PR: 0.9832
2025-06-17 17:20:04,351 - INFO - 💾 已保存批次 1 的中间结果 (10/50)
2025-06-17 17:20:04,351 - INFO - 🚀 开始处理: 011_NAB_id_11_Facility_tr_1007_1st_1526.csv
2025-06-17 17:20:04,353 - INFO -    数据形状: (4031, 1), 异常率: 0.118, 训练集: 1007
2025-06-17 17:20:04,390 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:20:50,592 - INFO -    ✅ 成功! 时间: 45.8s, AUC-ROC: 0.8286, AUC-PR: 0.7054
2025-06-17 17:20:50,592 - INFO - 🚀 开始处理: 012_NAB_id_12_Synthetic_tr_1007_1st_2787.csv
2025-06-17 17:20:50,594 - INFO -    数据形状: (4031, 1), 异常率: 0.127, 训练集: 1007
2025-06-17 17:20:50,629 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:21:36,872 - INFO -    ✅ 成功! 时间: 45.7s, AUC-ROC: 0.2651, AUC-PR: 0.0841
2025-06-17 17:21:36,872 - INFO - 🚀 开始处理: 013_NAB_id_13_Traffic_tr_623_1st_2084.csv
2025-06-17 17:21:36,873 - INFO -    数据形状: (2494, 1), 异常率: 0.099, 训练集: 623
2025-06-17 17:21:36,908 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:22:01,680 - INFO -    ✅ 成功! 时间: 24.3s, AUC-ROC: 0.7882, AUC-PR: 0.5216
2025-06-17 17:22:01,681 - INFO - 🚀 开始处理: 014_NAB_id_14_WebService_tr_500_1st_1045.csv
2025-06-17 17:22:01,682 - INFO -    数据形状: (1537, 1), 异常率: 0.099, 训练集: 500
2025-06-17 17:22:01,720 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:22:19,611 - INFO -    ✅ 成功! 时间: 17.9s, AUC-ROC: 0.4717, AUC-PR: 0.3859
2025-06-17 17:22:19,611 - INFO - 🚀 开始处理: 015_NAB_id_15_Synthetic_tr_1007_1st_2787.csv
2025-06-17 17:22:19,612 - INFO -    数据形状: (4031, 1), 异常率: 0.127, 训练集: 1007
2025-06-17 17:22:19,649 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:23:03,830 - INFO -    ✅ 成功! 时间: 43.6s, AUC-ROC: 0.3896, AUC-PR: 0.1012
2025-06-17 17:23:03,830 - INFO - 🚀 开始处理: 016_NAB_id_16_Environment_tr_1816_1st_3540.csv
2025-06-17 17:23:03,831 - INFO -    数据形状: (7266, 1), 异常率: 0.100, 训练集: 1816
2025-06-17 17:23:03,874 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:24:20,387 - INFO -    ✅ 成功! 时间: 76.5s, AUC-ROC: 0.5489, AUC-PR: 0.1178
2025-06-17 17:24:20,387 - INFO - 🚀 开始处理: 017_NAB_id_17_Synthetic_tr_1007_1st_1805.csv
2025-06-17 17:24:20,389 - INFO -    数据形状: (4031, 1), 异常率: 0.100, 训练集: 1007
2025-06-17 17:24:20,424 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:24:30,835 - ERROR -    ❌ 失败: Input contains NaN.
2025-06-17 17:24:30,835 - INFO - 🚀 开始处理: 018_NAB_id_18_Facility_tr_500_1st_669.csv
2025-06-17 17:24:30,836 - INFO -    数据形状: (1881, 1), 异常率: 0.100, 训练集: 500
2025-06-17 17:24:30,884 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:24:37,561 - ERROR -    ❌ 失败: Input contains NaN.
2025-06-17 17:24:37,561 - INFO - 🚀 开始处理: 019_NAB_id_19_Facility_tr_1007_1st_1171.csv
2025-06-17 17:24:37,562 - INFO -    数据形状: (4031, 1), 异常率: 0.099, 训练集: 1007
2025-06-17 17:24:37,598 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
