# -*- coding: utf-8 -*-
# GRU-based Temporal Convolutional Network Variational Autoencoder (GRU-TCN-VAE)
# A model combining TCN for local feature extraction and GRU for sequential modeling,
# all within a VAE framework. This model replaces the Attention mechanism of Attn-TCN-VAE.
#
# This file is structured to be compatible with the TSB-AD benchmark framework.

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import sys
from torch.utils.data import DataLoader, TensorDataset, random_split
from torch.nn.utils import weight_norm

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector

from sklearn.preprocessing import MinMaxScaler


# ----------------------------------------------------
# 1. Model Definition (Copied and adapted from attn_tcn_vae.py)
# ----------------------------------------------------
class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding=padding, dilation=dilation))
        self.chomp1 = lambda x: x[:, :, :-padding].contiguous()
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)

        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding=padding, dilation=dilation))
        self.chomp2 = lambda x: x[:, :, :-padding].contiguous()
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)

        self.net = nn.Sequential(self.conv1, self.relu1, self.dropout1,
                                 self.conv2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        self.init_weights()

    def init_weights(self):
        self.conv1.weight.data.normal_(0, 0.01)
        self.conv2.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)

    def forward(self, x):
        out = self.conv1(x)
        out = self.chomp1(out)
        out = self.relu1(out)
        out = self.dropout1(out)
        
        out = self.conv2(out)
        out = self.chomp2(out)
        out = self.relu2(out)
        out = self.dropout2(out)
        
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers += [TemporalBlock(in_channels, out_channels, kernel_size, stride=1, dilation=dilation_size,
                                     padding=(kernel_size-1) * dilation_size, dropout=dropout)]

        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

class GruTcnVae_Model(nn.Module):
    """The core GRU-TCN-VAE neural network architecture."""
    def __init__(self, input_dim, window_size, latent_dim, tcn_channels, tcn_kernel_size, tcn_dropout, gru_hidden_dim, gru_layers):
        super(GruTcnVae_Model, self).__init__()
        self.latent_dim = latent_dim
        self.window_size = window_size
        self.tcn_channels = tcn_channels
        self.input_dim = input_dim
        self.gru_hidden_dim = gru_hidden_dim

        # === Encoder ===
        self.encoder_tcn = TemporalConvNet(
            num_inputs=input_dim,
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size,
            dropout=tcn_dropout
        )
        self.encoder_gru = nn.GRU(
            input_size=tcn_channels[-1],
            hidden_size=gru_hidden_dim,
            num_layers=gru_layers,
            batch_first=True
        )
        self.fc_mu = nn.Linear(gru_hidden_dim, latent_dim)
        self.fc_log_var = nn.Linear(gru_hidden_dim, latent_dim)

        # === Decoder ===
        self.decoder_fc = nn.Linear(latent_dim, window_size * tcn_channels[-1])
        self.decoder_tcn = TemporalConvNet(
            num_inputs=tcn_channels[-1],
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size,
            dropout=tcn_dropout
        )
        self.decoder_conv_out = nn.Conv1d(tcn_channels[-1], input_dim, kernel_size=1)

    def reparameterize(self, mu, log_var):
        std = torch.exp(0.5 * log_var)
        eps = torch.randn_like(std)
        return mu + eps * std

    def forward(self, x):
        # x shape: (batch_size, window_size, input_dim)
        x = x.permute(0, 2, 1)  # -> (batch, input_dim, window_size) for TCN

        # Encoder
        tcn_out = self.encoder_tcn(x)
        tcn_out = tcn_out.permute(0, 2, 1) # -> (batch, window_size, tcn_channels[-1])
        
        # Pass through GRU and take the last hidden state
        _, gru_hidden = self.encoder_gru(tcn_out)
        gru_out = gru_hidden[-1] # -> (batch, gru_hidden_dim)

        mu = self.fc_mu(gru_out)
        log_var = self.fc_log_var(gru_out)
        z = self.reparameterize(mu, log_var)

        # Decoder
        dec_in = self.decoder_fc(z)
        dec_in = dec_in.view(-1, self.tcn_channels[-1], self.window_size)
        dec_tcn_out = self.decoder_tcn(dec_in)
        reconstructed = self.decoder_conv_out(dec_tcn_out)
        reconstructed = reconstructed.permute(0, 2, 1)
        reconstructed = torch.sigmoid(reconstructed) # Sigmoid for output scaled to [0,1]

        return reconstructed, mu, log_var


# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class GruTcnVae_AD(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        # Model hyperparameters
        self.window_size = HP.get('window_size', 128)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)
        self.beta = HP.get('beta', 1.0)
        
        # TCN parameters
        tcn_layers = HP.get('tcn_layers', 4)
        tcn_filters = HP.get('tcn_filters', 64)
        self.tcn_channels = [tcn_filters] * tcn_layers
        self.tcn_kernel_size = HP.get('tcn_kernel_size', 3)
        self.tcn_dropout = HP.get('tcn_dropout', 0.2)
        
        # GRU parameters
        self.gru_hidden_dim = HP.get('gru_hidden_dim', 64)
        self.gru_layers = HP.get('gru_layers', 1)
        
        print(f"🔄 Initializing GRU-TCN-VAE Detector... (Device: {self.device})")
        
        self.ts_scaler = MinMaxScaler()
        self.score_scaler = MinMaxScaler()
        self.model = None
        self.training_history = {}

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def _vae_loss(self, recon, x, mu, log_var):
        recon_loss = F.mse_loss(recon, x, reduction='sum')
        kld_loss = -0.5 * torch.sum(1 + log_var - mu.pow(2) - log_var.exp())
        return recon_loss + self.beta * kld_loss

    def fit(self, X, y=None):
        print(f"\n💪 开始训练 {self.__class__.__name__} 模型...")
        input_dim = X.shape[1]
        
        # Keep a reference to the original data for scoring later
        X_original_for_scoring = X

        if self.model is None:
            self.model = GruTcnVae_Model(
                input_dim=input_dim, window_size=self.window_size, latent_dim=self.latent_dim,
                tcn_channels=self.tcn_channels, tcn_kernel_size=self.tcn_kernel_size,
                tcn_dropout=self.tcn_dropout, gru_hidden_dim=self.gru_hidden_dim,
                gru_layers=self.gru_layers
            ).to(self.device)
        
        if self.normalize:
            # X is reassigned to the normalized version for training
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        print(f"   创建了 {len(windows)} 个训练窗口")
        if len(windows) == 0:
            print("⚠️ 警告: 数据长度不足以创建窗口，跳过训练。")
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        # Use AdamW optimizer and a learning rate scheduler for better convergence
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', factor=0.5, patience=5, verbose=False)

        self.model.train()
        epoch_losses = []
        for epoch in range(self.epochs):
            batch_losses = []
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                recon, mu, log_var = self.model(batch_windows)
                loss = self._vae_loss(recon, batch_windows, mu, log_var)
                
                if not (torch.isnan(loss) or torch.isinf(loss)):
                    loss.backward()
                    optimizer.step()
                    batch_losses.append(loss.item())

            epoch_loss = np.mean(batch_losses) if batch_losses else 0
            epoch_losses.append(epoch_loss)
            scheduler.step(epoch_loss)

            if (epoch + 1) % 5 == 0 or epoch == 0:
                print(f"   Epoch {epoch+1:3d}/{self.epochs}: Loss={epoch_loss:.6f}")
        
        self.training_history['loss'] = epoch_losses
        print("   ✅ 模型训练完成!")
        
        # Use the original, unnormalized data to compute scores for fitting the score scaler
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
        
    def _compute_scores(self, X, fit_scaler=False):
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0:
            return np.zeros(n_samples)
            
        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                recon, _, _ = self.model(batch_windows)
                # Use MSE per window as the reconstruction error
                error = torch.mean((batch_windows - recon)**2, dim=(1, 2))
                window_scores.extend(error.cpu().numpy())
        
        window_scores = np.array(window_scores)

        # --- Use Average Mapping for scores (from Lightweight_TS_Model) ---
        scores = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        
        for i, score in enumerate(window_scores):
            start, end = i, i + self.window_size
            scores[start:end] += score
            counts[start:end] += 1
            
        scores[counts > 0] /= counts[counts > 0]
        
        # Pad edges with the first/last valid score
        first_valid_idx = self.window_size - 1
        if n_samples > first_valid_idx:
            scores[:first_valid_idx] = scores[first_valid_idx]

        if fit_scaler:
            self.score_scaler.fit(scores.reshape(-1, 1))
            
        # The scaler expects a 2D array and returns one, so we flatten it
        return self.score_scaler.transform(scores.reshape(-1, 1)).ravel()

    def predict(self, X):
        scores = self.decision_function(X)
        scaled_scores = self.score_scaler.transform(scores.reshape(-1, 1)).flatten()
        return (scaled_scores > 0.5).astype(int)

# --- Standalone Test ---
def standalone_test():
    print("--- Running Standalone Test for GRU-TCN-VAE ---")
    
    # Generate synthetic data
    train_data = np.random.randn(1000, 1)
    test_data = np.random.randn(500, 1)
    test_data[100:150] += 5 # Inject anomaly
    
    # Define hyperparameters
    hp = {
        'window_size': 64, 'epochs': 10, 'lr': 1e-3, 'batch_size': 32,
        'latent_dim': 16, 'beta': 1.0, 'tcn_layers': 3, 'tcn_filters': 32,
        'tcn_kernel_size': 3, 'tcn_dropout': 0.1, 'gpu': 0,
        'gru_hidden_dim': 32, 'gru_layers': 1
    }
    
    # Create and train detector
    detector = GruTcnVae_AD(HP=hp, normalize=True)
    detector.fit(train_data)
    
    # Get scores
    scores = detector.decision_function(test_data)
    
    # Print results
    print("Scores shape:", scores.shape)
    print("Score for first 10 points:", scores[:10])
    print("Score around anomaly:", scores[95:155])
    
    # Check if scores are higher in the anomaly region
    normal_max = np.max(np.concatenate([scores[:100], scores[150:]]))
    anomaly_max = np.max(scores[100:150])
    print(f"Max score in normal region: {normal_max:.4f}")
    print(f"Max score in anomaly region: {anomaly_max:.4f}")
    
    if anomaly_max > normal_max:
        print("✅ Test PASSED: Anomaly scores are higher than normal scores.")
    else:
        print("❌ Test FAILED: Anomaly scores are NOT distinctly higher.")

if __name__ == '__main__':
    standalone_test() 