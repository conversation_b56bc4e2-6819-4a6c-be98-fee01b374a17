#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LERN批量测试进度监控脚本
"""

import os
import time
import glob
import pandas as pd
from datetime import datetime, <PERSON>el<PERSON>

def monitor_progress():
    print("🔍 LERN批量测试进度监控")
    print("=" * 60)
    
    # 查找结果文件
    result_files = glob.glob("lern_single_results*.csv")
    log_files = glob.glob("lern_*_results_*.csv")
    
    if result_files:
        print(f"📁 找到结果文件: {result_files}")
        
        # 读取最新结果
        latest_file = max(result_files, key=os.path.getctime)
        df = pd.read_csv(latest_file)
        
        print(f"📊 当前完成数量: {len(df)} 个数据集")
        print(f"⏱️  最新更新时间: {datetime.fromtimestamp(os.path.getctime(latest_file))}")
        
        if len(df) > 0:
            # 统计成功率
            success_count = len(df[df['status'] == 'success'])
            print(f"✅ 成功: {success_count} / {len(df)} ({success_count/len(df)*100:.1f}%)")
            
            # 平均性能
            if 'AUC-PR' in df.columns:
                avg_auc_pr = df['AUC-PR'].mean()
                avg_auc_roc = df['AUC-ROC'].mean() 
                avg_vus_pr = df['VUS-PR'].mean()
                avg_vus_roc = df['VUS-ROC'].mean()
                
                print(f"📈 平均性能:")
                print(f"   AUC-PR:  {avg_auc_pr:.4f}")
                print(f"   AUC-ROC: {avg_auc_roc:.4f}")
                print(f"   VUS-PR:  {avg_vus_pr:.4f}")
                print(f"   VUS-ROC: {avg_vus_roc:.4f}")
            
            # 最近5个结果
            print(f"\n📋 最近5个结果:")
            recent = df.tail(5)[['filename', 'AUC-PR', 'AUC-ROC', 'runtime']]
            for _, row in recent.iterrows():
                print(f"   {row['filename'][:30]:30} | AUC-PR: {row['AUC-PR']:.3f} | AUC-ROC: {row['AUC-ROC']:.3f} | {row['runtime']:.1f}s")
    
    else:
        print("📝 尚未找到结果文件，测试可能刚开始...")
    
    # 检查运行进程
    import subprocess
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lern_processes = [line for line in result.stdout.split('\n') if 'run_lern_simple.py' in line or 'Run_LERN_Detector.py' in line]
        
        if lern_processes:
            print(f"\n🔄 发现 {len(lern_processes)} 个LERN相关进程正在运行")
        else:
            print(f"\n⏸️  当前没有LERN进程运行")
            
    except:
        pass

if __name__ == "__main__":
    monitor_progress() 