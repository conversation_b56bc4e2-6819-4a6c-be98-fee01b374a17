# -*- coding: utf-8 -*-
# 极简设备测试脚本

import torch
import numpy as np
import time

def test_basic_functionality():
    """测试基础功能"""
    
    print("Enhanced CAAD-LMR 极简设备测试")
    print("=" * 40)
    
    # 1. 设备检查
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU数量: {torch.cuda.device_count()}")
        print(f"当前GPU: {torch.cuda.get_device_name()}")
    
    # 2. 测试transformers导入
    try:
        from transformers import AutoTokenizer, AutoModel
        print("✅ Transformers导入成功")
    except ImportError as e:
        print(f"❌ Transformers导入失败: {e}")
        return False
    
    # 3. 测试模型加载
    try:
        model_name = "sentence-transformers/all-MiniLM-L6-v2"
        print(f"正在加载模型: {model_name}")
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModel.from_pretrained(model_name)
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # 明确移动到设备
        model = model.to(device)
        print(f"✅ 模型加载成功，设备: {next(model.parameters()).device}")
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False
    
    # 4. 测试简单推理
    try:
        test_text = ["这是一个测试"]
        
        # tokenize
        inputs = tokenizer(test_text, return_tensors="pt", padding=True, truncation=True)
        
        # 确保输入在正确设备上
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # 推理
        with torch.no_grad():
            outputs = model(**inputs)
            features = outputs.last_hidden_state.mean(dim=1)
        
        print(f"✅ 模型推理成功")
        print(f"   输入设备: {inputs['input_ids'].device}")
        print(f"   输出设备: {features.device}")
        print(f"   特征形状: {features.shape}")
        
        # 测试设备转换
        cpu_features = features.cpu()
        if device == 'cuda':
            cuda_features = cpu_features.cuda()
            print("✅ 设备转换正常")
        
    except Exception as e:
        print(f"❌ 模型推理失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. 测试PyTorch基础操作
    try:
        x = torch.randn(10, features.shape[1]).to(device)
        y = torch.mm(x, features.t())
        print(f"✅ PyTorch操作正常，结果设备: {y.device}")
        
    except Exception as e:
        print(f"❌ PyTorch操作失败: {e}")
        return False
    
    print("\n🎉 所有基础测试通过!")
    return True

def test_tsb_ad_integration():
    """测试TSB-AD集成"""
    
    print("\nTSB-AD集成测试")
    print("-" * 30)
    
    try:
        from TSB_AD.evaluation.metrics import get_metrics
        from TSB_AD.utils.slidingWindows import find_length_rank
        print("✅ TSB-AD导入成功")
        
        # 测试基础功能
        data = np.random.randn(100, 1)
        slidingWindow = find_length_rank(data, rank=1)
        print(f"✅ 滑动窗口计算成功: {slidingWindow}")
        
        # 测试评估指标
        scores = np.random.rand(100)
        labels = np.zeros(100)
        labels[20:25] = 1  # 添加一些异常标签
        
        pred = scores > 0.8
        metrics = get_metrics(scores, labels, slidingWindow=slidingWindow, pred=pred)
        print(f"✅ 评估指标计算成功")
        
        return True
        
    except Exception as e:
        print(f"❌ TSB-AD集成测试失败: {e}")
        return False

if __name__ == '__main__':
    success1 = test_basic_functionality()
    success2 = test_tsb_ad_integration()
    
    if success1 and success2:
        print("\n🎉 所有测试通过! 可以运行Enhanced CAAD-LMR")
        print("\n下一步可以运行:")
        print("python Quick_Test_Enhanced_CAAD_LMR.py --test_synthetic")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    exit(0 if (success1 and success2) else 1) 