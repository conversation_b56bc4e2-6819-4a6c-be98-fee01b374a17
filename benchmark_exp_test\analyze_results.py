# -*- coding: utf-8 -*-
# analyze_results.py

import os
import json
import pandas as pd

def analyze_results(results_dir='multivariate_showdown_results', model_name='HTA-AD-M'):
    """
    Analyzes the JSON result files for a given model, calculates average metrics,
    and prints a summary.
    """
    model_results_path = os.path.join(results_dir, model_name)
    
    if not os.path.exists(model_results_path):
        print(f"❌ Error: Results directory not found at '{model_results_path}'")
        return

    all_metrics = []
    
    # --- Iterate over all result files ---
    for filename in sorted(os.listdir(model_results_path)):
        if not filename.endswith('.json'):
            if filename.endswith('.error'):
                print(f"⚠️ Skipping error file: {filename}")
            continue

        filepath = os.path.join(model_results_path, filename)
        with open(filepath, 'r') as f:
            try:
                data = json.load(f)
                # Only include results that have actual metrics
                if 'Standard-F1' in data: 
                    data['dataset'] = os.path.splitext(filename)[0]
                    all_metrics.append(data)
                elif 'Note' in data and data['Note'] == 'No anomalies in test set':
                    print(f"ℹ️ Skipping metric aggregation for '{filename}' (no anomalies in test set).")
            except json.JSONDecodeError:
                print(f"⚠️ Could not decode JSON from {filename}. Skipping.")
    
    if not all_metrics:
        print("\nNo valid metric files found to analyze.")
        return

    # --- Create and display a DataFrame ---
    df = pd.DataFrame(all_metrics)
    
    # Reorder columns for better readability
    metric_cols = ['Standard-F1', 'PA-F1', 'Event-based-F1', 'R-based-F1', 'AUC-ROC', 'AUC-PR', 'VUS_ROC', 'VUS_PR', 'Runtime']
    ordered_cols = ['dataset'] + [col for col in metric_cols if col in df.columns]
    df = df[ordered_cols]

    print("\n--- Individual Dataset Results ---")
    print(df.to_string(index=False))
    
    # --- Calculate and display average metrics ---
    # Convert metric columns to numeric, coercing errors
    for col in metric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            
    # Drop rows where key metrics might have failed conversion
    # Only consider columns that actually exist in the dataframe
    existing_metric_cols = [col for col in metric_cols if col in df.columns]
    df.dropna(subset=existing_metric_cols, how='any', inplace=True)

    avg_metrics = df[existing_metric_cols].mean().rename('Average')
    
    print("\n\n--- Average Performance Metrics ---")
    print(avg_metrics.to_string())
    print("\n")


if __name__ == '__main__':
    # You can change the model name here if you analyze another model's results
    analyze_results(model_name='HTA-AD-M') 