# -*- coding: utf-8 -*-
# calculate_model_complexity.py
#
# A script to systematically calculate and summarize the parameter count
# and estimated size of all anomaly detection models in the project.

import os
import sys
import torch
import pandas as pd
import glob
import importlib
import inspect
import warnings

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.base import BaseDetector

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

def count_parameters(model):
    """Counts the total and trainable parameters of a PyTorch model."""
    if not isinstance(model, torch.nn.Module):
        return 0, 0
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params

def get_model_from_detector(detector):
    """Attempts to find the torch.nn.Module from a detector instance."""
    if hasattr(detector, 'model') and isinstance(detector.model, torch.nn.Module):
        return detector.model
    if hasattr(detector, 'net') and isinstance(detector.net, torch.nn.Module):
        return detector.net
    if isinstance(detector, torch.nn.Module):
        return detector
    return None

def main():
    """
    Main function to scan, instantiate, and analyze all models.
    """
    results = []
    
    # --- Define dummy parameters for model instantiation ---
    DUMMY_INPUT_DIM = 5
    DUMMY_WINDOW_SIZE = 128
    DUMMY_HP = {
        'window_size': DUMMY_WINDOW_SIZE, 'win_size': DUMMY_WINDOW_SIZE, 'input_c': DUMMY_INPUT_DIM,
        'latent_dim': 16, 'input_dim': DUMMY_INPUT_DIM,
        'n_features': DUMMY_INPUT_DIM, 'in_features': DUMMY_INPUT_DIM, 'd_feat': DUMMY_INPUT_DIM,
        'feats': DUMMY_INPUT_DIM, 'feature_dim': DUMMY_INPUT_DIM, 'num_channel': [16, 16],
        'tcn_channels': [16, 16], 'cnn_channels': 16, 'downsample_stride': 2, 'kernel_size': 7,
        'lr': 1e-3, 'epochs': 1, 'batch_size': 32, 'gpu': 0, 'contamination': 0.1,
        'num_heads': 4, 'd_model': 64, 'n_layers': 2, 'top_k': 5, 'anomal_ratio': 0.5
    }

    # --- Find all model files ---
    model_paths = glob.glob('TSB_AD/models/**/*.py', recursive=True)
    model_paths += glob.glob('benchmark_exp/*.py', recursive=False)
    
    exclude_files = ['__init__.py', 'base.py', 'distance.py', 'feature.py', 'run_', 'calculate_', 'visualize_']
    model_paths = [p for p in model_paths if not any(ex in os.path.basename(p) for ex in exclude_files)]

    print(f"🔬 Found {len(model_paths)} potential model files to analyze...")

    # --- Loop through each model file ---
    for path in sorted(model_paths):
        try:
            module_path = path.replace('.py', '').replace(os.path.sep, '.')
            module = importlib.import_module(module_path)
            
            for attr_name in dir(module):
                if attr_name.startswith('__'): continue
                attr = getattr(module, attr_name)

                # Explicitly skip specified detectors
                if attr_name in ['CAAD_LMR_Detector', 'EnhancedCAAD_LMR_Detector','HTA_AD_M', 'LLM_MLP_AD']:
                    continue

                # Adjusted condition to also include AnomalyTransformer by name
                is_target_model = (inspect.isclass(attr) and issubclass(attr, BaseDetector) and attr is not BaseDetector) \
                                  or (attr_name == 'AnomalyTransformer')

                if is_target_model:
                    detector_class = attr
                    # Handle case where the class is not a BaseDetector descendant
                    is_base_detector_subclass = inspect.isclass(attr) and issubclass(attr, BaseDetector)

                    print(f"\nProcessing Model: {detector_class.__name__}")

                    try:
                        sig = inspect.signature(detector_class.__init__)
                        params = sig.parameters
                        init_params = {}

                        if 'HP' in params:
                            init_params['HP'] = DUMMY_HP
                        else:
                            for p_name in params:
                                if p_name in DUMMY_HP:
                                    init_params[p_name] = DUMMY_HP[p_name]
                        
                        detector = detector_class(**init_params)

                        # For BaseDetector subclasses, attempt lazy-init with fit
                        if is_base_detector_subclass and get_model_from_detector(detector) is None and hasattr(detector, 'fit'):
                            print(f"  ... Attempting lazy initialization via fit().")
                            dummy_train = torch.randn(DUMMY_WINDOW_SIZE * 2, DUMMY_INPUT_DIM).numpy()
                            try:
                                detector.fit(dummy_train)
                            except Exception:
                                pass # Ignore fit errors, just wanted to initialize
                        
                        # Special handling for AnomalyTransformer which is not a BaseDetector subclass
                        # and also requires fit() to be called to initialize its internal model.
                        elif not is_base_detector_subclass and detector_class.__name__ == 'AnomalyTransformer':
                             print(f"  ... Special handling for AnomalyTransformer: running fit() for initialization.")
                             dummy_train = torch.randn(DUMMY_WINDOW_SIZE * 2, DUMMY_INPUT_DIM).numpy()
                             try:
                                 # AnomalyTransformer expects num_epochs > 0 to build the model
                                 detector.num_epochs = 1
                                 detector.fit(dummy_train)
                             except Exception as fit_e:
                                 print(f"      - fit() failed for AnomalyTransformer: {fit_e}")
                                 pass # Ignore fit errors

                        torch_model = get_model_from_detector(detector)
                        
                        if torch_model:
                            total, trainable = count_parameters(torch_model)
                            size_mb = total * 4 / (1024 * 1024) if total > 0 else 0
                            results.append({
                                'Model': detector_class.__name__, 'Total Parameters': total,
                                'Trainable Parameters': trainable, 'Size (MB)': size_mb,
                                'Type': 'Deep Learning'
                            })
                            print(f"  ✅ Success. Params: {total:,}, Size: {size_mb:.3f} MB")
                        else:
                            results.append({
                                'Model': detector_class.__name__, 'Total Parameters': 0,
                                'Trainable Parameters': 0, 'Size (MB)': 0, 'Type': 'Non-Deep-Learning'
                            })
                            print(f"  ℹ️ Non-Deep-Learning model.")

                    except Exception as e:
                        results.append({'Model': detector_class.__name__, 'Type': f'Instantiation Error'})
                        print(f"  ❌ Error instantiating {detector_class.__name__}: {e}")
        
        except ImportError as e:
            print(f"\n- Skipping module {path} due to import error: {e}")
        except Exception as e:
            print(f"\n- Skipping module {path} due to unexpected error: {e}")

    # --- Clean, Display and Save Results ---
    if not results:
        print("\nNo models were successfully analyzed.")
        return

    summary_df = pd.DataFrame(results)
    summary_df.drop_duplicates(subset='Model', keep='first', inplace=True)
    summary_df = summary_df[~summary_df['Type'].str.contains('Error', na=False)]
    summary_df = summary_df.sort_values(by='Total Parameters', ascending=True).reset_index(drop=True)
    
    cols_order = ['Model', 'Total Parameters', 'Trainable Parameters', 'Size (MB)', 'Type']
    summary_df = summary_df[cols_order]
    
    print("\n\n" + "="*80)
    print(" " * 25 + "Model Complexity Summary (Cleaned)")
    print("="*80)
    # Format numbers for better readability in the final print
    summary_df['Total Parameters'] = summary_df['Total Parameters'].apply(lambda x: f"{x:,}")
    summary_df['Size (MB)'] = summary_df['Size (MB)'].apply(lambda x: f"{x:.4f}")
    print(summary_df.to_string())

    # Save to CSV with raw numbers
    output_path = 'model_complexity_summary.csv'
    df_to_save = pd.DataFrame(results)
    df_to_save.drop_duplicates(subset='Model', keep='first', inplace=True)
    df_to_save = df_to_save[~df_to_save['Type'].str.contains('Error', na=False)]
    df_to_save = df_to_save.sort_values(by='Total Parameters', ascending=True).reset_index(drop=True)
    df_to_save[cols_order].to_csv(output_path, index=False)
    print(f"\n\n✅ Cleaned summary saved to {output_path}")

if __name__ == '__main__':
    main() 