#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三模型重构对比实验：验证Transformer架构本身的时序局限性
HTA-AD vs 标准Transformer vs AnomalyTransformer

学术目标：
1. 证明标准Transformer自编码器在时序重构上的局限性
2. 显示AnomalyTransformer的复杂机制仍无法解决核心问题  
3. 展示HTA-AD通过时序归纳偏置的优越性
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import sys
import os
import torch
import torch.nn as nn
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from tqdm import tqdm
from scipy.spatial import ConvexHull

# --- Path Setup ---
# 确保可以导入 TSB_AD 模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_Model as HTA_AD_Model
from TSB_AD.models.AnomalyTransformer import AnomalyTransformerModel
from TSB_AD.models.StandardTransformerAE import StandardTransformerAutoencoder

# --- Helper Functions ---

def get_reconstruction(model, loader, device, model_name):
    """通用函数，用于获取模型对测试数据的重构结果"""
    model.eval()
    reconstructions = []
    originals = []
    with torch.no_grad():
        for batch_x, _ in tqdm(loader, desc=f"Getting Reconstructions for {model_name}", leave=False):
            batch_x = batch_x.to(device)
            
            if model_name == 'AnomalyTransformer':
                recons, _, _, _ = model(batch_x)
            else:
                recons, _ = model(batch_x)

            reconstructions.append(recons.cpu().numpy())
            originals.append(batch_x.cpu().numpy())
    
    # 将批次拼接成完整序列
    reconstructions = np.concatenate([r for r in reconstructions], axis=0)
    originals = np.concatenate([o for o in originals], axis=0)
    return originals, reconstructions


def get_latent_space(model, loader, device, model_name):
    """通用函数，用于提取模型的潜在空间表示"""
    model.eval()
    latents = []
    with torch.no_grad():
        for batch_x, _ in tqdm(loader, desc=f"Extracting Latent Space for {model_name}", leave=False):
            batch_x = batch_x.to(device)

            if model_name == 'AnomalyTransformer':
                # AnomalyTransformer需要特殊处理以获取编码器输出作为潜空间
                embedded_x = model.embedding(batch_x)
                latent, _, _, _ = model.encoder(embedded_x)
            else:
                # 其他模型遵循 (reconstruction, latent) 的输出格式
                _, latent = model(batch_x)
            
            # 潜空间维度可能是 (batch, seq_len, features)，在序列长度上取平均以获得单个向量
            if latent.ndim == 3:
                latent = latent.mean(1)

            latents.append(latent.cpu().numpy())
    
    return np.vstack(latents)


def my_kl_loss(p, q):
    """AnomalyTransformer中用于计算关联差异的KL散度损失"""
    res = p * (torch.log(p + 1e-4) - torch.log(q + 1e-4))
    return torch.mean(torch.sum(res, dim=-1), dim=1)


def train_model(name, model, train_loader, optimizer, criterion, device, num_epochs, win_size):
    """通用模型训练函数"""
    print(f"--- [START] Training {name} ---")
    
    # AnomalyTransformer的超参数k
    k_anomaly_transformer = 3.0

    for epoch in range(num_epochs):
        model.train()
        total_loss = 0
        loop = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}", leave=False)
        for batch_x, _ in loop:
            batch_x = batch_x[0] # Dataloader wraps it in a list
            batch_x = batch_x.to(device)
            optimizer.zero_grad()
            
            if name == 'AnomalyTransformer':
                # AnomalyTransformer的特殊Minimax训练策略
                reconstruction, series, prior, _ = model(batch_x)
                loss_recon = criterion(reconstruction, batch_x)

                # 计算关联差异损失 (Association Discrepancy)
                series_loss = 0.0
                prior_loss = 0.0
                for u in range(len(series)):
                    series_loss += (torch.mean(my_kl_loss(series[u], (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1, win_size)).detach())) + 
                                    torch.mean(my_kl_loss((prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1, win_size)).detach(), series[u])))
                    prior_loss += (torch.mean(my_kl_loss((prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1, win_size)), series[u].detach())) + 
                                   torch.mean(my_kl_loss(series[u].detach(), (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1, win_size)))))
                
                series_loss = series_loss / len(series)
                prior_loss = prior_loss / len(series)

                # Minimax策略的反向传播
                loss1 = loss_recon - k_anomaly_transformer * series_loss
                loss2 = loss_recon + k_anomaly_transformer * prior_loss

                loss1.backward(retain_graph=True)
                loss2.backward()
                
                loss_for_print = loss1 # 仅用于打印
            else:
                # 标准模型的训练
                reconstruction, _ = model(batch_x)
                loss_for_print = criterion(reconstruction, batch_x)
                loss_for_print.backward()

            optimizer.step()
            total_loss += loss_for_print.item()
        
        if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}, Train Loss: {total_loss/len(train_loader):.6f}")
    print(f"--- [SUCCESS] Finished training {name} ---")


def create_publication_quality_reconstruction_plot(dataset_name, original, reconstructions, mses):
    """创建达到出版物质量的2x2重构对比图"""
    COLORS = {'Original': '#1f77b4', 'HTA-AD (Ours)': '#2ca02c', 'Standard Transformer': '#ff7f0e', 'AnomalyTransformer': '#d62728'}
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=("<b>(a) Original Signal Segment</b>", "<b>(b) Model Reconstructions</b>", "<b>(c) Reconstruction Error (MSE)</b>", "<b>(d) Performance Summary</b>"),
        specs=[[{}, {}], [{}, {"type": "table"}]],
        vertical_spacing=0.25, horizontal_spacing=0.15,
        column_widths=[0.6, 0.4]
    )
    time_steps = np.arange(len(original))

    fig.add_trace(go.Scatter(x=time_steps, y=original, name='Original Signal', line=dict(color=COLORS['Original'], width=2.5)), row=1, col=1)
    
    fig.add_trace(go.Scatter(x=time_steps, y=original, name='Original', showlegend=False, line=dict(color=COLORS['Original'], width=1.5, dash='dot'), opacity=0.6), row=1, col=2)
    for name, recon in reconstructions.items():
        fig.add_trace(go.Scatter(x=time_steps, y=recon, name=name, line=dict(color=COLORS[name], width=2)), row=1, col=2)

    for name, recon in reconstructions.items():
        error = (original - recon)**2
        fig.add_trace(go.Scatter(x=time_steps, y=error, name=f'{name} Error', line=dict(color=COLORS[name]), showlegend=False), row=2, col=1)

    st_relative = f'{mses.get("Standard Transformer", 0)/mses.get("HTA-AD (Ours)", 1e-9):.2f}×'
    at_relative = f'{mses.get("AnomalyTransformer", 0)/mses.get("HTA-AD (Ours)", 1e-9):.2f}×'
    
    perf_values = [
        ['🏆 HTA-AD (Ours)', '🤖 Standard Transformer', '🚨 AnomalyTransformer'], 
        [f'<b>{mses.get("HTA-AD (Ours)", 0):.4f}</b>', f'{mses.get("Standard Transformer", 0):.4f}', f'{mses.get("AnomalyTransformer", 0):.4f}'],
        ['<b>1.00×</b> (Baseline)', st_relative, at_relative]
    ]
    fig.add_trace(go.Table(
        header=dict(values=['<b>Model</b>', '<b>Total MSE</b>', '<b>Relative Degradation</b>'], fill_color='#2c3e50', font=dict(color='white', size=14), align='left', height=40),
        cells=dict(values=perf_values, fill_color=['#dff0d8', '#fcf8e3', '#f2dede'] * 3, align='left', height=35, font_size=13)
    ), row=2, col=2)

    fig.update_layout(
        title_text=f"<b>Architectural Showdown: The Failure of Permutation-Invariant Transformers in TSAD</b><br><sub>Dataset: {dataset_name}</sub>",
        title_x=0.5, font=dict(family="Computer Modern, serif", size=16),
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        margin=dict(l=80, r=40, b=80, t=140), plot_bgcolor='rgba(245, 245, 245, 1)'
    )
    fig.update_yaxes(title_text="Value", row=1, col=1); fig.update_yaxes(title_text="Value", row=1, col=2)
    fig.update_yaxes(title_text="Squared Error", row=2, col=1, type="log")
    for row in [1, 2]:
        for col in [1, 2]:
            fig.update_xaxes(showline=True, linewidth=1, linecolor='black', mirror=True, row=row, col=col)
            fig.update_yaxes(showline=True, linewidth=1, linecolor='black', mirror=True, row=row, col=col)
    fig.update_xaxes(title_text="Time Step", row=2, col=1)
    return fig


def create_publication_quality_latent_space_plot(dataset_name, latent_spaces):
    """创建达到出版物质量的潜空间对比图"""
    COLORS = {'HTA-AD (Ours)': '#2ca02c', 'Standard Transformer': '#ff7f0e', 'AnomalyTransformer': '#d62728'}
    
    fig = make_subplots(rows=1, cols=len(latent_spaces), subplot_titles=[f"<b>{name}</b>" for name in latent_spaces.keys()], shared_yaxes=True)

    for i, (name, latent_2d) in enumerate(latent_spaces.items()):
        col = i + 1
        fig.add_trace(go.Scatter(
            x=latent_2d[:, 0], y=latent_2d[:, 1], mode='markers', name=name, 
            marker=dict(color=COLORS.get(name), size=5, opacity=0.7)
        ), row=1, col=col)

        if name == 'HTA-AD (Ours)':
            try:
                hull = ConvexHull(latent_2d)
                fig.add_trace(go.Scatter(
                    x=np.append(latent_2d[hull.vertices, 0], latent_2d[hull.vertices[0], 0]), 
                    y=np.append(latent_2d[hull.vertices, 1], latent_2d[hull.vertices[0], 1]),
                    fill="toself", fillcolor=f'rgba(44, 160, 44, 0.1)',
                    line=dict(color=f'rgba(44, 160, 44, 0.5)', width=2, dash='dash'),
                    mode='lines', name='Learned Manifold', showlegend=False
                ), row=1, col=col)
            except Exception as e:
                print(f"Could not compute convex hull for HTA-AD: {e}")

    fig.update_layout(
        title_text=f"<b>Latent Space Comparison (t-SNE): Revealing Architectural Inductive Bias</b><br><sub>Dataset: {dataset_name}</sub>",
        title_x=0.5, font=dict(family="Computer Modern, serif", size=16), 
        showlegend=False, margin=dict(l=40, r=40, b=80, t=140),
        yaxis_title="t-SNE Dimension 2", plot_bgcolor='rgba(245, 245, 245, 1)'
    )
    for i in range(1, len(latent_spaces) + 1): 
        fig.update_xaxes(title_text="t-SNE Dimension 1", row=1, col=i)
        fig.update_xaxes(showline=True, linewidth=1, linecolor='black', mirror=True, row=1, col=i)
        fig.update_yaxes(showline=True, linewidth=1, linecolor='black', mirror=True, row=1, col=i)
    return fig


def main():
    """主执行函数"""
    # --- Academic Framing ---
    print("🚀 Starting Three-Model Reconstruction Comparison")
    print("=" * 70)
    print("Academic Goal: Prove Transformer architecture limitations for time series")
    print("=" * 70)
    
    # --- Configuration ---
    DATASET_NAME = '001_NAB_id_1_Facility_tr_1007_1st_2014.csv'
    DATA_PATH = os.path.join(project_root, 'Datasets', 'TSB-AD-U', 'TSB-AD-U', DATASET_NAME)
    
    WIN_SIZE = 100
    BATCH_SIZE = 64
    NUM_EPOCHS = 15
    TRAIN_TEST_SPLIT = 0.7

    # --- Setup ---
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"⚙️ Using device: {device}")

    # --- Data Loading and Preprocessing ---
    if not os.path.exists(DATA_PATH):
        print(f"❌ Dataset not found at {DATA_PATH}. Please check the path.")
        return
    
    df = pd.read_csv(DATA_PATH)
    data = df['value'].values.astype(np.float32).reshape(-1, 1)

    train_size = int(len(data) * TRAIN_TEST_SPLIT)
    train_data, test_data = data[:train_size], data[train_size:]

    scaler = StandardScaler()
    train_data_scaled = scaler.fit_transform(train_data)
    
    # Create sliding windows
    def create_sequences(input_data, seq_length):
        seqs = []
        for i in range(len(input_data) - seq_length + 1):
            seqs.append(input_data[i:i+seq_length])
        return torch.from_numpy(np.array(seqs)).float()

    train_sequences = create_sequences(train_data_scaled, WIN_SIZE)
    
    # Create test loader with original and scaled data
    test_sequences_scaled = create_sequences(scaler.transform(test_data), WIN_SIZE)
    original_test_sequences = create_sequences(test_data, WIN_SIZE)

    train_loader = torch.utils.data.DataLoader(train_sequences, batch_size=BATCH_SIZE, shuffle=True)
    test_loader = torch.utils.data.DataLoader(torch.utils.data.TensorDataset(test_sequences_scaled, original_test_sequences), batch_size=BATCH_SIZE, shuffle=False)

    # --- Model Initialization ---
    feat_dim = train_sequences.shape[2]
    models = {
        'HTA-AD (Ours)': HTA_AD_Model(feat_dim=feat_dim, win_size=WIN_SIZE, latent_dim=32),
        'Standard Transformer': StandardTransformerAutoencoder(feat_dim=feat_dim, win_size=WIN_SIZE, d_model=64, nhead=4, num_layers=2, latent_dim=32),
        'AnomalyTransformer': AnomalyTransformerModel(win_size=WIN_SIZE, enc_in=feat_dim, c_out=feat_dim, d_model=64, n_heads=4, e_layers=2)
    }
    
    reconstructions = {}
    latent_representations = {}
    final_mses = {}

    # --- Main Loop: Training and Evaluation ---
    for name, model in models.items():
        try:
            model.to(device)
            optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
            criterion = nn.MSELoss()
            
            # Use a simple dataloader for training that doesn't yield labels
            train_loader_simple = torch.utils.data.DataLoader(train_sequences, batch_size=BATCH_SIZE, shuffle=True)
            train_model(name, model, train_loader_simple, optimizer, criterion, device, NUM_EPOCHS, WIN_SIZE)
            
            # Get reconstructions from scaled test data
            test_loader_scaled_only = torch.utils.data.DataLoader(test_sequences_scaled, batch_size=BATCH_SIZE, shuffle=False)
            _, recons_scaled = get_reconstruction(model, test_loader_scaled_only, device, name)
            
            # Inverse transform to get true reconstruction values
            recons_unscaled = scaler.inverse_transform(recons_scaled.reshape(-1, feat_dim)).reshape(recons_scaled.shape)
            reconstructions[name] = recons_unscaled
            
            final_mses[name] = np.mean((original_test_sequences.numpy() - reconstructions[name])**2)
            
            # Get latent space
            latent_representations[name] = get_latent_space(model, test_loader_scaled_only, device, name)
            
        except Exception as e:
            print(f"--- [ERROR] Failed to process {name} ---")
            import traceback
            traceback.print_exc()

    # --- Visualization ---
    if not reconstructions:
        print("🚨 No models were successfully processed. Exiting.")
        return

    print(f"🔍 Final MSEs: {final_mses}")
    
    # Prepare data for plotting
    vis_len = 4000
    vis_orig = original_test_sequences.numpy().flatten()[:vis_len]
    vis_recons = {name: recon.flatten()[:vis_len] for name, recon in reconstructions.items()}

    # Create and save plots
    output_dir = os.path.join(project_root, 'visualizations', 'three_model_comparison')
    os.makedirs(output_dir, exist_ok=True)

    reconstruction_fig = create_publication_quality_reconstruction_plot(DATASET_NAME, vis_orig, vis_recons, final_mses)
    recon_path = os.path.join(output_dir, 'three_model_reconstruction_comparison_final')
    reconstruction_fig.write_html(recon_path + '.html')
    reconstruction_fig.write_image(recon_path + '.png', width=1800, height=1200)
    print(f"✅ Publication-quality reconstruction comparison saved to {recon_path}.[html/png]")

    valid_latents = {k: v for k, v in latent_representations.items() if v is not None and v.shape[0] > 1}
    if len(valid_latents) > 1:
        tsne_results = {}
        perplexity = min(30, min(rep.shape[0] for rep in valid_latents.values()) - 1)

        for name, latent in valid_latents.items():
            print(f"Running t-SNE for {name}...")
            tsne = TSNE(n_components=2, random_state=42, perplexity=perplexity, n_iter=300)
            tsne_results[name] = tsne.fit_transform(latent)
        
        latent_fig = create_publication_quality_latent_space_plot(DATASET_NAME, tsne_results)
        latent_path = os.path.join(output_dir, 'latent_space_comparison_final')
        latent_fig.write_html(latent_path + '.html')
        latent_fig.write_image(latent_path + '.png', width=1800, height=700)
        print(f"✅ Publication-quality latent space comparison saved to {latent_path}.[html/png]")
    else:
        print("⚠️ Latent space visualization skipped (not enough models succeeded).")

    print("\n" + "=" * 70)
    print("🎉 Three-model comparison completed!")

if __name__ == "__main__":
    main()