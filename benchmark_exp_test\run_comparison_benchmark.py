# -*- coding: utf-8 -*-
# Comprehensive Benchmark Comparison Script
# This script runs a selection of models against a random subset of datasets
# to provide a comparative performance analysis.

import os
import pandas as pd
import numpy as np
import time
import sys
import random

# --- Path Setup & Selective Imports ---
# To avoid importing broken modules, we import dependencies directly.
try:
    from benchmark_exp.Lightweight_TS_Model import LTS_AD
    from benchmark_exp.attn_tcn_vae import AttnTcnVAE_AD
    # We will need the model pools for the dispatcher, but we can't import the
    # wrappers directly as they import the broken LERN detector.
    # We will redefine the pools locally.
    from TSB_AD.evaluation.metrics import get_metrics
    from TSB_AD.utils.slidingWindows import find_length_rank
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    print(f"✅ Added project root to path: {project_root}")
    from benchmark_exp.Lightweight_TS_Model import LTS_AD
    from benchmark_exp.attn_tcn_vae import AttnTcnVAE_AD
    from TSB_AD.evaluation.metrics import get_metrics
    from TSB_AD.utils.slidingWindows import find_length_rank

# Re-defining pools locally to avoid importing the broken wrapper
# NOTE: This is a temporary workaround. For a permanent fix, LERN detector should be fixed.
Unsupervise_AD_Pool_Local = ['FFT', 'SR', 'NORMA', 'Series2Graph', 'Sub_IForest', 'IForest', 'LOF', 'Sub_LOF', 'POLY', 'MatrixProfile', 'Sub_PCA', 'PCA', 'HBOS', 
                             'Sub_HBOS', 'KNN', 'Sub_KNN','KMeansAD', 'KMeansAD_U', 'KShapeAD', 'COPOD', 'CBLOF', 'COF', 'EIF', 'RobustPCA']
Semisupervise_AD_Pool_Local = ['Left_STAMPi', 'SAND', 'MCD', 'Sub_MCD', 'OCSVM', 'Sub_OCSVM', 'AutoEncoder', 'CNN', 'LSTMAD', 'TranAD', 'USAD', 'OmniAnomaly', 
                               'AnomalyTransformer', 'TimesNet', 'FITS', 'Donut', 'OFA']

# We must also re-import the runners for the models we want to use
from TSB_AD.model_wrapper import run_Unsupervise_AD, run_Semisupervise_AD


def run_comparison_benchmark():
    """
    Runs a comparative benchmark for a specified list of models on a random
    subset of datasets and reports the average performance.
    """
    # --- Experiment Configuration ---
    DATA_DIRECTORY = './Datasets/TSB-AD-U/'
    RESULTS_DIR = './benchmark_results_comparison/'
    NUM_DATASETS_TO_SAMPLE = 25
    RANDOM_SEED = 42  # for reproducibility
    os.makedirs(RESULTS_DIR, exist_ok=True)

    # --- Model Selection ---
    # Our custom models + 10 baselines from the pools. LERN is excluded.
    MODELS_TO_TEST = [
        'Attn-TCN-VAE',
        'LTS_AD',
        'IForest',
        'LOF',
        'PCA',
        'MatrixProfile',
        'COPOD',
        'ECOD', # Assuming ECOD is available
        'AutoEncoder',
        'TranAD',
        'USAD',
        'AnomalyTransformer'
    ]
    
    # --- Hyperparameter Definitions ---
    # Using GPU 1 for our custom models as requested.
    ATTN_TCN_VAE_HP = {
        'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64,
        'latent_dim': 32, 'beta': 1.0, 'tcn_layers': 4, 'tcn_filters': 64,
        'tcn_kernel_size': 3, 'tcn_dropout': 0.2, 'gpu': 1
    }
    LTS_AD_HP = {
        'window_size': 100, 'epochs': 30, 'lr': 1e-3, 'batch_size': 128,
        'latent_dim': 16, 'gpu': 1
    }

    # --- Dataset Selection ---
    try:
        all_files = sorted([f for f in os.listdir(DATA_DIRECTORY) if f.endswith('.csv') and not f.startswith('.')])
        if len(all_files) < NUM_DATASETS_TO_SAMPLE:
             print(f"⚠️ Warning: Requested {NUM_DATASETS_TO_SAMPLE} datasets, but only found {len(all_files)}. Using all available.")
             sampled_files = all_files
        else:
             random.seed(RANDOM_SEED)
             sampled_files = random.sample(all_files, NUM_DATASETS_TO_SAMPLE)
    except FileNotFoundError:
        print(f"❌ Error: Data directory not found at '{DATA_DIRECTORY}'")
        return

    # --- Main Benchmark Loop ---
    all_results = []
    print(f"🚀 Starting benchmark comparison on {len(sampled_files)} datasets for {len(MODELS_TO_TEST)} models...")

    for model_name in MODELS_TO_TEST:
        print(f"\n{'='*20} Testing Model: {model_name} {'='*20}")
        model_start_time = time.time()
        
        for i, filename in enumerate(sampled_files):
            print(f"  [{i+1}/{len(sampled_files)}] Processing: {filename}")
            
            try:
                filepath = os.path.join(DATA_DIRECTORY, filename)
                df = pd.read_csv(filepath).dropna()
                
                if df.shape[1] < 2: continue

                data = df.iloc[:, 0:-1].values.astype(float)
                label = df.iloc[:, -1].astype(int).to_numpy()

                train_index_str = filename.split('.')[0].split('_')[-3]
                train_size = int(train_index_str)
                if train_size >= len(data): continue
                data_train = data[:train_size]

                # --- Model Dispatcher ---
                start_time = time.time()
                scores = None

                if model_name == 'Attn-TCN-VAE':
                    clf = AttnTcnVAE_AD(HP=ATTN_TCN_VAE_HP)
                    clf.fit(data_train)
                    scores = clf.decision_function(data)
                elif model_name == 'LTS_AD':
                    clf = LTS_AD(HP=LTS_AD_HP)
                    clf.fit(data_train)
                    scores = clf.decision_function(data)
                elif model_name in Unsupervise_AD_Pool_Local:
                    scores = run_Unsupervise_AD(model_name, data)
                elif model_name in Semisupervise_AD_Pool_Local:
                    scores = run_Semisupervise_AD(model_name, data_train, data)
                else:
                    print(f"    SKIPPING: Model '{model_name}' not found in any defined pool.")
                    continue
                
                runtime = time.time() - start_time
                if isinstance(scores, str): # Error occurred
                    raise Exception(scores)

                # --- Evaluate ---
                min_len = min(len(scores), len(label))
                scores, label_sliced = scores[:min_len], label[:min_len]
                
                slidingWindow = find_length_rank(data[:len(label_sliced)].flatten().reshape(-1, 1), rank=1)
                metrics = get_metrics(scores, label_sliced, slidingWindow=slidingWindow)
                
                result_row = {'model': model_name, 'dataset': filename, 'runtime(s)': round(runtime, 2), **metrics}
                all_results.append(result_row)
                print(f"    ✅ Completed. F1-Score: {metrics.get('Standard-F1', 'N/A'):.4f}, Runtime: {runtime:.2f}s")

            except Exception as e:
                print(f"    ❌ Error processing file: {e}")
                all_results.append({'model': model_name, 'dataset': filename, 'runtime(s)': 0, 'error': str(e)})

        print(f"--- Model {model_name} finished in {time.time() - model_start_time:.2f}s ---")


    # --- Report and Save Results ---
    if not all_results:
        print("\nNo datasets were successfully processed.")
        return
        
    results_df = pd.DataFrame(all_results)
    
    # Calculate average metrics per model
    numeric_cols = results_df.select_dtypes(include=np.number).columns
    summary_df = results_df.groupby('model')[numeric_cols].mean()
    
    print("\n\n{'='*25} BENCHMARK SUMMARY {'='*25}")
    print("\n--- Detailed Results per Dataset ---")
    print(results_df.to_string())
    
    print("\n--- Average Performance Across All Datasets ---")
    print(summary_df.to_string())
    
    detailed_path = os.path.join(RESULTS_DIR, 'benchmark_comparison_detailed.csv')
    summary_path = os.path.join(RESULTS_DIR, 'benchmark_comparison_summary.csv')
    
    results_df.to_csv(detailed_path, index=False)
    summary_df.to_csv(summary_path, index=True)
    
    print(f"\n💾 Detailed results saved to: {detailed_path}")
    print(f"💾 Summary report saved to: {summary_path}")
    print("\n🎉🎉🎉 Benchmark comparison finished successfully! 🎉🎉🎉")

if __name__ == '__main__':
    run_comparison_benchmark() 