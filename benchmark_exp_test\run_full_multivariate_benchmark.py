# -*- coding: utf-8 -*-
# Full-scale MULTIVARIATE benchmark script
# This script runs a selection of custom models on all datasets in the TSB-AD-M dataset.
# It's designed to be run from the command line, e.g., in the background using nohup.

import os
import sys
import pandas as pd
import numpy as np
import json
from tqdm import tqdm
import torch

# --- Environment Setup ---
# Add project root to sys.path to allow imports from TSB_AD
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    
# --- TSB-AD Imports ---
from TSB_AD.utils.dataset import TSB_M_Dataset
from TSB_AD.utils.metrics import F1_score, compute_delay
from TSB_AD.eval_methods.simple_best_f1 import find_best_f1_threshold

# --- Model Imports ---
# Import the custom models from the benchmark_exp directory
# NOTE: Lightweight_TS_Model is the univariate version. We need the multivariate one.
from benchmark_exp.Lightweight_TS_Model_M import LTS_M_AD as Lightweight_TS_Model_M_AD
from benchmark_exp.gru_tcn_ae import GruTcnAe_AD
from benchmark_exp.gru_tcn_vae import GruTcnVae_AD
from benchmark_exp.attn_tcn_vae import AttnTcnVae_AD
from benchmark_exp.temporal_attn_tcn_vae import TemporalAttnTcnVae_AD

# --- Configuration ---
# Determine which GPU to use
GPU_ID = int(sys.argv[1]) if len(sys.argv) > 1 else 0
DEVICE = f"cuda:{GPU_ID}" if torch.cuda.is_available() else "cpu"
print(f"--- Running Multivariate Benchmark on {DEVICE} ---")

# Path to the multivariate dataset
DATASET_PATH = os.path.join(project_root, "Datasets/TSB-AD-M")

# Directory to save results
RESULTS_DIR = os.path.join(project_root, f"benchmark_results/multivariate_custom_gpu{GPU_ID}")
os.makedirs(RESULTS_DIR, exist_ok=True)

# Define the models to be benchmarked
# Each entry is a tuple: (ModelClass, model_name, hyperparameter_dict)
MODELS_TO_BENCHMARK = [
    (Lightweight_TS_Model_M_AD, "Lightweight_TS_Model_M", {'window_size': 128, 'gpu': GPU_ID}),
    (GruTcnAe_AD, "GRU_TCN_AE", {'window_size': 128, 'epochs': 30, 'gpu': GPU_ID}),
    (GruTcnVae_AD, "GRU_TCN_VAE", {'window_size': 128, 'epochs': 30, 'gpu': GPU_ID}),
    (AttnTcnVae_AD, "Attn_TCN_VAE", {'window_size': 128, 'epochs': 30, 'gpu': GPU_ID}),
    (TemporalAttnTcnVae_AD, "TemporalAttn_TCN_VAE", {'window_size': 128, 'epochs': 30, 'gpu': GPU_ID}),
]

# --- Main Benchmark Loop ---
def run_benchmark():
    """
    Iterates through all datasets and all specified models, runs the evaluation,
    and saves the results to a CSV file.
    """
    tsb_dataset = TSB_M_Dataset(DATASET_PATH)
    all_results = []
    
    # Use tqdm for progress bars
    dataset_iterator = tqdm(tsb_dataset, desc="Total Progress")

    for i, (X_train, y_train, X_test, y_test) in enumerate(dataset_iterator):
        dataset_name = tsb_dataset.files[i]
        dataset_iterator.set_description(f"Processing {dataset_name}")
        
        # Add a progress bar for models
        model_iterator = tqdm(MODELS_TO_BENCHMARK, desc="Models", leave=False)
        for model_class, model_name, hp in model_iterator:
            model_iterator.set_description(f"Running {model_name}")
            
            try:
                # 1. Initialize and train the model
                model = model_class(HP=hp)
                model.fit(X_train)
                
                # 2. Get anomaly scores on the test set
                anomaly_scores = model.decision_function(X_test)
                
                # Handle cases with no variance in scores
                if anomaly_scores.min() == anomaly_scores.max():
                    print(f"⚠️ Warning: All anomaly scores are identical for {model_name} on {dataset_name}. Setting F1 to 0.")
                    best_f1, best_th, delay = 0.0, 0.0, 0.0
                else:
                    # 3. Find the best F1 score
                    best_f1, best_th, _ = find_best_f1_threshold(anomaly_scores, y_test, n_thresholds=100)
                    
                    # 4. Compute delay
                    pred = (anomaly_scores > best_th).astype(int)
                    delay = compute_delay(pred, y_test)

                # 5. Store results
                result = {
                    "dataset": dataset_name,
                    "model": model_name,
                    "f1": best_f1,
                    "threshold": best_th,
                    "delay": delay,
                    "hyperparameters": json.dumps(hp)
                }
                all_results.append(result)
                print(f"   -> Result for {model_name} on {dataset_name}: F1={best_f1:.4f}, Delay={delay:.4f}")

            except Exception as e:
                print(f"❌ ERROR running {model_name} on {dataset_name}: {e}")
                # Store error result
                result = {
                    "dataset": dataset_name,
                    "model": model_name,
                    "f1": 0.0,
                    "threshold": 0.0,
                    "delay": 0.0,
                    "hyperparameters": f"ERROR: {e}"
                }
                all_results.append(result)

            # --- Save results incrementally ---
            # This ensures that even if the script crashes, we have partial results.
            df_results = pd.DataFrame(all_results)
            results_path = os.path.join(RESULTS_DIR, "multivariate_benchmark_results.csv")
            df_results.to_csv(results_path, index=False)
            
    print("\n--- ✅ Multivariate Benchmark Complete ---")
    print(f"Results saved to {results_path}")

if __name__ == "__main__":
    run_benchmark() 