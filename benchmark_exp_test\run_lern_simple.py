#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LERN模型简单批量测试脚本 - 使用os.system确保实时显示
"""

import os
import time
import glob
import argparse

def main():
    parser = argparse.ArgumentParser(description='LERN简单批量测试')
    parser.add_argument('--data_direc', type=str, default='../Datasets/TSB-AD-U/')
    parser.add_argument('--max_files', type=int, default=5, help='最大测试文件数')
    parser.add_argument('--start_from', type=int, default=0, help='从第几个文件开始')
    args = parser.parse_args()
    
    print("🚀 LERN模型简单批量测试启动")
    print("="*80)
    
    # 检查数据集
    if not os.path.exists(args.data_direc):
        print(f"❌ 数据集目录不存在: {args.data_direc}")
        return
    
    # 获取文件列表
    csv_files = glob.glob(os.path.join(args.data_direc, "*.csv"))
    csv_files = [os.path.basename(f) for f in csv_files]
    csv_files = sorted(csv_files)
    
    print(f"📂 找到 {len(csv_files)} 个数据集文件")
    
    # 选择测试文件
    end_idx = min(args.start_from + args.max_files, len(csv_files))
    test_files = csv_files[args.start_from:end_idx]
    
    print(f"🧪 将测试文件 {args.start_from+1}-{end_idx} (共{len(test_files)}个)")
    print(f"⚡ 使用os.system - 完整显示训练过程")
    
    total_start = time.time()
    success_count = 0
    
    for i, filename in enumerate(test_files, 1):
        print(f"\n" + "="*80)
        print(f"📊 [{args.start_from+i}/{len(csv_files)}] 处理文件: {filename}")
        print("="*80)
        
        # 构建命令
        cmd = f'python Run_LERN_Detector.py --filename "{filename}" --data_direc "{args.data_direc}" --AD_Name LERN_AD'
        
        print(f"🔧 运行命令: {cmd}")
        
        # 记录开始时间
        start_time = time.time()
        
        # 使用os.system运行命令 - 这会完整显示所有输出
        return_code = os.system(cmd)
        
        end_time = time.time()
        runtime = end_time - start_time
        
        if return_code == 0:
            success_count += 1
            print(f"\n✅ 成功处理: {filename} (用时: {runtime:.1f}s)")
        else:
            print(f"\n❌ 处理失败: {filename} (返回码: {return_code}, 用时: {runtime:.1f}s)")
    
    total_end = time.time()
    total_time = total_end - total_start
    
    # 打印汇总
    print(f"\n" + "="*80)
    print(f"🎉 简单批量测试完成!")
    print("="*80)
    print(f"📊 总文件数: {len(test_files)}")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {len(test_files) - success_count}")
    print(f"⏱️ 总时间: {total_time:.2f}s")
    print(f"📈 平均每文件: {total_time/len(test_files):.1f}s")
    
    if end_idx < len(csv_files):
        print(f"\n💡 继续测试更多文件:")
        print(f"   python run_lern_simple.py --start_from {end_idx} --max_files {args.max_files}")

if __name__ == "__main__":
    main() 