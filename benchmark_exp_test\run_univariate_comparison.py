# -*- coding: utf-8 -*-
# run_univariate_comparison.py
#
# A script to run a focused comparison on UNIVARIATE datasets.
# This pits our best multivariate model (LERN) against native univariate models.

import os
import sys
import pandas as pd
import random
import time
from tqdm import tqdm
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import precision_recall_curve


# --- <PERSON><PERSON><PERSON><PERSON><PERSON> and Seaborn Setup ---
try:
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")
    # A simple check for a common Chinese font
    if 'SimHei' in [f.name for f in plt.matplotlib.font_manager.fontManager.ttflist]:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        print("🎨 使用中文字体: SimHei")
except Exception:
    print("🎨 字体设置失败，将使用默认字体")


# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- TSB-AD Imports ---
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.evaluation.metrics import get_metrics
# from TSB_AD.models.CNN import CNN # No longer needed

# --- User Model Imports ---
from benchmark_exp.lern import LERN_AD
from benchmark_exp.Lightweight_TS_Model import LTS_AD

# --- Custom Univariate Data Loader ---
class UnivariateDataset:
    def __init__(self, dataset_path):
        df = pd.read_csv(dataset_path).dropna()
        try:
            train_size_str = os.path.basename(dataset_path).split('_')[-3]
            self.train_size = int(train_size_str)
        except (ValueError, IndexError):
            self.train_size = int(len(df) * 0.7)

        self.data = df.iloc[:, 0:-1].values.astype(float)
        self.label = df.iloc[:, -1].astype(int).to_numpy()
        self.train = self.data[:self.train_size]
        self.name = os.path.basename(dataset_path)

# --- Configuration ---
DATASET_ROOT = 'Datasets/TSB-AD-U/'
# NUM_DATASETS_TO_TEST = 10 # Run on all datasets for the final showdown
# Define a new root directory for the detailed showdown results
SHOWDOWN_OUTPUT_DIR = 'LERN_vs_LTS_AD_Showdown_Results/'

# --- Model Definitions ---
MODELS = {
    'LERN': {
        'class': LERN_AD,
        'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64, 'latent_dim': 16, 'tcn_channels': [16,16], 'downsample_stride': 4, 'gpu': 0},
        'type': 'user'
    },
    'LTS_AD': {
        'class': LTS_AD,
        'params': {'window_size': 100, 'epochs': 30, 'lr': 1e-3, 'batch_size': 128, 'latent_dim': 16, 'gpu': 0},
        'type': 'user'
    },
    # 'LAD': {
    #     'class': LAD_AD,
    #     'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 128, 'decomp_kernel_size': 25, 'gpu': 0},
    #     'type': 'user'
    # },
    # 'CNN': {
    #     'class': CNN,
    #     'params': {'window_size': 100, 'num_channel': [16, 16, 20], 'lr': 0.0008, 'feats': 1},
    #     'type': 'baseline'
    # }
}

# --- Visualization Function (adapted from LTS_AD) ---
def create_visualizations(filename, data, label, output, train_size, save_dir, model_name):
    """
    Creates a multi-panel visualization to show detection results and anomaly scores.
    """
    os.makedirs(save_dir, exist_ok=True)
    try:
        df = pd.DataFrame({
            'value': data.flatten(),
            'score': output,
            'label': label
        })
        
        precision, recall, thresholds = precision_recall_curve(df['label'], df['score'])
        f1_scores = np.divide(2 * recall * precision, recall + precision, out=np.zeros_like(recall), where=(recall + precision) != 0)
        best_f1_idx = np.argmax(f1_scores)
        smart_threshold = thresholds[best_f1_idx]
        df['pred'] = (df['score'] >= smart_threshold).astype(int)

        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(18, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1, 1]})
        
        ax1.plot(df.index, df['value'], color='cornflowerblue', alpha=0.9, label='Time Series', zorder=1)
        true_anomalies = df[df['label'] == 1]
        if not true_anomalies.empty:
            ax1.scatter(true_anomalies.index, true_anomalies['value'], color='red', marker='o', s=50, zorder=2, label=f'True Anomalies ({len(true_anomalies)})')
        ax1.axvline(x=train_size, color='seagreen', linestyle='--', linewidth=2, label='Train/Test Split')
        ax1.set_title(f'{model_name} Detection Results: {os.path.basename(filename)}', fontsize=16)
        ax1.set_ylabel('Value', fontsize=12)
        ax1.legend(loc='upper right')

        ax2.plot(df.index, df['score'], color='darkviolet', label=f'{model_name} Anomaly Score')
        ax2.axhline(y=smart_threshold, color='darkorange', linestyle='--', linewidth=2, label=f'Smart Threshold={smart_threshold:.4f}')
        ax2.fill_between(df.index, df['score'], smart_threshold, where=df['score'] >= smart_threshold, color='darkviolet', alpha=0.3, interpolate=True)
        ax2.set_ylabel('Anomaly Score', fontsize=12)
        ax2.legend(loc='upper right')

        ax3.fill_between(df.index, 0, 1, where=df['label'] == 1, color='lightcoral', alpha=0.8, step='mid', label='Ground Truth')
        ax3.fill_between(df.index, 1, 2, where=df['pred'] == 1, color='cornflowerblue', alpha=0.8, step='mid', label='Prediction')
        ax3.set_yticks([0.5, 1.5])
        ax3.set_yticklabels(['True', 'Pred'])
        ax3.set_xlabel('Time Step', fontsize=12)
        ax3.legend(loc='upper right')

        plt.tight_layout()
        save_path = os.path.join(save_dir, f"{model_name}_{os.path.basename(filename).replace('.csv', '')}_detection.png")
        plt.savefig(save_path, dpi=200)
        plt.close(fig)

    except Exception as e:
        print(f"❌ 创建可视化失败: {e}")
        import traceback
        traceback.print_exc()


def run_single_experiment(model_name, model_info, dataset, output_dir):
    try:
        train_data, full_data, label = dataset.train, dataset.data, dataset.label
        dataset_name = dataset.name

        print(f"  -> Running Model: {model_name}")
        start_time = time.time()
        
        # Initialize model
        model_type = model_info.get('type', 'user')
        if model_type == 'user':
            model = model_info['class'](HP=model_info['params'])
        else: # baseline
            model = model_info['class'](**model_info['params'])
        
        # Fit model
        model.fit(train_data)
        
        # Get scores and runtime
        scores = model.decision_function(full_data)
        runtime = time.time() - start_time
        
        min_len = min(len(scores), len(label))
        scores, label = scores[:min_len], label[:min_len]
        
        # --- Save artifacts ---
        # 1. Save Training Loss
        if hasattr(model, 'training_history') and model.training_history:
            loss_path = os.path.join(output_dir, f"{model_name}_loss_history.json")
            with open(loss_path, 'w') as f:
                json.dump(model.training_history, f)

        # 2. Save Metrics
        slidingWindow = find_length_rank(full_data[:min_len], rank=1)
        results = get_metrics(scores, label, slidingWindow=slidingWindow)
        results['runtime(s)'] = runtime
        
        metrics_df = pd.DataFrame([results])
        metrics_path = os.path.join(output_dir, f"{model_name}_metrics.csv")
        metrics_df.to_csv(metrics_path, index=False)

        # 3. Save Visualization
        create_visualizations(
            filename=dataset_name,
            data=full_data[:min_len],
            label=label,
            output=scores,
            train_size=dataset.train_size,
            save_dir=output_dir,
            model_name=model_name
        )
        
        print(f"     ✅ Finished. Saved results to {output_dir}")
        return results

    except Exception as e:
        print(f"❌ Error running {model_name} on {dataset_name}: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    os.makedirs(SHOWDOWN_OUTPUT_DIR, exist_ok=True)
    all_datasets = sorted([f for f in os.listdir(DATASET_ROOT) if f.endswith('.csv')])
    
    if not all_datasets:
        print(f"❌ No datasets found in {DATASET_ROOT}. Exiting.")
        return

    # --- Find starting point ---
    start_dataset_name = '172_MITDB_id_3_Medical_tr_27274_1st_27374.csv'
    try:
        start_index = all_datasets.index(start_dataset_name)
        print(f"✅ Found starting dataset: {start_dataset_name} at index {start_index}")
    except ValueError:
        print(f"⚠️ Warning: Starting dataset '{start_dataset_name}' not found. Starting from the beginning.")
        start_index = 0
        
    selected_datasets = all_datasets[start_index:] # Run on all datasets from starting point
        
    print(f"🔬 Running univariate Showdown: LERN vs. LTS on {len(selected_datasets)} datasets...")
    print(f"   Saving detailed results to: {SHOWDOWN_OUTPUT_DIR}")
    print("   Models:", list(MODELS.keys()))

    all_results = []
    
    for dataset_name in tqdm(selected_datasets, desc="Total Progress"):
        dataset_path = os.path.join(DATASET_ROOT, dataset_name)
        
        # Create a dedicated output directory for this dataset
        dataset_output_dir = os.path.join(SHOWDOWN_OUTPUT_DIR, dataset_name.replace('.csv', ''))
        os.makedirs(dataset_output_dir, exist_ok=True)
        
        print(f"\n--- Processing Dataset: {dataset_name} ---")
        
        for model_name, model_info in MODELS.items():
            # Check if results for this model and dataset already exist
            metrics_path = os.path.join(dataset_output_dir, f"{model_name}_metrics.csv")
            if os.path.exists(metrics_path):
                print(f"     ⏩ Skipping {model_name} on {dataset_name}, results already exist.")
                # Still load the old results to include in the final summary
                try:
                    res_df = pd.read_csv(metrics_path)
                    results = res_df.to_dict('records')[0]
                except Exception as e:
                    print(f"     ⚠️ Could not reload existing results for {model_name}: {e}")
                    results = None # Ensure results is None if loading fails
            else:
                results = run_single_experiment(model_name, model_info, UnivariateDataset(dataset_path), dataset_output_dir)

            if results:
                results['model'] = model_name
                results['dataset'] = dataset_name
                all_results.append(results)
    
    if not all_results:
        print("\n❌ No results generated. Check for errors or if all datasets were skipped. Exiting.")
        return

    # --- Final Summary ---
    summary_df = pd.DataFrame(all_results)
    avg_per_model = summary_df.drop(columns=['dataset']).groupby('model').mean()
    
    print("\n\n--- Average Performance (LERN vs. LTS Showdown) ---")
    print(avg_per_model.to_string())
    
    # Also save the summary to a CSV file in the showdown directory
    summary_path = os.path.join(SHOWDOWN_OUTPUT_DIR, 'average_performance_summary.csv')
    avg_per_model.to_csv(summary_path)
    print(f"\n✅ Average performance summary saved to {summary_path}")

    # Save all individual results to one big file as well
    full_results_file = os.path.join(SHOWDOWN_OUTPUT_DIR, '_full_raw_results.csv')
    summary_df.to_csv(full_results_file, index=False)
    
    print(f"\n\n🎉 Univariate Showdown finished!")
    print(f"📄 Summary saved to: {summary_path}")
    print(f"📄 Full raw results saved to: {full_results_file}")


if __name__ == '__main__':
    main()