# -*- coding: utf-8 -*-
# LAD: Linear Anomaly Detector
# A highly efficient, linear model for time series anomaly detection, inspired by DLinear.

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import random, argparse, time, os, sys
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.metrics import precision_recall_curve

warnings.filterwarnings('ignore')

# --- Matplotlib and Seaborn Setup ---
try:
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")
    import matplotlib.font_manager as fm
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    # Check for a common Chinese font
    if any('SimHei' in f for f in available_fonts):
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        print("🎨 使用中文字体: SimHei")
    else:
        print("🎨 未找到中文字体，将使用默认字体")
except Exception as e:
    print(f"🎨 字体设置失败: {e}")

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
    from TSB_AD.utils.slidingWindows import find_length_rank
    from TSB_AD.evaluation.metrics import get_metrics
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    print(f"✅ Added project root to path: {project_root}")
    try:
        from TSB_AD.models.base import BaseDetector
        from TSB_AD.utils.slidingWindows import find_length_rank
        from TSB_AD.evaluation.metrics import get_metrics
    except ImportError:
        print("❌ 无法导入TSB_AD包。请确保在项目根目录下运行 `pip install -e .` 来安装项目。")
        sys.exit(1)


# ----------------------------------------------------
# 1. Linear Model Definition
# ----------------------------------------------------
class MovingAvg(nn.Module):
    """
    Moving average block to extract the trend of a time series.
    """
    def __init__(self, kernel_size):
        super(MovingAvg, self).__init__()
        self.kernel_size = kernel_size
        self.avg = nn.AvgPool1d(kernel_size=kernel_size, stride=1, padding=0)

    def forward(self, x):
        # Pad the series on both ends
        front = x[:, :, 0:1].repeat(1, 1, (self.kernel_size - 1) // 2)
        end = x[:, :, -1:].repeat(1, 1, (self.kernel_size - 1) // 2)
        x_padded = torch.cat([front, x, end], dim=2)
        
        # Apply moving average
        trend = self.avg(x_padded)
        return trend

class SeriesDecomp(nn.Module):
    """
    Series decomposition block for multivariate data.
    """
    def __init__(self, kernel_size):
        super(SeriesDecomp, self).__init__()
        self.moving_avg = MovingAvg(kernel_size)

    def forward(self, x):
        trend = self.moving_avg(x)
        seasonal = x - trend
        return seasonal, trend

class LAD_Model(nn.Module):
    """
    Linear Anomaly Detector (LAD) model core for MULTIVARIATE data.
    """
    def __init__(self, window_size, input_dim, decomp_kernel_size=25):
        super(LAD_Model, self).__init__()
        self.window_size = window_size
        self.input_dim = input_dim
        
        # Decomposition layer
        self.decomposer = SeriesDecomp(kernel_size=decomp_kernel_size)
        
        # Linear layers for seasonal and trend components
        self.linear_seasonal = nn.Linear(window_size, window_size)
        self.linear_trend = nn.Linear(window_size, window_size)

    def forward(self, x):
        # Input x: [Batch, Window_size, Input_dim]
        # Transpose for decomposition: [Batch, Input_dim, Window_size]
        x_transposed = x.permute(0, 2, 1)
        
        seasonal_init, trend_init = self.decomposer(x_transposed)
        
        # Apply linear layers (they operate on the last dim, which is window_size)
        seasonal_output = self.linear_seasonal(seasonal_init)
        trend_output = self.linear_trend(trend_init)

        # Combine and transpose back
        reconstructed_transposed = seasonal_output + trend_output
        reconstructed = reconstructed_transposed.permute(0, 2, 1) # -> [Batch, Window_size, Input_dim]
        
        return reconstructed

# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class LAD_AD(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        self.window_size = HP.get('window_size', 100)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        
        print(f"🔄 初始化LAD (多变量) 检测器... (设备: {self.device})")
        
        self.model = None # Lazy initialization in fit()
        self.ts_scaler = StandardScaler()
        self.score_scaler = MinMaxScaler()
        self.criterion = nn.MSELoss()

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
            
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        
        if self.model is None:
            self.model = LAD_Model(
                window_size=self.window_size,
                input_dim=input_dim,
                decomp_kernel_size=self.HP.get('decomp_kernel_size', 25)
            ).to(self.device)

        X_original_for_scoring = X
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        if len(windows) == 0:
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        optimizer = optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        
        self.model.train()
        for epoch in range(self.epochs):
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                reconstructed = self.model(batch_windows)
                loss = self.criterion(reconstructed, batch_windows)
                
                if not (torch.isnan(loss) or torch.isinf(loss)):
                    loss.backward()
                    optimizer.step()
        
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed = self.model(batch_windows)
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        window_scores = np.array(window_scores)
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(window_scores):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        first_score_idx = self.window_size - 1
        if n_samples > first_score_idx:
             scores_mapped[:first_score_idx] = scores_mapped[first_score_idx]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

# (The main execution block and visualization can be added if needed for standalone testing)
# For now, the focus is on making it importable for the benchmark script. 