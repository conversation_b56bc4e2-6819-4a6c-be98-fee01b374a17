#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准Transformer自编码器 - 用于验证Transformer架构本身的时序重构能力
设计理念：最纯粹的Transformer架构 + 最简单的重构任务
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
from .base import BaseDetector

class PositionalEncoding(nn.Module):
    """标准的正弦位置编码"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class StandardTransformerAutoencoder(nn.Module):
    """
    标准Transformer自编码器
    - 使用纯粹的多头自注意力机制
    - 标准的Encoder-Decoder架构
    - 仅使用MSE重构损失
    """
    def __init__(self, input_dim, d_model=128, nhead=8, num_layers=3, window_size=100):
        super(StandardTransformerAutoencoder, self).__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        self.window_size = window_size
        
        # 输入嵌入层
        self.input_embedding = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model, window_size)
        
        # Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # Transformer Decoder
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            activation='relu',
            batch_first=True
        )
        self.transformer_decoder = nn.TransformerDecoder(decoder_layer, num_layers=num_layers)
        
        # 输出投影层
        self.output_projection = nn.Linear(d_model, input_dim)
        
        # Dropout
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_dim)
        batch_size, seq_len, _ = x.shape
        
        # 输入嵌入 + 位置编码
        embedded = self.input_embedding(x) * math.sqrt(self.d_model)
        embedded = embedded.permute(1, 0, 2)  # (seq_len, batch_size, d_model)
        embedded = self.pos_encoding(embedded)
        embedded = embedded.permute(1, 0, 2)  # (batch_size, seq_len, d_model)
        embedded = self.dropout(embedded)
        
        # Encoder
        memory = self.transformer_encoder(embedded)
        
        # Decoder - 使用zero-initialized target sequence
        tgt = torch.zeros_like(embedded)
        
        # Decoder输出
        decoded = self.transformer_decoder(tgt, memory)
        
        # 输出投影
        reconstruction = self.output_projection(decoded)
        
        return reconstruction

class StandardTransformerAE(BaseDetector):
    """
    标准Transformer自编码器检测器
    用于证明Transformer架构本身在时序重构任务上的局限性
    """
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        
        # 超参数
        self.window_size = HP.get('window_size', 100)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-4)
        self.batch_size = HP.get('batch_size', 64)
        self.d_model = HP.get('d_model', 128)
        self.nhead = HP.get('nhead', 8)
        self.num_layers = HP.get('num_layers', 3)
        
        # GPU设置
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"
        
        print(f"🔄 Initializing Standard Transformer Autoencoder... (Device: {self.device})")
        
        # 模型和预处理器
        self.model = None
        self.ts_scaler = MinMaxScaler()
        self.score_scaler = MinMaxScaler()
        self.criterion = nn.MSELoss()
        self.training_history = {}
        
    def _create_windows(self, X):
        """创建滑动窗口"""
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)
    
    def fit(self, X, y=None):
        """训练模型"""
        input_dim = X.shape[1]
        
        # 初始化模型
        if self.model is None:
            self.model = StandardTransformerAutoencoder(
                input_dim=input_dim,
                d_model=self.d_model,
                nhead=self.nhead,
                num_layers=self.num_layers,
                window_size=self.window_size
            ).to(self.device)
        
        # 数据预处理
        X_original_for_scoring = X
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        # 创建窗口
        windows = self._create_windows(X)
        if len(windows) == 0:
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self
        
        # 创建数据加载器
        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        # 优化器
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
        
        # 训练循环
        self.model.train()
        epoch_losses = []
        
        print(f"Training Standard Transformer AE for {self.epochs} epochs...")
        for epoch in range(self.epochs):
            batch_losses = []
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播 - 纯粹的重构任务
                reconstructed = self.model(batch_windows)
                
                # 计算重构损失 - 纯粹的MSE
                loss = self.criterion(reconstructed, batch_windows)
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                
                batch_losses.append(loss.item())
            
            epoch_loss = np.mean(batch_losses) if batch_losses else 0
            epoch_losses.append(epoch_loss)
            scheduler.step(epoch_loss)
            
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{self.epochs}, Loss: {epoch_loss:.6f}")
        
        self.training_history = {'loss': epoch_losses}
        
        # 计算异常分数
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self
    
    def decision_function(self, X):
        """计算异常分数"""
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        """计算重构误差作为异常分数"""
        n_samples = X.shape[0]
        
        # 数据预处理
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X
        
        # 创建窗口
        windows = self._create_windows(X_norm)
        if len(windows) == 0:
            return np.zeros(n_samples)
        
        # 数据加载器
        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        # 计算重构误差
        self.model.eval()
        window_scores = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed = self.model(batch_windows)
                
                # 计算每个窗口的平均重构误差
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        # 将窗口分数映射回时间序列
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        
        for i, score in enumerate(np.array(window_scores)):
            start, end = i, i + self.window_size
            if end <= n_samples:
                scores_mapped[start:end] += score
                counts[start:end] += 1
        
        # 平均重叠区域的分数
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        # 填充未覆盖的开始部分
        if self.window_size > 1 and n_samples > self.window_size - 1:
            scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]
        
        # 归一化分数
        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()
    
    def get_reconstruction(self, X):
        """获取重构结果 - 用于可视化分析"""
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        # 数据预处理
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X
        
        # 创建窗口
        windows = self._create_windows(X_norm)
        if len(windows) == 0:
            return X.copy()
        
        # 获取重构
        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        all_reconstructions = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed = self.model(batch_windows)
                all_reconstructions.append(reconstructed.cpu().numpy())
        
        if not all_reconstructions:
            return X.copy()
        
        # 重构完整时间序列
        full_recons_windows = np.concatenate(all_reconstructions)
        reconstructed_full = np.zeros_like(X_norm)
        counts = np.zeros_like(X_norm)
        
        for i, recon_window in enumerate(full_recons_windows):
            start, end = i, i + self.window_size
            if end > len(reconstructed_full):
                break
            reconstructed_full[start:end] += recon_window
            counts[start:end] += 1
        
        # 平均重叠区域
        reconstructed_full[counts > 0] /= counts[counts > 0]
        
        # 填充开始部分
        if self.window_size > 1:
            reconstructed_full[:self.window_size-1] = reconstructed_full[self.window_size-1]
        
        # 反归一化
        if self.normalize:
            try:
                reconstructed_full = self.ts_scaler.inverse_transform(reconstructed_full)
            except:
                pass
        
        return reconstructed_full