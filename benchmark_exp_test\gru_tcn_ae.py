# -*- coding: utf-8 -*-
# GRU-based Temporal Convolutional Network Autoencoder (GRU-TCN-AE)
# An Autoencoder model combining TCN for local feature extraction and GRU for sequential modeling.
# This version is a standard AE, not a VAE, for closer comparison to Lightweight_TS_Model.
#
# This file is structured to be compatible with the TSB-AD benchmark framework.

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import sys
from torch.utils.data import DataLoader, TensorDataset
from torch.nn.utils import weight_norm
import math

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector

from sklearn.preprocessing import MinMaxScaler

# --- Association Discrepancy Loss (Copied from previous work) ---
def get_prior_association(window_size, sigma_scale=1.0):
    """Generates a prior association matrix based on temporal distance."""
    prior = torch.zeros(window_size, window_size)
    for i in range(window_size):
        for j in range(window_size):
            prior[i, j] = abs(i - j)
    
    sigma = window_size * sigma_scale
    prior = 1.0 / (math.sqrt(2 * math.pi) * sigma) * torch.exp(-prior**2 / (2 * sigma**2))
    return prior

def association_discrepancy_loss(attention_weights, prior_association, device):
    """Calculates the KL divergence between series-derived and prior associations."""
    # attention_weights shape: (batch_size, num_heads, seq_len, seq_len)
    # We can average over the heads or take the first one. Let's average.
    series_association = torch.mean(attention_weights, dim=1) # Avg over heads
    
    # Ensure prior is on the same device. Broadcasting will handle the batch dimension.
    prior_association = prior_association.to(device)

    # Use log_softmax and kl_div for numerical stability
    log_series = F.log_softmax(series_association, dim=-1)
    log_prior = F.log_softmax(prior_association, dim=-1)
    
    kl_div = F.kl_div(log_series, log_prior, reduction='batchmean', log_target=True)
    return kl_div

# ----------------------------------------------------
# 1. Model Definition (Adapted from gru_tcn_vae.py)
# ----------------------------------------------------

class LinearAttention(nn.Module):
    """
    A lightweight, linear attention mechanism with O(N) complexity.
    This implementation is based on the idea of changing the order of matrix multiplication.
    No softmax is used, following the 'Transformers are RNNs' paper.
    """
    def __init__(self, embed_dim, n_heads, dropout=0.1):
        super().__init__()
        self.n_heads = n_heads
        self.head_dim = embed_dim // n_heads
        
        # Ensure the embedding dimension is divisible by the number of heads
        if self.head_dim * n_heads != embed_dim:
            raise ValueError("embed_dim must be divisible by n_heads")

        self.to_q = nn.Linear(embed_dim, embed_dim, bias=False)
        self.to_k = nn.Linear(embed_dim, embed_dim, bias=False)
        self.to_v = nn.Linear(embed_dim, embed_dim, bias=False)
        
        self.elu = nn.ELU()
        self.to_out = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, query, key, value):
        # In our self-attention case, query, key, and value are the same.
        # We'll use `query` as the main input `x` to derive Q, K, V.
        x = query
        # x shape: (batch_size, seq_len, embed_dim)
        b, n, d, h = *x.shape, self.n_heads

        # Project to Q, K, V and split into heads
        q = self.to_q(x).view(b, n, h, self.head_dim).transpose(1, 2)
        k = self.to_k(x).view(b, n, h, self.head_dim).transpose(1, 2)
        v = self.to_v(x).view(b, n, h, self.head_dim).transpose(1, 2)
        
        # Use ELU activation on Q and K, as suggested for stabilization without softmax
        q = self.elu(q) + 1
        k = self.elu(k) + 1

        # The core of Linear Attention: change the order of multiplication
        # Instead of (q @ k.transpose) @ v  (O(n^2)), we do q @ (k.transpose @ v) (O(n))
        # k_t_v shape: (b, h, head_dim, head_dim)
        k_t_v = torch.einsum('bhnd,bhnm->bhdm', k, v)
        
        # z shape: (b, h, n, head_dim)
        z = torch.einsum('bhnj,bhjd->bhnd', q, k_t_v)

        # For compatibility, we can approximate attention weights. 
        # Note: This is an approximation and adds overhead.
        # It's primarily for analysis if needed, not for the core computation.
        # Here we return a simplified version or None.
        attn_weights = None 

        # Concatenate heads and project out
        out = z.transpose(1, 2).reshape(b, n, d)
        out = self.to_out(out)
        
        return self.dropout(out), attn_weights

class GatedAttention(nn.Module):
    """
    A super lightweight gated attention mechanism.
    It learns a single gate value per time step to scale the input features.
    """
    def __init__(self, embed_dim, dropout=0.1):
        super().__init__()
        # A simple linear layer followed by a sigmoid to create a gate
        self.gate_layer = nn.Sequential(
            nn.Linear(embed_dim, 1),
            nn.Sigmoid()
        )
        self.dropout = nn.Dropout(dropout)

    def forward(self, query, key, value):
        # In self-attention, query, key, and value are the same.
        x = query # Shape: (batch, seq_len, embed_dim)
        
        # Calculate a gate value for each time step
        # gates shape: (batch, seq_len, 1)
        gates = self.gate_layer(x)
        
        # Apply the gates to the input, scaling each time step's features
        gated_output = x * gates
        
        # For compatibility, we return the gates as a stand-in for attention weights.
        # Reshape to (batch, 1, seq_len) to mimic a single attention head.
        attn_weights = gates.permute(0, 2, 1)

        return self.dropout(gated_output), attn_weights

class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)

        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)

        self.net = nn.Sequential(self.conv1, self.relu1, self.dropout1,
                                 self.conv2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        self.init_weights()

    def init_weights(self):
        self.conv1.weight.data.normal_(0, 0.01)
        self.conv2.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)

    def forward(self, x):
        out = self.relu1(self.conv1(x))
        out = self.dropout1(out)
        
        out = self.relu2(self.conv2(out))
        out = self.dropout2(out)
        
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers += [TemporalBlock(in_channels, out_channels, kernel_size, stride=1, dilation=dilation_size,
                                     padding=0, dropout=dropout)]

        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

class GruTcnAe_Model(nn.Module):
    """The core GRU-TCN-AE neural network architecture, now with Attention."""
    def __init__(self, input_dim, window_size, latent_dim, tcn_channels, tcn_kernel_size, tcn_dropout, gru_hidden_dim, gru_layers, n_heads=4, attention_type='gated', downsample_factor=1):
        super(GruTcnAe_Model, self).__init__()
        self.latent_dim = latent_dim
        self.window_size = window_size
        self.tcn_channels = tcn_channels
        self.input_dim = input_dim
        self.downsample_factor = downsample_factor

        # === Encoder ===
        self.encoder_tcn = TemporalConvNet(
            num_inputs=input_dim,
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size,
            dropout=tcn_dropout
        )
        
        # --- Downsampling Layer ---
        if self.downsample_factor > 1:
            self.downsampler = nn.AvgPool1d(kernel_size=self.downsample_factor)
            self.downsampled_len = window_size // self.downsample_factor
            print(f"💡 Using downsampling with factor {self.downsample_factor}. New sequence length: {self.downsampled_len}")
        else:
            self.downsampler = None
            self.downsampled_len = window_size
        
        # --- Attention Layer ---
        if attention_type == 'linear':
            self.attention = LinearAttention(embed_dim=tcn_channels[-1], n_heads=n_heads)
            print("💡 Using Linear Attention")
        elif attention_type == 'gated':
            self.attention = GatedAttention(embed_dim=tcn_channels[-1])
            print("💡 Using Gated Attention")
        else: # 'multihead' or default
        self.attention = nn.MultiheadAttention(embed_dim=tcn_channels[-1], num_heads=n_heads, batch_first=True)
            print("💡 Using Standard Multi-Head Attention")

        self.encoder_gru = nn.GRU(
            input_size=tcn_channels[-1],
            hidden_size=gru_hidden_dim,
            num_layers=gru_layers,
            batch_first=True
        )
        # From GRU output to a deterministic latent space
        self.fc_encode = nn.Linear(gru_hidden_dim, latent_dim)

        # === Decoder ===
        self.decoder_fc = nn.Linear(latent_dim, self.downsampled_len * tcn_channels[-1])
        self.decoder_tcn = TemporalConvNet(
            num_inputs=tcn_channels[-1],
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size,
            dropout=tcn_dropout
        )
        self.decoder_conv_out = nn.Conv1d(tcn_channels[-1], input_dim, kernel_size=1)

    def forward(self, x):
        # x shape: (batch_size, window_size, input_dim)
        x_permuted = x.permute(0, 2, 1)  # -> (batch, input_dim, window_size) for TCN

        # Encoder
        tcn_out = self.encoder_tcn(x_permuted)
        
        # Downsample if configured
        if self.downsampler:
            tcn_out_downsampled = self.downsampler(tcn_out)
        else:
            tcn_out_downsampled = tcn_out

        tcn_out_permuted = tcn_out_downsampled.permute(0, 2, 1) # -> (batch, downsampled_len, tcn_channels[-1])
        
        # Apply self-attention
        attn_output, attn_weights = self.attention(tcn_out_permuted, tcn_out_permuted, tcn_out_permuted)
        
        _, gru_hidden = self.encoder_gru(attn_output) # Use attention output
        gru_out = gru_hidden[-1] # -> (batch, gru_hidden_dim)
        
        # Get the deterministic latent vector
        encoded = self.fc_encode(gru_out)

        # Decoder
        dec_in = self.decoder_fc(encoded)
        dec_in = dec_in.view(-1, self.tcn_channels[-1], self.downsampled_len)
        
        # Upsample if needed
        if self.downsampler:
            dec_in_upsampled = F.interpolate(dec_in, size=self.window_size, mode='linear', align_corners=False)
        else:
            dec_in_upsampled = dec_in

        dec_tcn_out = self.decoder_tcn(dec_in_upsampled)
        reconstructed_permuted = self.decoder_conv_out(dec_tcn_out)
        reconstructed = reconstructed_permuted.permute(0, 2, 1)
        reconstructed = torch.sigmoid(reconstructed) # Sigmoid for output scaled to [0,1]

        return reconstructed, attn_weights

# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class GruTcnAe_AD(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        # Back to simple HPs
        self.window_size = HP.get('window_size', 128)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)
        
        tcn_layers = HP.get('tcn_layers', 4)
        tcn_filters = HP.get('tcn_filters', 64)
        self.tcn_channels = [tcn_filters] * tcn_layers
        self.tcn_kernel_size = HP.get('tcn_kernel_size', 3)
        self.tcn_dropout = HP.get('tcn_dropout', 0.2)
        self.gru_hidden_dim = HP.get('gru_hidden_dim', 64)
        self.gru_layers = HP.get('gru_layers', 1)
        self.n_heads = HP.get('n_heads', 4)
        self.use_linear_attention = HP.get('use_linear_attention', True)
        self.attention_type = HP.get('attention_type', 'gated')
        self.downsample_factor = HP.get('downsample_factor', 1)
        
        print(f"🔄 Initializing GRU-TCN-AE Detector... (Device: {self.device})")
        
        self.ts_scaler = MinMaxScaler()
        self.score_scaler = MinMaxScaler()
        self.model = None
        self.criterion = nn.MSELoss()
        self.training_history = {}

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        
        if self.model is None:
            self.model = GruTcnAe_Model(
                input_dim=input_dim, window_size=self.window_size, latent_dim=self.latent_dim,
                tcn_channels=self.tcn_channels, tcn_kernel_size=self.tcn_kernel_size,
                tcn_dropout=self.tcn_dropout, gru_hidden_dim=self.gru_hidden_dim,
                gru_layers=self.gru_layers,
                n_heads=self.n_heads,
                attention_type=self.attention_type,
                downsample_factor=self.downsample_factor
            ).to(self.device)

        X_original_for_scoring = X
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=self.lr)

        self.model.train()
        for epoch in range(self.epochs):
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                recon, _ = self.model(batch_windows) # Still gets attn_weights, but we ignore it
                loss = self.criterion(recon, batch_windows)
                loss.backward()
                optimizer.step()
        
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        """
        Calculates anomaly scores for new data.
        Required by the BaseDetector interface.
        """
        return self._compute_scores(X, fit_scaler=False)

    def _compute_scores(self, X, fit_scaler=False):
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        all_window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed, _ = self.model(batch_windows) # attn_weights are ignored
                
                # Simple reconstruction error
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                all_window_scores.extend(errors.cpu().numpy())
        
        # Map window scores back to point scores
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(all_window_scores):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

    def predict(self, X):
        scores = self.decision_function(X)
        scaled_scores = self.score_scaler.transform(scores.reshape(-1, 1)).flatten()
        return (scaled_scores > 0.5).astype(int)

# --- Standalone Test ---
def standalone_test():
    print("--- Running Standalone Test for GRU-TCN-AE ---")
    
    train_data = np.random.randn(1000, 1)
    test_data = np.random.randn(500, 1)
    test_data[100:150] += 5 
    
    hp = {
        'window_size': 64, 'epochs': 10, 'lr': 1e-3, 'batch_size': 32,
        'latent_dim': 16, 'tcn_layers': 3, 'tcn_filters': 32,
        'tcn_kernel_size': 3, 'tcn_dropout': 0.1, 'gpu': 0,
        'gru_hidden_dim': 32, 'gru_layers': 1
    }
    
    detector = GruTcnAe_AD(HP=hp, normalize=True)
    detector.fit(train_data)
    
    scores = detector.decision_function(test_data)
    
    print("Scores shape:", scores.shape)
    
    normal_max = np.max(np.concatenate([scores[:100], scores[150:]]))
    anomaly_max = np.max(scores[100:150])
    print(f"Max score in normal region: {normal_max:.4f}")
    print(f"Max score in anomaly region: {anomaly_max:.4f}")
    
    if anomaly_max > normal_max:
        print("✅ Test PASSED: Anomaly scores are higher than normal scores.")
    else:
        print("❌ Test FAILED: Anomaly scores are NOT distinctly higher.")

if __name__ == '__main__':
    standalone_test() 