"""
Demo script for HTA-AD with Real LLM Explanation Module

This script demonstrates how to use the enhanced HTA-AD model with actual LLM-based
anomaly explanations. It supports multiple LLM providers including OpenAI, Anthropic,
Ollama (local), and Hugging Face transformers.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import json
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore')

# Import our enhanced HTA-AD with LLM explanation
from hta_ad import HTA_AD
from llm_explanation_module import LLMExplanationModule

def generate_synthetic_data_with_anomalies(n_samples=1000, n_features=3, anomaly_ratio=0.05):
    """
    Generate synthetic multivariate time series data with injected anomalies
    
    Parameters
    ----------
    n_samples : int
        Number of time points
    n_features : int
        Number of features (sensors)
    anomaly_ratio : float
        Ratio of anomalous points
        
    Returns
    -------
    Tuple[np.ndar<PERSON>, np.n<PERSON><PERSON>, List[int]]
        (data, labels, anomaly_indices)
    """
    np.random.seed(42)
    
    # Generate normal patterns
    t = np.linspace(0, 4*np.pi, n_samples)
    X = np.zeros((n_samples, n_features))
    
    # Feature 1: Temperature-like (sine wave with noise)
    X[:, 0] = 20 + 5 * np.sin(t) + 0.5 * np.random.randn(n_samples)
    
    # Feature 2: Pressure-like (cosine wave with trend)
    X[:, 1] = 100 + 10 * np.cos(t) + 0.01 * t + 0.8 * np.random.randn(n_samples)
    
    # Feature 3: Vibration-like (higher frequency with bursts)
    X[:, 2] = 2 * np.sin(3*t) + 0.3 * np.random.randn(n_samples)
    
    # Inject different types of anomalies
    labels = np.zeros(n_samples)
    anomaly_indices = []
    
    n_anomalies = int(n_samples * anomaly_ratio)
    
    for i in range(n_anomalies):
        # Random anomaly position
        idx = np.random.randint(50, n_samples - 50)
        anomaly_type = np.random.choice(['point', 'contextual', 'collective'])
        
        if anomaly_type == 'point':
            # Point anomaly: sudden spike
            feature_idx = np.random.randint(n_features)
            X[idx, feature_idx] += np.random.choice([-1, 1]) * (5 + 3 * np.random.rand())
            
        elif anomaly_type == 'contextual':
            # Contextual anomaly: pattern change over a window
            window_size = np.random.randint(5, 15)
            feature_idx = np.random.randint(n_features)
            for j in range(window_size):
                if idx + j < n_samples:
                    X[idx + j, feature_idx] += 2 * np.sin(j * 0.5) * np.random.choice([-1, 1])
                    
        elif anomaly_type == 'collective':
            # Collective anomaly: multiple features affected
            window_size = np.random.randint(3, 8)
            for j in range(window_size):
                if idx + j < n_samples:
                    X[idx + j, :] += np.random.randn(n_features) * 2
        
        # Mark as anomaly
        labels[idx:idx+window_size] = 1
        anomaly_indices.extend(range(idx, min(idx+window_size, n_samples)))
    
    return X, labels, list(set(anomaly_indices))

def setup_llm_config(provider='openai'):
    """
    Setup LLM configuration for different providers
    
    Parameters
    ----------
    provider : str
        LLM provider: 'openai', 'anthropic', 'ollama', 'local'
        
    Returns
    -------
    Dict[str, Any]
        LLM configuration
    """
    if provider == 'openai':
        return {
            'provider': 'openai',
            'model': 'gpt-3.5-turbo',
            'api_key': os.getenv('OPENAI_API_KEY'),  # Set your API key as environment variable
            'max_tokens': 500,
            'temperature': 0.3
        }
    elif provider == 'anthropic':
        return {
            'provider': 'anthropic',
            'model': 'claude-3-sonnet-20240229',
            'api_key': os.getenv('ANTHROPIC_API_KEY'),  # Set your API key as environment variable
            'max_tokens': 500,
            'temperature': 0.3
        }
    elif provider == 'ollama':
        return {
            'provider': 'ollama',
            'model': 'llama2',  # or 'mistral', 'codellama', etc.
            'base_url': 'http://localhost:11434',
            'max_tokens': 500,
            'temperature': 0.3
        }
    elif provider == 'local':
        return {
            'provider': 'local',
            'model': 'microsoft/DialoGPT-medium',  # or any other local model
            'max_tokens': 200,
            'temperature': 0.3
        }
    else:
        raise ValueError(f"Unsupported provider: {provider}")

def demo_llm_explanation(llm_provider='openai'):
    """
    Main demo function for LLM-based anomaly explanation
    
    Parameters
    ----------
    llm_provider : str
        LLM provider to use for explanations
    """
    print(f"🚀 Starting HTA-AD Demo with {llm_provider.upper()} LLM Explanations")
    print("=" * 60)
    
    # Generate synthetic data
    print("📊 Generating synthetic multivariate time series data...")
    X, y_true, true_anomaly_indices = generate_synthetic_data_with_anomalies(
        n_samples=1000, 
        n_features=3, 
        anomaly_ratio=0.03
    )
    
    feature_names = ["Temperature (°C)", "Pressure (kPa)", "Vibration (mm/s)"]
    print(f"   - Data shape: {X.shape}")
    print(f"   - True anomalies: {len(true_anomaly_indices)} points")
    
    # Define domain knowledge for better explanations
    domain_knowledge = {
        "feature_0": {
            "point_anomaly": "温度传感器故障、环境温度突变或冷却系统异常",
            "contextual_anomaly": "温度控制系统调节异常或环境条件渐变",
            "collective_anomaly": "整体热管理系统故障或外部热源影响",
            "seasonal_anomaly": "季节性温度模式中断或系统校准问题"
        },
        "feature_1": {
            "point_anomaly": "压力传感器故障、管道堵塞或阀门突然关闭",
            "contextual_anomaly": "压力调节系统响应延迟或流量控制异常",
            "collective_anomaly": "压力系统整体故障或多点泄漏",
            "seasonal_anomaly": "压力系统的定期维护影响或环境压力变化"
        },
        "feature_2": {
            "point_anomaly": "机械冲击、轴承故障或异物撞击",
            "contextual_anomaly": "设备磨损加剧、负载变化或运行模式切换",
            "collective_anomaly": "机械系统整体失衡或多组件同时故障",
            "seasonal_anomaly": "设备定期保养周期或环境振动源变化"
        }
    }
    
    # Setup LLM configuration
    print(f"🤖 Setting up {llm_provider.upper()} LLM configuration...")
    try:
        llm_config = setup_llm_config(llm_provider)
        print(f"   - Provider: {llm_config['provider']}")
        print(f"   - Model: {llm_config['model']}")
        print(f"   - Max tokens: {llm_config['max_tokens']}")
    except Exception as e:
        print(f"❌ Failed to setup LLM config: {e}")
        return
    
    # Initialize HTA-AD model
    print("🔧 Initializing HTA-AD model...")
    HP = {
        'window_size': 64,
        'epochs': 25,
        'lr': 1e-3,
        'batch_size': 32,
        'latent_dim': 16,
        'tcn_channels': [16, 16, 16],
        'cnn_channels': 8,
        'downsample_stride': 2,
        'gpu': 0
    }
    
    detector = HTA_AD(HP=HP, normalize=True)
    
    # Split data for training and testing
    train_size = int(0.7 * len(X))
    X_train = X[:train_size]
    X_test = X[train_size:]
    
    print(f"   - Training samples: {len(X_train)}")
    print(f"   - Testing samples: {len(X_test)}")
    
    # Train the model
    print("🏋️ Training HTA-AD model...")
    detector.fit(X_train)
    print("   ✅ Training completed!")
    
    # Initialize LLM explanation module
    print("🧠 Initializing LLM Explanation Module...")
    explanation_module = LLMExplanationModule(
        hta_model=detector.model,
        window_size=HP['window_size'],
        feature_names=feature_names,
        domain_knowledge=domain_knowledge,
        llm_config=llm_config,
        device=detector.device
    )
    
    # Fit explanation module with normal patterns
    if detector.normalize:
        X_train_norm = detector.ts_scaler.transform(X_train)
    else:
        X_train_norm = X_train
    
    training_windows = detector._create_windows(X_train_norm)
    explanation_module.fit(training_windows)
    print("   ✅ LLM Explanation Module initialized!")
    
    # Detect anomalies
    print("🔍 Detecting anomalies...")
    anomaly_scores = detector.decision_function(X_test)
    
    # Find top anomalies
    threshold = np.percentile(anomaly_scores, 95)
    detected_anomalies = np.where(anomaly_scores > threshold)[0]
    
    print(f"   - Detected {len(detected_anomalies)} anomalies")
    print(f"   - Detection threshold: {threshold:.6f}")
    
    # Generate LLM explanations for top anomalies
    print("🤖 Generating LLM explanations for detected anomalies...")
    print("=" * 60)
    
    # Prepare test data for explanation
    if detector.normalize:
        X_test_norm = detector.ts_scaler.transform(X_test)
    else:
        X_test_norm = X_test
    
    explanations = {}
    
    # Explain top 3 anomalies
    top_anomalies = detected_anomalies[np.argsort(anomaly_scores[detected_anomalies])[-3:]]
    
    for i, anomaly_idx in enumerate(top_anomalies):
        print(f"\n🔍 Anomaly #{i+1} at index {anomaly_idx + train_size}")
        print(f"   Anomaly score: {anomaly_scores[anomaly_idx]:.6f}")
        
        # Extract window around anomaly
        start_idx = max(0, anomaly_idx - HP['window_size'] + 1)
        end_idx = min(len(X_test_norm), start_idx + HP['window_size'])
        
        if end_idx - start_idx < HP['window_size']:
            start_idx = max(0, end_idx - HP['window_size'])
        
        anomaly_window = X_test_norm[start_idx:end_idx]
        
        if anomaly_window.shape[0] == HP['window_size']:
            try:
                # Generate explanation using LLM
                explanation_context = explanation_module.explain_anomaly(
                    anomaly_window=anomaly_window,
                    time_index=anomaly_idx + train_size
                )
                
                explanations[anomaly_idx] = explanation_context
                
                print(f"   Anomaly type: {explanation_context['anomaly_type']}")
                print(f"   Most affected feature: {explanation_context['feature_names'][explanation_context['top_feature_indices'][0]]}")
                print("\n📝 LLM Explanation:")
                print("   " + explanation_context['explanation'].replace('\n', '\n   '))
                
                # Save visualization
                os.makedirs("./llm_demo_results", exist_ok=True)
                fig = explanation_module.visualize_anomaly(
                    context=explanation_context,
                    save_path=f"./llm_demo_results/anomaly_{anomaly_idx}_explanation.png"
                )
                plt.close(fig)
                print(f"   💾 Visualization saved to: ./llm_demo_results/anomaly_{anomaly_idx}_explanation.png")
                
            except Exception as e:
                print(f"   ❌ Failed to generate explanation: {e}")
        else:
            print(f"   ⚠️ Insufficient data for explanation")
        
        print("-" * 40)
    
    # Save all explanations to JSON
    explanations_json = {}
    for idx, context in explanations.items():
        explanations_json[str(idx)] = {
            'anomaly_type': context['anomaly_type'],
            'time_index': context['time_index'],
            'explanation': context['explanation'],
            'most_affected_feature': context['feature_names'][context['top_feature_indices'][0]],
            'anomaly_score': float(anomaly_scores[idx])
        }
    
    with open('./llm_demo_results/explanations.json', 'w', encoding='utf-8') as f:
        json.dump(explanations_json, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 All explanations saved to: ./llm_demo_results/explanations.json")
    print("\n✅ Demo completed successfully!")
    print(f"📊 Summary:")
    print(f"   - Total test samples: {len(X_test)}")
    print(f"   - Detected anomalies: {len(detected_anomalies)}")
    print(f"   - Explained anomalies: {len(explanations)}")
    print(f"   - LLM Provider: {llm_provider.upper()}")

if __name__ == "__main__":
    # You can change the provider here
    # Options: 'openai', 'anthropic', 'ollama', 'local'
    
    # For OpenAI (requires OPENAI_API_KEY environment variable)
    # demo_llm_explanation('openai')
    
    # For Anthropic Claude (requires ANTHROPIC_API_KEY environment variable)
    # demo_llm_explanation('anthropic')
    
    # For local Ollama (requires Ollama running locally)
    # demo_llm_explanation('ollama')
    
    # For local Hugging Face model (no API key required)
    demo_llm_explanation('local')
    
    print("\n" + "="*60)
    print("💡 To use different LLM providers:")
    print("   - OpenAI: Set OPENAI_API_KEY environment variable")
    print("   - Anthropic: Set ANTHROPIC_API_KEY environment variable") 
    print("   - Ollama: Install and run Ollama locally")
    print("   - Local: Uses Hugging Face transformers (no setup needed)")
