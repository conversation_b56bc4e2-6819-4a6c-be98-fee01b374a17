#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HTA_AD多变量数据集超参数测试脚本
用于测试不同超参数组合对模型性能的影响
"""

import os
import pandas as pd
import numpy as np
import json
import time
from itertools import product
from tqdm import tqdm
from datetime import datetime
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns

# 导入模型和评估工具
from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.models.HTA_AD_M import HTA_AD_M
from TSB_AD.evaluation.metrics import get_metrics

# 设置随机种子以确保结果可复现
np.random.seed(42)

# 配置参数
CONFIG = {
    # 数据集配置
    'datasets': [
        'Datasets/TSB-AD-U/355_UCR_id_53_HumanActivity_tr_48750_1st_143411.csv',
        'Datasets/TSB-AD-U/356_UCR_id_54_HumanActivity_tr_48750_1st_143411.csv',
        'Datasets/TSB-AD-U/357_UCR_id_55_HumanActivity_tr_48750_1st_143411.csv'
    ],
    # 训练集比例
    'train_ratio': 0.15,
    # 测试的模型
    'models': ['HTA_AD', 'HTA_AD_M'],
    # 超参数网格搜索配置
    'hyperparameters': {
        'window_size': [64, 128, 256],
        'latent_dim': [16, 32, 64],
        'tcn_channels_config': [
            [16, 16], 
            [32, 32], 
            [64, 64],
            [16, 16, 16],
            [32, 32, 32],
            [64, 64, 64]
        ],
        'cnn_channels': [8, 16, 32],
        'downsample_stride': [1, 2, 4],
        # 固定参数
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 64,
        'gpu': 0
    },
    # 结果保存路径
    'results_dir': 'hyperparameter_results',
    # 是否保存可视化结果
    'save_plots': True
}

def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def load_dataset(file_path):
    """加载数据集并进行预处理"""
    print(f"加载数据集: {file_path}")
    df = pd.read_csv(file_path).dropna()
    data = df.iloc[:, 0:-1].values.astype(float)
    labels = df['Label'].astype(int).to_numpy()
    
    # 提取数据集名称
    dataset_name = os.path.basename(file_path).split('.')[0]
    
    return data, labels, dataset_name

def run_experiment(model_name, dataset_path, hyperparams):
    """运行单个实验"""
    # 加载数据集
    data, labels, dataset_name = load_dataset(dataset_path)
    
    # 划分训练集和测试集
    train_size = int(len(data) * CONFIG['train_ratio'])
    data_train = data[:train_size]
    data_test = data[train_size:]
    
    # 实例化模型
    hp = hyperparams.copy()
    hp['tcn_channels'] = hp.pop('tcn_channels_config')  # 重命名参数
    
    start_time = time.time()
    
    try:
        # 选择模型
        if model_name == 'HTA_AD':
            model = HTA_AD(HP=hp)
        elif model_name == 'HTA_AD_M':
            model = HTA_AD_M(HP=hp)
        else:
            raise ValueError(f"不支持的模型: {model_name}")
        
        # 训练模型
        model.fit(data_train)
        
        # 获取测试集的异常分数
        test_scores = model.decision_function(data_test)
        
        # 创建完整的分数数组
        full_scores = np.zeros(len(data))
        full_scores[train_size:] = test_scores
        
        # 计算评估指标
        metrics = get_metrics(full_scores, labels)
        
        # 记录运行时间
        runtime = time.time() - start_time
        
        return {
            'dataset': dataset_name,
            'model': model_name,
            'hyperparameters': hyperparams,
            'metrics': metrics,
            'runtime': runtime,
            'status': 'success'
        }
    
    except Exception as e:
        # 记录错误信息
        return {
            'dataset': dataset_name,
            'model': model_name,
            'hyperparameters': hyperparams,
            'metrics': None,
            'runtime': time.time() - start_time,
            'status': 'error',
            'error_message': str(e)
        }

def generate_hyperparameter_combinations():
    """生成所有超参数组合"""
    hyperparams = CONFIG['hyperparameters']
    
    # 固定参数
    fixed_params = {
        'epochs': hyperparams['epochs'],
        'lr': hyperparams['lr'],
        'batch_size': hyperparams['batch_size'],
        'gpu': hyperparams['gpu']
    }
    
    # 变化参数
    variable_params = {
        'window_size': hyperparams['window_size'],
        'latent_dim': hyperparams['latent_dim'],
        'tcn_channels_config': hyperparams['tcn_channels_config'],
        'cnn_channels': hyperparams['cnn_channels'],
        'downsample_stride': hyperparams['downsample_stride']
    }
    
    # 生成所有组合
    combinations = []
    keys = variable_params.keys()
    values = variable_params.values()
    
    for combination in product(*values):
        param_dict = dict(zip(keys, combination))
        param_dict.update(fixed_params)
        combinations.append(param_dict)
    
    return combinations

def visualize_results(results, save_dir):
    """可视化实验结果"""
    # 将结果转换为DataFrame
    rows = []
    for result in results:
        if result['status'] == 'success' and result['metrics'] is not None:
            row = {
                'dataset': result['dataset'],
                'model': result['model'],
                'window_size': result['hyperparameters']['window_size'],
                'latent_dim': result['hyperparameters']['latent_dim'],
                'tcn_channels': str(result['hyperparameters']['tcn_channels_config']),
                'cnn_channels': result['hyperparameters']['cnn_channels'],
                'downsample_stride': result['hyperparameters']['downsample_stride'],
                'runtime': result['runtime']
            }
            # 添加指标
            for metric_name, metric_value in result['metrics'].items():
                row[metric_name] = metric_value
            rows.append(row)
    
    if not rows:
        print("没有成功的实验结果可视化")
        return
    
    df = pd.DataFrame(rows)
    
    # 保存结果表格
    df.to_csv(os.path.join(save_dir, 'all_results.csv'), index=False)
    
    # 对每个数据集和模型组合创建单独的可视化
    for dataset in df['dataset'].unique():
        for model in df['model'].unique():
            subset = df[(df['dataset'] == dataset) & (df['model'] == model)]
            
            if subset.empty:
                continue
            
            # 创建性能热图
            plt.figure(figsize=(15, 10))
            
            # 选择重要的指标进行可视化
            metrics_to_plot = ['AUC-ROC', 'AUC-PR', 'Standard-F1', 'Event-based-F1']
            
            for i, metric in enumerate(metrics_to_plot):
                plt.subplot(2, 2, i+1)
                
                # 为热图准备数据
                pivot = subset.pivot_table(
                    index='window_size', 
                    columns='latent_dim',
                    values=metric,
                    aggfunc='mean'
                )
                
                # 绘制热图
                sns.heatmap(pivot, annot=True, fmt=".3f", cmap="YlGnBu", cbar=True)
                plt.title(f'{metric} - {model} on {dataset}')
                plt.xlabel('Latent Dimension')
                plt.ylabel('Window Size')
            
            plt.tight_layout()
            plt.savefig(os.path.join(save_dir, f'{dataset}_{model}_metrics_heatmap.png'))
            plt.close()
            
            # 绘制运行时间与性能的关系图
            plt.figure(figsize=(12, 8))
            plt.scatter(subset['runtime'], subset['AUC-ROC'], alpha=0.7)
            
            # 添加标签
            for i, row in subset.iterrows():
                plt.annotate(
                    f"w{row['window_size']}_l{row['latent_dim']}_c{row['cnn_channels']}_s{row['downsample_stride']}", 
                    (row['runtime'], row['AUC-ROC']),
                    fontsize=8
                )
            
            plt.xlabel('Runtime (seconds)')
            plt.ylabel('AUC-ROC')
            plt.title(f'Performance vs Runtime - {model} on {dataset}')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.savefig(os.path.join(save_dir, f'{dataset}_{model}_performance_vs_runtime.png'))
            plt.close()
    
    # 创建模型比较图
    for dataset in df['dataset'].unique():
        model_comparison = df[df['dataset'] == dataset].groupby('model').agg({
            'AUC-ROC': 'max',
            'AUC-PR': 'max',
            'Standard-F1': 'max',
            'Event-based-F1': 'max',
            'runtime': 'mean'
        }).reset_index()
        
        # 绘制条形图
        plt.figure(figsize=(12, 8))
        metrics = ['AUC-ROC', 'AUC-PR', 'Standard-F1', 'Event-based-F1']
        
        x = np.arange(len(model_comparison))
        width = 0.2
        
        for i, metric in enumerate(metrics):
            plt.bar(x + i*width, model_comparison[metric], width, label=metric)
        
        plt.xlabel('Model')
        plt.ylabel('Score')
        plt.title(f'Model Comparison on {dataset}')
        plt.xticks(x + width * 1.5, model_comparison['model'])
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.savefig(os.path.join(save_dir, f'{dataset}_model_comparison.png'))
        plt.close()
    
    print(f"可视化结果已保存到 {save_dir}")

def main():
    """主函数"""
    # 创建结果目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(CONFIG['results_dir'], f"hyperparameter_test_{timestamp}")
    ensure_dir(results_dir)
    
    # 保存配置
    with open(os.path.join(results_dir, 'config.json'), 'w') as f:
        json.dump(CONFIG, f, indent=4)
    
    # 生成超参数组合
    hyperparameter_combinations = generate_hyperparameter_combinations()
    print(f"共生成 {len(hyperparameter_combinations)} 种超参数组合")
    
    # 运行实验
    results = []
    
    # 计算总实验数量
    total_experiments = len(CONFIG['datasets']) * len(CONFIG['models']) * len(hyperparameter_combinations)
    print(f"即将运行 {total_experiments} 个实验...")
    
    # 使用tqdm显示进度
    progress_bar = tqdm(total=total_experiments, desc="实验进度")
    
    for dataset_path in CONFIG['datasets']:
        for model_name in CONFIG['models']:
            for hyperparams in hyperparameter_combinations:
                # 运行实验
                result = run_experiment(model_name, dataset_path, hyperparams)
                results.append(result)
                
                # 保存中间结果
                with open(os.path.join(results_dir, 'intermediate_results.json'), 'w') as f:
                    json.dump(results, f, indent=4)
                
                # 更新进度条
                progress_bar.update(1)
    
    progress_bar.close()
    
    # 保存最终结果
    with open(os.path.join(results_dir, 'final_results.json'), 'w') as f:
        json.dump(results, f, indent=4)
    
    # 可视化结果
    if CONFIG['save_plots']:
        visualize_results(results, results_dir)
    
    print(f"实验完成，结果已保存到 {results_dir}")

if __name__ == "__main__":
    main() 