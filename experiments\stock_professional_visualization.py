#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional Stock Data Visualization for HTA-AD
基于benchmark实验的专业可视化风格
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import precision_recall_curve
import warnings
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# Ensure TSB_AD module can be imported
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD

warnings.filterwarnings('ignore')

# Professional plotting parameters
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 14
plt.rcParams['figure.dpi'] = 300

def load_stock_data():
    """加载并预处理Stock金融数据"""
    print("🔍 Loading Stock Financial Data...")
    
    # 使用Stock数据
    stock_data_path = "../Datasets/TSB-AD-U/TSB-AD-U/151_Stock_id_3_Finance_tr_500_1st_62.csv"
    
    try:
        df = pd.read_csv(stock_data_path)
        data = df['Data'].values.astype(np.float32)
        labels = df['Label'].values.astype(int)
        
        print(f"  ✅ 数据加载成功: {len(data)} points")
        print(f"  📊 异常点: {labels.sum()} ({labels.sum()/len(labels):.1%})")
        print(f"  💰 价格范围: ${data.min():.2f} - ${data.max():.2f}")
        print(f"  📈 波动率: {np.std(data):.2f}")
        
        # 找到异常点位置
        anomaly_indices = np.where(labels == 1)[0]
        print(f"  🚨 异常开始位置: {anomaly_indices[0]} (训练后{anomaly_indices[0]-500}步)")
        
        return data, labels, 500  # train_size = 500
        
    except Exception as e:
        print(f"❌ 加载Stock数据失败: {e}")
        return None, None, None

def professional_stock_visualization():
    """创建专业的Stock数据可视化 - 参考benchmark风格"""
    
    # 加载数据
    data, labels, train_size = load_stock_data()
    if data is None:
        return
    
    print("\n🔄 Initializing Professional HTA-AD Analysis...")
    
    # 使用更优的超参数（参考benchmark调优结果）
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    
    # HTA_AD需要HP字典参数 - 使用经过验证的默认配置
    HP = {
        'gpu': 0,
        'window_size': 100,
        'epochs': 15,
        'lr': 1e-3,
        'batch_size': 64,
        'latent_dim': 32,  # 使用默认latent维度
        'tcn_channels': [32, 32, 32],  # 使用默认TCN配置
        'cnn_channels': 16,  # 使用默认CNN通道
        'downsample_stride': 2,
        'ablation_mode': None
    }
    
    model = HTA_AD(HP=HP, normalize=True)
    model.device = device
    
    # 准备训练和测试数据
    window_size = HP['window_size']
    
    # 训练数据（前500个点）
    train_data = data[:train_size].reshape(-1, 1)
    
    # 测试数据（从500开始）
    test_data = data[train_size:].reshape(-1, 1)
    
    print(f"  📏 训练数据: {train_data.shape}")
    print(f"  📏 测试数据: {test_data.shape}")
    
    # 训练模型
    print("\n🏋️ Training HTA-AD Model...")
    model.fit(train_data)
    print("  ✅ 训练完成")
    
    # 测试阶段
    print("\n🧪 Testing Phase...")
    # 获取整个数据集的异常分数
    all_scores = model.decision_function(data.reshape(-1, 1))
    
    # 提取测试部分的分数
    test_scores = all_scores[train_size:]
    
    print(f"  📊 测试分数: {len(test_scores)}")
    print(f"  📈 平均异常分数: {test_scores.mean():.6f}")
    print(f"  📈 异常分数范围: [{test_scores.min():.6f}, {test_scores.max():.6f}]")
    
    # 创建专业可视化
    create_professional_plots(data, labels, test_scores, train_size, window_size)

def create_professional_plots(data, labels, errors, train_size, window_size):
    """创建专业的多面板可视化"""
    
    print("\n🎨 Creating Professional Visualizations...")
    
    # === 图1: 完整时间序列 + 异常检测结果 ===
    fig1 = create_detection_overview(data, labels, errors, train_size, window_size)
    
    # === 图2: 重构误差分析 ===
    fig2 = create_reconstruction_analysis(data, labels, errors, train_size, window_size)
    
    # === 图3: 异常得分分布 ===
    fig3 = create_score_distribution(errors, labels[train_size:])
    
    print("  ✅ 所有可视化创建完成!")
    print(f"  📁 文件已保存:")
    print(f"    📊 stock_detection_overview.png")
    print(f"    📊 stock_reconstruction_analysis.png") 
    print(f"    📊 stock_score_distribution.png")

def create_detection_overview(data, labels, errors, train_size, window_size):
    """创建检测概览图"""
    
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(18, 12), sharex=True, 
                                       gridspec_kw={'height_ratios': [3, 2, 1]})
    
    time_steps = np.arange(len(data))
    
    # === 子图1: 股票价格时间序列 ===
    ax1.plot(time_steps, data, color='#1f77b4', linewidth=1.5, label='Stock Price', alpha=0.8)
    
    # 标记异常点
    anomaly_indices = np.where(labels == 1)[0]
    if len(anomaly_indices) > 0:
        ax1.scatter(anomaly_indices, data[anomaly_indices], 
                   color='red', marker='o', s=20, zorder=5, 
                   label=f'True Anomalies ({len(anomaly_indices)})', alpha=0.8)
    
    # 训练/测试分割线
    ax1.axvline(x=train_size, color='green', linestyle='--', linewidth=2, 
               label='Train/Test Split', alpha=0.7)
    
    ax1.set_title('📈 Stock Price Time Series with Anomaly Detection', fontsize=16, fontweight='bold')
    ax1.set_ylabel('Stock Price ($)', fontsize=14)
    ax1.legend(loc='upper left', fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # === 子图2: 异常分数 ===
    error_time = np.arange(train_size, train_size + len(errors))
    ax2.plot(error_time, errors, color='#ff7f0e', linewidth=2, label='Anomaly Score')
    
    # 计算阈值（基于精确率-召回率曲线）
    test_labels = labels[train_size:train_size+len(errors)]
    if test_labels.sum() > 0:
        precision, recall, thresholds = precision_recall_curve(test_labels, errors)
        f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
        best_threshold = thresholds[np.argmax(f1_scores)]
        
        ax2.axhline(y=best_threshold, color='red', linestyle=':', linewidth=2, 
                   label=f'Optimal Threshold = {best_threshold:.4f}', alpha=0.8)
        
        # 填充超过阈值的区域
        ax2.fill_between(error_time, errors, best_threshold, 
                        where=errors >= best_threshold, 
                        color='red', alpha=0.2, interpolate=True, 
                        label='Detected Anomalies')
    
    ax2.set_title('🔍 HTA-AD Anomaly Score Analysis', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Anomaly Score', fontsize=12)
    ax2.legend(loc='upper left', fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # === 子图3: 真实标签 ===
    ax3.fill_between(time_steps, 0, labels, step='mid', color='red', alpha=0.6, 
                     label='Ground Truth Anomalies')
    ax3.set_title('🎯 Ground Truth Anomaly Labels', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Time Steps', fontsize=14)
    ax3.set_ylabel('Anomaly', fontsize=12)
    ax3.set_ylim(-0.1, 1.1)
    ax3.legend(loc='upper left', fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('stock_detection_overview.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_reconstruction_analysis(data, labels, errors, train_size, window_size):
    """创建重构分析图"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 10))
    
    # === 子图1: 正常vs异常误差分布 ===
    test_labels = labels[train_size:train_size+len(errors)]
    normal_errors = errors[test_labels == 0]
    anomaly_errors = errors[test_labels == 1]
    
    if len(normal_errors) > 0:
        ax1.hist(normal_errors, bins=30, alpha=0.7, color='blue', 
                label=f'Normal ({len(normal_errors)})', density=True)
    if len(anomaly_errors) > 0:
        ax1.hist(anomaly_errors, bins=30, alpha=0.7, color='red', 
                label=f'Anomaly ({len(anomaly_errors)})', density=True)
    
    ax1.set_title('📊 Score Distribution: Normal vs Anomaly', fontweight='bold')
    ax1.set_xlabel('Anomaly Score')
    ax1.set_ylabel('Density')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # === 子图2: 误差时序图（放大视图）===
    window_start = max(0, train_size - 100)
    window_end = min(len(data), train_size + 300)
    zoom_time = np.arange(window_start, window_end)
    zoom_data = data[window_start:window_end]
    zoom_labels = labels[window_start:window_end]
    
    ax2.plot(zoom_time, zoom_data, color='#1f77b4', linewidth=2, label='Stock Price')
    
    # 标记异常
    zoom_anomalies = np.where(zoom_labels == 1)[0] + window_start
    if len(zoom_anomalies) > 0:
        ax2.scatter(zoom_anomalies, data[zoom_anomalies], 
                   color='red', marker='o', s=30, zorder=5, label='Anomalies')
    
    ax2.axvline(x=train_size, color='green', linestyle='--', linewidth=2, alpha=0.7)
    ax2.set_title('🔍 Zoomed View: Around Train/Test Split', fontweight='bold')
    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Stock Price ($)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # === 子图3: 误差vs时间关系 ===
    error_time = np.arange(train_size, train_size + len(errors))
    ax3.scatter(error_time, errors, c=test_labels, cmap='coolwarm', 
               alpha=0.6, s=15, edgecolor='black', linewidth=0.5)
    ax3.set_title('🎯 Score vs Time (Red=Anomaly)', fontweight='bold')
    ax3.set_xlabel('Time Steps')
    ax3.set_ylabel('Anomaly Score')
    ax3.grid(True, alpha=0.3)
    
    # === 子图4: 性能指标摘要 ===
    if test_labels.sum() > 0:
        from sklearn.metrics import roc_auc_score, average_precision_score, f1_score
        
        try:
            auc_roc = roc_auc_score(test_labels, errors)
            auc_pr = average_precision_score(test_labels, errors)
            
            # 计算最佳F1分数
            precision, recall, thresholds = precision_recall_curve(test_labels, errors)
            f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
            best_f1 = np.max(f1_scores)
            
            # 显示指标
            metrics_text = f"""
📊 Performance Metrics:
• AUC-ROC: {auc_roc:.3f}
• AUC-PR: {auc_pr:.3f}  
• Best F1: {best_f1:.3f}
• Normal Samples: {len(normal_errors)}
• Anomaly Samples: {len(anomaly_errors)}
• Error Ratio: {len(anomaly_errors)/len(normal_errors) if len(normal_errors) > 0 else 'N/A':.2f}x
            """.strip()
            
            ax4.text(0.05, 0.95, metrics_text, transform=ax4.transAxes, 
                    fontsize=11, verticalalignment='top', 
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
            ax4.set_title('📈 Performance Summary', fontweight='bold')
            ax4.axis('off')
            
        except Exception as e:
            ax4.text(0.5, 0.5, f'计算指标时出错:\n{str(e)}', 
                    transform=ax4.transAxes, ha='center', va='center')
            ax4.set_title('⚠️ Metrics Error', fontweight='bold')
            ax4.axis('off')
    else:
        ax4.text(0.5, 0.5, '测试集中无异常点\n无法计算性能指标', 
                transform=ax4.transAxes, ha='center', va='center')
        ax4.set_title('⚠️ No Anomalies in Test Set', fontweight='bold')
        ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('stock_reconstruction_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_score_distribution(errors, test_labels):
    """创建得分分布图"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # === 子图1: 累积分布函数 ===
    normal_errors = errors[test_labels == 0] if test_labels.sum() < len(test_labels) else []
    anomaly_errors = errors[test_labels == 1] if test_labels.sum() > 0 else []
    
    if len(normal_errors) > 0:
        sorted_normal = np.sort(normal_errors)
        y_normal = np.arange(1, len(sorted_normal) + 1) / len(sorted_normal)
        ax1.plot(sorted_normal, y_normal, label='Normal', color='blue', linewidth=2)
    
    if len(anomaly_errors) > 0:
        sorted_anomaly = np.sort(anomaly_errors)
        y_anomaly = np.arange(1, len(sorted_anomaly) + 1) / len(sorted_anomaly)
        ax1.plot(sorted_anomaly, y_anomaly, label='Anomaly', color='red', linewidth=2)
    
    ax1.set_title('📈 Cumulative Distribution Function', fontweight='bold')
    ax1.set_xlabel('Anomaly Score')
    ax1.set_ylabel('Cumulative Probability')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # === 子图2: 箱线图比较 ===
    if len(normal_errors) > 0 and len(anomaly_errors) > 0:
        box_data = [normal_errors, anomaly_errors]
        box_labels = ['Normal', 'Anomaly']
        
        bp = ax2.boxplot(box_data, labels=box_labels, patch_artist=True)
        bp['boxes'][0].set_facecolor('lightblue')
        bp['boxes'][1].set_facecolor('lightcoral')
        
        # 添加统计信息
        stats_text = f"""
Normal: μ={np.mean(normal_errors):.4f}, σ={np.std(normal_errors):.4f}
Anomaly: μ={np.mean(anomaly_errors):.4f}, σ={np.std(anomaly_errors):.4f}
Separation: {np.mean(anomaly_errors)/np.mean(normal_errors):.2f}x
        """.strip()
        
        ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    ax2.set_title('📊 Score Distribution Comparison', fontweight='bold')
    ax2.set_xlabel('Sample Type')
    ax2.set_ylabel('Anomaly Score')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('stock_score_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

if __name__ == "__main__":
    print("="*80)
    print("🚀 Professional Stock Data Analysis with HTA-AD")
    print("="*80)
    
    professional_stock_visualization()
    
    print("\n" + "="*80)
    print("✅ Professional Analysis Complete!")
    print("📁 Check the generated PNG files for detailed visualizations")
    print("="*80) 