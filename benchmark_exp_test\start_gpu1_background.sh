#!/bin/bash
# LERN v2.3 GPU1后台测试启动脚本

echo "🚀 LERN v2.3 GPU1后台测试启动器"
echo "=================================="

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法:"
    echo "  $0 small   # 小规模测试 (前50个数据集)"
    echo "  $0 full    # 完整测试 (全部870个数据集)"
    echo ""
    read -p "请选择模式 [small/full]: " mode
else
    mode=$1
fi

# 设置输出文件
timestamp=$(date +"%Y%m%d_%H%M%S")
if [ "$mode" = "small" ]; then
    log_file="lern_gpu1_small_${timestamp}.log"
    result_dir="lern_gpu1_small_results"
elif [ "$mode" = "full" ]; then
    log_file="lern_gpu1_full_${timestamp}.log"
    result_dir="lern_gpu1_full_results"
else
    echo "❌ 无效模式: $mode"
    exit 1
fi

echo "📊 测试模式: $mode"
echo "📁 结果目录: $result_dir"
echo "📄 日志文件: $log_file"
echo "⏰ 开始时间: $(date)"

# 确认执行
read -p "确认开始测试? [y/N]: " confirm
if [[ ! "$confirm" =~ ^[Yy]([Ee][Ss])?$ ]]; then
    echo "❌ 已取消"
    exit 0
fi

echo ""
echo "🏃‍♂️ 在后台启动测试..."
echo "💡 使用以下命令监控进度:"
echo "   tail -f $log_file"
echo "   watch -n 5 'ls -la $result_dir/'"

# 在后台运行
nohup python run_lern_gpu1_background.py "$mode" > "$log_file" 2>&1 &
bg_pid=$!

echo "✅ 后台进程已启动!"
echo "🆔 进程ID: $bg_pid"
echo "📄 日志文件: $log_file"
echo ""
echo "🔍 常用监控命令:"
echo "   # 查看实时日志"
echo "   tail -f $log_file"
echo ""
echo "   # 查看进程状态"
echo "   ps aux | grep $bg_pid"
echo ""
echo "   # 终止进程 (如需要)"
echo "   kill $bg_pid"
echo ""
echo "   # 查看GPU使用情况"
echo "   nvidia-smi"

# 保存进程信息
echo "$bg_pid" > "lern_gpu1_${mode}_pid.txt"
echo "💾 进程ID已保存到: lern_gpu1_${mode}_pid.txt"

echo ""
echo "🎯 测试已在后台开始运行!" 