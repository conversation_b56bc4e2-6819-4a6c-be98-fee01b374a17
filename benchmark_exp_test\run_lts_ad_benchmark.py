# -*- coding: utf-8 -*-
# Benchmark script for Lightweight Time Series Anomaly Detector (LTS_AD)
# This script iterates through all datasets, runs the detector, and reports average performance.

import os
import pandas as pd
import numpy as np
import time
import sys

# --- Path Setup ---
# Ensure the project root is in the Python path for robust imports
try:
    from benchmark_exp.Lightweight_TS_Model import LTS_AD, create_visualizations
    from TSB_AD.evaluation.metrics import get_metrics
    from TSB_AD.utils.slidingWindows import find_length_rank
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    print(f"✅ Added project root to path: {project_root}")
    from benchmark_exp.Lightweight_TS_Model import LTS_AD, create_visualizations
    from TSB_AD.evaluation.metrics import get_metrics
    from TSB_AD.utils.slidingWindows import find_length_rank

def run_benchmark():
    """
    Runs the LTS_AD model on all datasets in the specified directory
    and reports the average performance.
    """
    data_directory = './Datasets/TSB-AD-U/'
    results_dir = './lts_ad_results/'
    os.makedirs(results_dir, exist_ok=True)
    
    # --- Hyperparameters for LTS_AD ---
    LTS_AD_HP = {
        'window_size': 100,
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 128,
        'latent_dim': 16,
    }

    all_results = []
    
    # Get a sorted list of dataset files
    try:
        dataset_files = sorted([f for f in os.listdir(data_directory) if f.endswith('.csv') and not f.startswith('.')])
    except FileNotFoundError:
        print(f"❌ 错误: 数据目录未找到 at '{data_directory}'")
        return

    print(f"🚀 发现 {len(dataset_files)} 个数据集. 开始批量测试 LTS_AD 模型...")
    
    for i, filename in enumerate(dataset_files):
        print(f"\n[{i+1}/{len(dataset_files)}] 正在处理: {filename}")
        
        try:
            filepath = os.path.join(data_directory, filename)
            df = pd.read_csv(filepath).dropna()
            
            # Ensure data has at least two columns
            if df.shape[1] < 2:
                print("   ⚠️ 文件中的列数少于2，跳过...")
                continue

            data = df.iloc[:, 0:-1].values.astype(float)
            label = df.iloc[:, -1].astype(int).to_numpy()

            # Parse training size from filename (e.g., *_tr_1007_*)
            train_index_str = filename.split('.')[0].split('_')[-3]
            train_size = int(train_index_str)
            
            if train_size >= len(data):
                print(f"   ⚠️ 训练集大小 ({train_size}) 大于或等于总数据长度 ({len(data)})，跳过...")
                continue
                
            data_train = data[:train_size]
            
            # --- Run Detector ---
            start_time = time.time()
            clf = LTS_AD(HP=LTS_AD_HP)
            clf.fit(data_train)
            scores = clf.decision_function(data)
            runtime = time.time() - start_time
            
            # --- Evaluate ---
            # Ensure scores and labels have the same length
            min_len = min(len(scores), len(label))
            scores, label = scores[:min_len], label[:min_len]
            
            slidingWindow = find_length_rank(data[:len(label)].flatten().reshape(-1, 1), rank=1)
            metrics = get_metrics(scores, label, slidingWindow=slidingWindow)
            
            # --- Visualize ---
            create_visualizations(
                filename=filename,
                data=data[:len(label)],
                label=label,
                output=scores,
                train_size=train_size,
                save_dir=results_dir,
                model_name="LTS_AD"
            )

            result_row = {
                'filename': filename,
                'runtime(s)': round(runtime, 2),
                **metrics
            }
            all_results.append(result_row)
            print(f"   ✅ 完成, F1-Score: {metrics.get('Standard-F1', 'N/A'):.4f}, Runtime: {runtime:.2f}s")

        except Exception as e:
            print(f"   ❌ 处理文件时发生错误: {e}")
            import traceback
            traceback.print_exc()
            all_results.append({'filename': filename, 'runtime(s)': 0, 'error': str(e)})

    # --- Report Results ---
    if not all_results:
        print("\n没有数据集被成功处理。")
        return
        
    results_df = pd.DataFrame(all_results)
    
    # Calculate average metrics
    numeric_cols = results_df.select_dtypes(include=np.number).columns
    mean_metrics = results_df[numeric_cols].mean().to_frame().T
    mean_metrics.index = ['Average']
    
    print("\n\n--- 벤치마크 결과 요약 ---")
    print("各数据集详细性能:")
    print(results_df.to_string())
    
    print("\n平均性能指标:")
    print(mean_metrics.to_string())
    
    # Save results to CSV
    detailed_results_path = os.path.join(results_dir, 'lts_ad_benchmark_detailed.csv')
    summary_path = os.path.join(results_dir, 'lts_ad_benchmark_summary.csv')
    
    results_df.to_csv(detailed_results_path, index=False)
    mean_metrics.to_csv(summary_path, index=True)
    
    print(f"\n💾 详细结果已保存到: {detailed_results_path}")
    print(f"💾 总结报告已保存到: {summary_path}")
    print("\n🎉 批量测试完成!")

if __name__ == '__main__':
    run_benchmark() 