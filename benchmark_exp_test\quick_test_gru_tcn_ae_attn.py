# -*- coding: utf-8 -*-
# Quick test script for the ATTENTION-ENHANCED GRU-TCN-AE model
import os
import sys
import pandas as pd
import numpy as np
import torch
from sklearn.metrics import f1_score, precision_score, recall_score, precision_recall_curve

# --- Environment Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Model & Utils Import ---
# Make sure to import the modified model
from benchmark_exp.gru_tcn_ae import GruTcnAe_AD

# --- Standalone Best F1 Score Finder ---
def find_best_f1_threshold(scores, labels, n_thresholds=100):
    """
    Calculates the best F1 score by searching over a range of thresholds.
    This logic is adapted from TSB_AD's internal metrics.
    """
    if np.all(labels == 0):
        return 0.0, 0.0, [] # No anomalies, F1 is 0.
        
    precision, recall, thresholds = precision_recall_curve(labels, scores)
    
    # handle cases where precision or recall are 0 to avoid division by zero
    f1_scores = np.divide(2 * recall * precision, recall + precision, 
                          out=np.zeros_like(recall), where=(recall + precision) != 0)
    
    # The last threshold is not included by precision_recall_curve, add it
    # thresholds = np.append(thresholds, 1.0) 
    
    best_f1_idx = np.argmax(f1_scores)
    best_f1 = f1_scores[best_f1_idx]
    best_threshold = thresholds[best_f1_idx]
    
    return best_f1, best_threshold, f1_scores

def load_dataset(file_path):
    """Loads a single multivariate dataset from the TSB-AD-M benchmark."""
    df = pd.read_csv(file_path)
    if 'Unnamed: 0' in df.columns:
        df = df.drop(columns=['Unnamed: 0'])
    labels = df.iloc[:, -1].values
    features = df.iloc[:, :-1].values
    try:
        train_len = int(os.path.basename(file_path).split('_tr_')[1].split('_')[0])
    except (IndexError, ValueError):
        train_len = int(len(features) * 0.7)
    return features[:train_len], labels[:train_len], features[train_len:], labels[train_len:], os.path.basename(file_path)

def run_test():
    """Main function to run the quick test for the new model."""
    print("--- 🚀 Starting Quick Test for Attention-Enhanced GRU-TCN-AE ---")
    
    # --- Configuration ---
    GPU_ID = 0
    DATASET_FILE = os.path.join(project_root, "Datasets/TSB-AD-M/001_Genesis_id_1_Sensor_tr_4055_1st_15538.csv")
    
    # Model Hyperparameters
    HP = {
        'gpu': GPU_ID,
        'window_size': 128,
        'epochs': 30,
        'lr': 1e-4,
        'batch_size': 64,
        'latent_dim': 32,
        'gru_hidden_dim': 64,
        'tcn_layers': 4,
        'tcn_filters': 64,
        'n_heads': 4,
        'lambda_': 0.7 # Giving more weight to the association loss
    }
    
    # --- Data Loading ---
    if not os.path.exists(DATASET_FILE):
        print(f"❌ ERROR: Dataset file not found at {DATASET_FILE}")
        return
        
    X_train, y_train, X_test, y_test, dataset_name = load_dataset(DATASET_FILE)
    print(f"\n🔬 Loaded dataset: {dataset_name} | Train: {X_train.shape} | Test: {X_test.shape}")
    
    # --- Model Training & Evaluation ---
    try:
        model = GruTcnAe_AD(HP=HP)
        print("\n💪 Training model with hyperparameters:")
        print(HP)
        model.fit(X_train)
        
        print("\n🔍 Evaluating model on the test set...")
        anomaly_scores = model.decision_function(X_test)
        
        best_f1, best_th, _ = find_best_f1_threshold(anomaly_scores, y_test, n_thresholds=200)
        predictions = (anomaly_scores > best_th).astype(int)
        precision = precision_score(y_test, predictions)
        recall = recall_score(y_test, predictions)
        
        print("\n--- ✅ Test Complete ---")
        print(f"Dataset: {dataset_name}")
        print("Performance Metrics:")
        print(f"  - Best F1-Score: {best_f1:.4f}")
        print(f"  - Precision:     {precision:.4f}")
        print(f"  - Recall:        {recall:.4f}")
        print(f"  - @ Threshold:   {best_th:.6f}")

    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_test() 