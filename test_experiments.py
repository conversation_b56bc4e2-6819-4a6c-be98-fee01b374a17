#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的实验脚本
"""

import sys
import os

# 确保可以导入 TSB_AD 模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_data_loading():
    """测试数据加载功能"""
    print("🧪 测试数据加载功能...")
    
    try:
        # 测试重构可视化的数据加载
        sys.path.insert(0, os.path.join(current_dir, 'experiments'))
        from reconstruction_visualization import load_sample_data
        
        data, labels, train_size, dataset_name = load_sample_data()
        print(f"✅ 重构可视化数据加载成功: {data.shape}, 训练集: {train_size}, 数据集: {dataset_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hta_ad_model():
    """测试HTA-AD模型基本功能"""
    print("🧪 测试HTA-AD模型...")
    
    try:
        from TSB_AD.models.HTA_AD import HTA_AD
        from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict
        import numpy as np
        
        # 创建模拟数据
        np.random.seed(42)
        data = np.random.randn(500, 3)  # 500个时间点，3个特征
        
        # 创建模型
        hp = Optimal_Uni_algo_HP_dict.get('HTA_AD', {})
        hp['epochs'] = 5  # 减少训练时间
        hp['window_size'] = 32  # 减少窗口大小
        
        model = HTA_AD(HP=hp, normalize=True)
        
        # 训练模型
        model.fit(data[:300])  # 使用前300个点训练
        
        # 测试推理
        scores = model.decision_function(data)
        
        print(f"✅ HTA-AD模型测试成功: 输入{data.shape}, 异常分数{scores.shape}")
        return True
        
    except Exception as e:
        print(f"❌ HTA-AD模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_experiment():
    """测试简化的实验"""
    print("🧪 测试简化实验...")
    
    try:
        import numpy as np
        from TSB_AD.models.HTA_AD import HTA_AD
        from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict
        
        # 创建简单的测试数据
        np.random.seed(42)
        length = 200
        time = np.arange(length)
        
        # 生成带异常的信号
        signal = np.sin(time * 0.1) + 0.1 * np.random.randn(length)
        signal[80:90] += 2  # 注入异常
        signal[150:160] += 2  # 注入异常
        
        data = np.column_stack([signal, signal * 0.8])
        labels = np.zeros(length)
        labels[80:90] = 1
        labels[150:160] = 1
        
        # 创建和训练模型
        hp = {
            'window_size': 16,
            'epochs': 3,
            'lr': 1e-3,
            'batch_size': 8,
            'latent_dim': 8,
            'tcn_channels': [8, 8],
            'cnn_channels': 4
        }
        
        model = HTA_AD(HP=hp, normalize=True)
        model.fit(data[:120])  # 训练前120个点
        
        # 获取异常分数
        scores = model.decision_function(data)
        
        print(f"✅ 简化实验成功: 数据{data.shape}, 分数{scores.shape}")
        print(f"   异常点平均分数: {scores[labels==1].mean():.4f}")
        print(f"   正常点平均分数: {scores[labels==0].mean():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化实验失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试修复后的实验代码")
    print("=" * 60)
    
    tests = [
        ("数据加载", test_data_loading),
        ("HTA-AD模型", test_hta_ad_model), 
        ("简化实验", test_simple_experiment)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
        
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 所有测试通过！实验代码修复成功。")
        print("📋 现在可以运行以下实验:")
        print("   python experiments/reconstruction_visualization.py")
        print("   python experiments/latent_space_visualization.py") 
        print("   python experiments/shuffling_experiment.py")
        print("   python experiments/robustness_analysis.py")
        print("   python experiments/ablation_study.py")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息并修复。")