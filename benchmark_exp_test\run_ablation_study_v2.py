import pandas as pd
import numpy as np
import time
import os
import glob
import random
import matplotlib.pyplot as plt
import warnings
import argparse

# --- Suppress Warnings ---
warnings.filterwarnings("ignore")

# --- Model Imports ---
from TSB_AD.models.POLY import POLY
from TSB_AD.models.PCA import PCA as Sub_PCA
from benchmark_exp.Run_LERN_Detector import LERN_AD, create_visualizations
from TSB_AD.utils.slidingWindows import find_length_rank
from sklearn.metrics import roc_auc_score, average_precision_score, precision_score, recall_score, f1_score

# --- Constants ---
DATASET_PATH = 'Datasets/TSB-AD-U/*.csv'
DEFAULT_SAVE_DIR = './ablation_results'
NUM_DATASETS_TO_TEST = 5  # 减少测试的数据集数量以便快速调试

def run_single_test(model_name, model_class, data, label, train_size, name, gpu_id=0):
    """
    Runs a single model on a single dataset.
    """
    save_dir = os.path.join(DEFAULT_SAVE_DIR, model_name)
    os.makedirs(save_dir, exist_ok=True)
    
    print(f"\n{'='*20}\nRunning {model_name} on {name}\n{'='*20}")
    
    data_train = data[:train_size]

    # --- Hyperparameter & Model Init ---
    window_size = find_length_rank(data_train, rank=10)
    print(f"📊 Using window size: {window_size}")
    
    start_time = time.time()
    try:
        if model_name.startswith("LERN"):
            HP = {
                'window_size': window_size, 'patience': 10, 'num_epochs': 50, 'lr': 1e-3,
                'batch_size': 32, 'gpu': gpu_id, 'llm_name': 'sentence-transformers/all-MiniLM-L6-v2',
                'save_path': os.path.join(save_dir, f'model_{name}.pt'),
                'train_anomaly_ratio': None, 'mask_ratio': 0.6
            }
            clf = model_class(HP=HP)
        elif model_name == "POLY":
            clf = model_class(window=window_size, power=3)
        elif model_name == "Sub-PCA":
            clf = model_class(slidingWindow=window_size, sub=True)
        else:
            raise ValueError(f"Unknown model: {model_name}")

        # --- Fit and Predict ---
        clf.fit(data_train)
        training_history = getattr(clf, 'training_history', None)
        scores = clf.decision_function(data)
    
    except Exception as e:
        print(f"❌ ERROR during model execution: {e}")
        import traceback
        traceback.print_exc()
        return None

    end_time = time.time()

    # --- Evaluate & Visualize ---
    test_scores = scores[train_size:]
    test_labels = label[train_size:]
    
    # Handle NaN/Inf in scores
    if np.any(np.isnan(test_scores)) or np.any(np.isinf(test_scores)):
        print("⚠️ NaN or Inf found in scores, replacing with 0.")
        test_scores = np.nan_to_num(test_scores)

    threshold = np.mean(test_scores) + 2 * np.std(test_scores)
    test_predictions = (test_scores > threshold).astype(int)
    
    metrics = {
        'AUC-ROC': roc_auc_score(test_labels, test_scores),
        'AUC-PR': average_precision_score(test_labels, test_scores),
        'Precision': precision_score(test_labels, test_predictions),
        'Recall': recall_score(test_labels, test_predictions),
        'F1': f1_score(test_labels, test_predictions),
        'runtime_sec': end_time - start_time
    }
    
    print(f"✅ Finished in {metrics['runtime_sec']:.2f}s")
    for k, v in metrics.items():
        if isinstance(v, float): print(f"   {k}: {v:.4f}")
    
    create_visualizations(
        filename=name, data=data.flatten(), label=label, output=scores,
        train_size=train_size, training_history=training_history,
        save_dir=save_dir, model_name=model_name
    )
    
    return metrics

def main():
    """Main function to run the ablation study."""
    parser = argparse.ArgumentParser(description="Ablation Study Runner v2")
    parser.add_argument('--gpu', type=int, default=0, help='GPU ID to use for LERN model')
    args = parser.parse_args()

    print(f"🚀 Starting Ablation Study Runner v2 (using GPU: {args.gpu})")
    
    # --- Models to Test ---
    models_to_test = {
        "LERN_v2.3_MAE": LERN_AD,
        "POLY": POLY,
        "Sub-PCA": Sub_PCA
    }
    
    all_datasets = glob.glob(DATASET_PATH)
    selected_datasets = random.sample(all_datasets, min(NUM_DATASETS_TO_TEST, len(all_datasets)))
    
    print(f"📂 Will run on {len(selected_datasets)} randomly selected datasets.")
    
    # --- Run Experiments ---
    results = []
    for dataset_path in selected_datasets:
        name = os.path.basename(dataset_path)
        try:
            df = pd.read_csv(dataset_path)
            data = df['Data'].values.astype(float).reshape(-1, 1)
            label = df['Label'].values.astype(int)
            train_size = int(len(data) * 0.15)
        except Exception as e:
            print(f"❌ ERROR loading data {name}: {e}")
            continue

        for model_name, model_class in models_to_test.items():
            metrics = run_single_test(model_name, model_class, data, label, train_size, name, gpu_id=args.gpu)
            if metrics:
                metrics['model'] = model_name
                metrics['dataset'] = name
                results.append(metrics)
    
    # --- Summarize Results ---
    if results:
        results_df = pd.DataFrame(results)
        summary_path = os.path.join(DEFAULT_SAVE_DIR, 'ablation_summary.csv')
        results_df.to_csv(summary_path, index=False)
        print(f"\n✅ Ablation study complete. Summary saved to {summary_path}")
        print(results_df)

if __name__ == '__main__':
    main() 